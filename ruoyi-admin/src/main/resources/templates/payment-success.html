<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .success-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #52c41a;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: bounce 0.6s ease-in-out;
        }
        
        .success-icon::after {
            content: '✓';
            color: white;
            font-size: 40px;
            font-weight: bold;
        }
        
        .success-title {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .success-message {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .order-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .order-info h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .order-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .order-detail:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .order-label {
            color: #666;
            font-weight: 500;
        }
        
        .order-value {
            color: #333;
            font-weight: 600;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #d9d9d9;
            transform: translateY(-2px);
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon"></div>
        <h1 class="success-title">支付成功！</h1>
        <p class="success-message">恭喜您，订单支付已完成，我们将尽快为您处理订单。</p>
        
        <div class="order-info">
            <h3>订单信息</h3>
            <div class="order-detail">
                <span class="order-label">订单号：</span>
                <span class="order-value" th:text="${orderNo}">-</span>
            </div>
            <div class="order-detail">
                <span class="order-label">支付金额：</span>
                <span class="order-value" th:text="${totalAmount}">-</span>
            </div>
            <div class="order-detail">
                <span class="order-label">支付时间：</span>
                <span class="order-value" th:text="${payTime}">-</span>
            </div>
            <div class="order-detail">
                <span class="order-label">支付方式：</span>
                <span class="order-value">支付宝</span>
            </div>
            <div class="order-detail" th:if="${tradeNo != '-'}">
                <span class="order-label">交易号：</span>
                <span class="order-value" th:text="${tradeNo}">-</span>
            </div>
        </div>
        
        <div class="btn-group">
            <a href="javascript:void(0)" class="btn btn-primary" onclick="goHome()">返回首页</a>
            <a href="javascript:void(0)" class="btn btn-secondary" onclick="viewOrder()">查看订单</a>
        </div>
    </div>

    <script th:inline="javascript">
        // 获取订单号
        var orderNo = /*[[${orderNo}]]*/ '';
        
        // 返回首页
        function goHome() {
            window.location.href = '/';
        }
        
        // 查看订单
        function viewOrder() {
            if (orderNo && orderNo !== '-') {
                // 这里可以跳转到订单详情页面
                alert('订单号：' + orderNo + '\n功能开发中...');
            } else {
                alert('订单信息获取失败');
            }
        }
    </script>
</body>
</html>
