<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .success-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #52c41a;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: bounce 0.6s ease-in-out;
        }
        
        .success-icon::after {
            content: '✓';
            color: white;
            font-size: 40px;
            font-weight: bold;
        }
        
        .success-title {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .success-message {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .order-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .order-info h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .order-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .order-detail:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .order-label {
            color: #666;
            font-weight: 500;
        }
        
        .order-value {
            color: #333;
            font-weight: 600;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #d9d9d9;
            transform: translateY(-2px);
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .loading {
            display: none;
            color: #666;
            font-size: 14px;
            margin-top: 20px;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon"></div>
        <h1 class="success-title">支付成功！</h1>
        <p class="success-message">恭喜您，订单支付已完成，我们将尽快为您处理订单。</p>
        
        <div class="order-info">
            <h3>订单信息</h3>
            <div class="order-detail">
                <span class="order-label">订单号：</span>
                <span class="order-value" id="orderNo">-</span>
            </div>
            <div class="order-detail">
                <span class="order-label">支付金额：</span>
                <span class="order-value" id="totalAmount">-</span>
            </div>
            <div class="order-detail">
                <span class="order-label">支付时间：</span>
                <span class="order-value" id="payTime">-</span>
            </div>
            <div class="order-detail">
                <span class="order-label">支付方式：</span>
                <span class="order-value">支付宝</span>
            </div>
        </div>
        
        <div class="btn-group">
            <a href="javascript:void(0)" class="btn btn-primary" onclick="goHome()">返回首页</a>
            <a href="javascript:void(0)" class="btn btn-secondary" onclick="viewOrder()">查看订单</a>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            正在获取订单详情...
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                out_trade_no: params.get('out_trade_no'),
                total_amount: params.get('total_amount'),
                trade_no: params.get('trade_no'),
                timestamp: params.get('timestamp')
            };
        }
        
        // 初始化页面
        function initPage() {
            const params = getUrlParams();
            
            // 显示订单信息
            if (params.out_trade_no) {
                document.getElementById('orderNo').textContent = params.out_trade_no;
            }
            
            if (params.total_amount) {
                document.getElementById('totalAmount').textContent = '¥' + params.total_amount;
            }
            
            // 显示支付时间
            document.getElementById('payTime').textContent = new Date().toLocaleString('zh-CN');
            
            // 如果有订单号，查询订单详情
            if (params.out_trade_no) {
                queryOrderStatus(params.out_trade_no);
            }
        }
        
        // 查询订单状态
        function queryOrderStatus(orderNo) {
            document.getElementById('loading').style.display = 'block';
            
            fetch(`/api/query/${orderNo}/ALIPAY`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    
                    if (data.code === '0000' && data.data) {
                        // 更新订单信息
                        if (data.data.totalAmount) {
                            document.getElementById('totalAmount').textContent = '¥' + data.data.totalAmount;
                        }
                        
                        console.log('订单查询成功:', data.data);
                    }
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    console.error('查询订单失败:', error);
                });
        }
        
        // 返回首页
        function goHome() {
            window.location.href = '/';
        }
        
        // 查看订单
        function viewOrder() {
            const params = getUrlParams();
            if (params.out_trade_no) {
                // 这里可以跳转到订单详情页面
                alert('订单号：' + params.out_trade_no + '\n功能开发中...');
            } else {
                alert('订单信息获取失败');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
