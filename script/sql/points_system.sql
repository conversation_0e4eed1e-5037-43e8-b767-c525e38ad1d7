-- ----------------------------
-- 积分购买系统相关表结构 (MySQL版本)
-- 适用于 RuoyiVuePlus 框架
-- ----------------------------

-- ----------------------------
-- 1、套餐表
-- ----------------------------
DROP TABLE IF EXISTS sys_package;
CREATE TABLE sys_package (
    id               bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
    tenant_id        varchar(20)     DEFAULT '000000'           COMMENT '租户编号',
    package_name     varchar(100)    NOT NULL                   COMMENT '套餐名称',
    package_desc     varchar(500)    DEFAULT ''                 COMMENT '套餐描述',
    points_amount    bigint(20)      NOT NULL                   COMMENT '积分数量',
    original_price   decimal(10,2)   NOT NULL                   COMMENT '原价',
    sale_price       decimal(10,2)   NOT NULL                   COMMENT '售价',
    discount_rate    decimal(5,2)    DEFAULT 0.00               COMMENT '折扣率(%)',
    bonus_points     bigint(20)      DEFAULT 0                  COMMENT '赠送积分',
    package_type     char(1)         DEFAULT '1'                COMMENT '套餐类型（1普通套餐 2限时优惠 3新用户专享）',
    is_hot          char(1)         DEFAULT '0'                COMMENT '是否热门（0否 1是）',
    sort_order      int(4)          DEFAULT 0                  COMMENT '显示顺序',
    start_time      datetime        DEFAULT NULL               COMMENT '生效时间',
    end_time        datetime        DEFAULT NULL               COMMENT '失效时间',
    status          char(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
    del_flag        char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
    create_dept     bigint(20)      DEFAULT NULL               COMMENT '创建部门',
    create_by       bigint(20)      DEFAULT NULL               COMMENT '创建者',
    create_time     datetime        DEFAULT NULL               COMMENT '创建时间',
    update_by       bigint(20)      DEFAULT NULL               COMMENT '更新者',
    update_time     datetime        DEFAULT NULL               COMMENT '更新时间',
    remark          varchar(500)    DEFAULT NULL               COMMENT '备注',
    PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT = '积分套餐表';

-- ----------------------------
-- 2、订单表
-- ----------------------------
DROP TABLE IF EXISTS sys_order;
CREATE TABLE sys_order (
    id               bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
    tenant_id        varchar(20)     DEFAULT '000000'           COMMENT '租户编号',
    order_no         varchar(64)     NOT NULL                   COMMENT '订单号',
    user_id          bigint(20)      NOT NULL                   COMMENT '用户ID',
    package_id       bigint(20)      NOT NULL                   COMMENT '套餐ID',
    package_name     varchar(100)    NOT NULL                   COMMENT '套餐名称',
    points_amount    bigint(20)      NOT NULL                   COMMENT '购买积分数量',
    bonus_points     bigint(20)      DEFAULT 0                  COMMENT '赠送积分数量',
    total_points     bigint(20)      NOT NULL                   COMMENT '总积分数量',
    original_price   decimal(10,2)   NOT NULL                   COMMENT '原价',
    pay_amount       decimal(10,2)   NOT NULL                   COMMENT '实付金额',
    discount_amount  decimal(10,2)   DEFAULT 0.00               COMMENT '优惠金额',
    payment_method   varchar(20)     DEFAULT NULL               COMMENT '支付方式（alipay支付宝 wechat微信 balance余额）',
    payment_no       varchar(64)     DEFAULT NULL               COMMENT '支付流水号',
    order_status     char(1)         DEFAULT '0'                COMMENT '订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）',
    pay_time         datetime        DEFAULT NULL               COMMENT '支付时间',
    cancel_time      datetime        DEFAULT NULL               COMMENT '取消时间',
    refund_time      datetime        DEFAULT NULL               COMMENT '退款时间',
    expire_time      datetime        DEFAULT NULL               COMMENT '订单过期时间',
    client_ip        varchar(128)    DEFAULT ''                 COMMENT '客户端IP',
    user_agent       varchar(500)    DEFAULT ''                 COMMENT '用户代理',
    del_flag         char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
    create_dept      bigint(20)      DEFAULT NULL               COMMENT '创建部门',
    create_by        bigint(20)      DEFAULT NULL               COMMENT '创建者',
    create_time      datetime        DEFAULT NULL               COMMENT '创建时间',
    update_by        bigint(20)      DEFAULT NULL               COMMENT '更新者',
    update_time      datetime        DEFAULT NULL               COMMENT '更新时间',
    remark           varchar(500)    DEFAULT NULL               COMMENT '备注',
    PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT = '积分订单表';

-- ----------------------------
-- 3、积分表
-- ----------------------------
DROP TABLE IF EXISTS sys_points;
CREATE TABLE sys_points (
    id               bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
    tenant_id        varchar(20)     DEFAULT '000000'           COMMENT '租户编号',
    user_id          bigint(20)      NOT NULL                   COMMENT '用户ID',
    change_type      char(1)         NOT NULL                   COMMENT '变动类型（1收入 2支出）',
    change_reason    varchar(20)     NOT NULL                   COMMENT '变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）',
    points_amount    bigint(20)      NOT NULL                   COMMENT '积分数量',
    balance_before   bigint(20)      NOT NULL                   COMMENT '变动前余额',
    balance_after    bigint(20)      NOT NULL                   COMMENT '变动后余额',
    related_id       bigint(20)      DEFAULT NULL               COMMENT '关联ID（订单ID、消费记录ID等）',
    related_type     varchar(20)     DEFAULT NULL               COMMENT '关联类型（order订单 consume消费 gift赠送）',
    expire_time      datetime        DEFAULT NULL               COMMENT '积分过期时间',
    description      varchar(200)    DEFAULT ''                 COMMENT '描述说明',
    del_flag         char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
    create_dept      bigint(20)      DEFAULT NULL               COMMENT '创建部门',
    create_by        bigint(20)      DEFAULT NULL               COMMENT '创建者',
    create_time      datetime        DEFAULT NULL               COMMENT '创建时间',
    update_by        bigint(20)      DEFAULT NULL               COMMENT '更新者',
    update_time      datetime        DEFAULT NULL               COMMENT '更新时间',
    remark           varchar(500)    DEFAULT NULL               COMMENT '备注',
    PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT = '积分变动记录表';

-- ----------------------------
-- 4、钱包信息表
-- ----------------------------
DROP TABLE IF EXISTS sys_wallet;
CREATE TABLE sys_wallet (
    id               bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
    tenant_id        varchar(20)     DEFAULT '000000'           COMMENT '租户编号',
    user_id          bigint(20)      NOT NULL                   COMMENT '用户ID',
    balance          decimal(10,2)   DEFAULT 0.00               COMMENT '余额',
    frozen_amount    decimal(10,2)   DEFAULT 0.00               COMMENT '冻结金额',
    total_recharge   decimal(10,2)   DEFAULT 0.00               COMMENT '累计充值',
    total_consume    decimal(10,2)   DEFAULT 0.00               COMMENT '累计消费',
    points_balance   bigint(20)      DEFAULT 0                  COMMENT '积分余额',
    total_points     bigint(20)      DEFAULT 0                  COMMENT '累计获得积分',
    used_points      bigint(20)      DEFAULT 0                  COMMENT '累计使用积分',
    pay_password     varchar(100)    DEFAULT NULL               COMMENT '支付密码',
    is_pay_pwd_set   char(1)         DEFAULT '0'                COMMENT '是否设置支付密码（0否 1是）',
    status           char(1)         DEFAULT '0'                COMMENT '钱包状态（0正常 1冻结 2注销）',
    last_trade_time  datetime        DEFAULT NULL               COMMENT '最后交易时间',
    version          int(11)         DEFAULT 0                  COMMENT '版本号（乐观锁）',
    del_flag         char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
    create_dept      bigint(20)      DEFAULT NULL               COMMENT '创建部门',
    create_by        bigint(20)      DEFAULT NULL               COMMENT '创建者',
    create_time      datetime        DEFAULT NULL               COMMENT '创建时间',
    update_by        bigint(20)      DEFAULT NULL               COMMENT '更新者',
    update_time      datetime        DEFAULT NULL               COMMENT '更新时间',
    remark           varchar(500)    DEFAULT NULL               COMMENT '备注',
    PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT = '用户钱包表';

-- ----------------------------
-- 5、钱包流水表
-- ----------------------------
DROP TABLE IF EXISTS sys_wallet_record;
CREATE TABLE sys_wallet_record (
    id               bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
    tenant_id        varchar(20)     DEFAULT '000000'           COMMENT '租户编号',
    user_id          bigint(20)      NOT NULL                   COMMENT '用户ID',
    wallet_id        bigint(20)      NOT NULL                   COMMENT '钱包ID',
    trade_no         varchar(64)     NOT NULL                   COMMENT '交易流水号',
    trade_type       char(1)         NOT NULL                   COMMENT '交易类型（1收入 2支出）',
    trade_reason     varchar(20)     NOT NULL                   COMMENT '交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）',
    amount           decimal(10,2)   NOT NULL                   COMMENT '交易金额',
    balance_before   decimal(10,2)   NOT NULL                   COMMENT '交易前余额',
    balance_after    decimal(10,2)   NOT NULL                   COMMENT '交易后余额',
    related_id       bigint(20)      DEFAULT NULL               COMMENT '关联ID',
    related_type     varchar(20)     DEFAULT NULL               COMMENT '关联类型',
    payment_method   varchar(20)     DEFAULT NULL               COMMENT '支付方式',
    payment_no       varchar(64)     DEFAULT NULL               COMMENT '支付流水号',
    description      varchar(200)    DEFAULT ''                 COMMENT '描述说明',
    client_ip        varchar(128)    DEFAULT ''                 COMMENT '客户端IP',
    del_flag         char(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
    create_dept      bigint(20)      DEFAULT NULL               COMMENT '创建部门',
    create_by        bigint(20)      DEFAULT NULL               COMMENT '创建者',
    create_time      datetime        DEFAULT NULL               COMMENT '创建时间',
    update_by        bigint(20)      DEFAULT NULL               COMMENT '更新者',
    update_time      datetime        DEFAULT NULL               COMMENT '更新时间',
    remark           varchar(500)    DEFAULT NULL               COMMENT '备注',
    PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT = '钱包流水记录表';

-- ----------------------------
-- 初始化套餐数据
-- ----------------------------
INSERT INTO sys_package (
    tenant_id, package_name, package_desc, points_amount, original_price, sale_price,
    discount_rate, bonus_points, package_type, is_hot, sort_order, status,
    del_flag, create_time, remark
) VALUES
(
    '000000',
    '萌芽启航包',
    '适合新手用户，包含10,000积分，可观看100个视频，单价3.66元/视频，预计曝光量50,000',
    10000,
    366.00,
    366.00,
    0.00,
    0,
    '3',
    '0',
    1,
    '0',
    '0',
    NOW(),
    '新用户专享套餐，基础播放量500/视频'
),
(
    '000000',
    '星光进阶包',
    '进阶用户首选，包含30,000积分，可观看300个视频，单价3.3元/视频，预计曝光量150,000',
    30000,
    990.00,
    990.00,
    0.00,
    0,
    '1',
    '1',
    2,
    '0',
    '0',
    NOW(),
    '热门套餐，基础播放量500/视频'
),
(
    '000000',
    '青铜提速包',
    '性价比之选，包含50,000积分，可观看500个视频，单价3.1元/视频，预计曝光量250,000',
    50000,
    1550.00,
    1550.00,
    0.00,
    0,
    '1',
    '0',
    3,
    '0',
    '0',
    NOW(),
    '普通套餐，基础播放量500/视频'
),
(
    '000000',
    '铂金跃升包',
    '高级用户推荐，包含100,000积分，可观看1,000个视频，单价2.8元/视频，预计曝光量500,000',
    100000,
    2800.00,
    2800.00,
    0.00,
    0,
    '1',
    '1',
    4,
    '0',
    '0',
    NOW(),
    '热门套餐，基础播放量500/视频'
),
(
    '000000',
    '钻石尊享包',
    '专业用户专属，包含300,000积分，可观看3,000个视频，单价2.5元/视频，预计曝光量1,500,000',
    300000,
    7500.00,
    7500.00,
    0.00,
    0,
    '1',
    '0',
    5,
    '0',
    '0',
    NOW(),
    '专业套餐，基础播放量500/视频'
),
(
    '000000',
    '王者全域包',
    '顶级用户至尊选择，包含500,000积分，可观看5,000个视频，单价2.0元/视频，预计曝光量2,500,000',
    500000,
    10000.00,
    10000.00,
    0.00,
    0,
    '1',
    '1',
    6,
    '0',
    '0',
    NOW(),
    '至尊套餐，基础播放量500/视频'
);
