# BusinessesVideoUploadModule 上传组件

这是一个从 `task-operate-drawer.vue` 中迁移出来的增强上传组件，支持视频、音频和克隆音色文件的上传。

## 功能特性

- 🎬 **多类型支持**: 支持视频、音频、克隆音色三种上传类型
- 🎨 **美观界面**: 采用渐变背景、动画效果和现代化设计
- 📁 **多文件上传**: 支持单文件或多文件上传
- 🔄 **上传状态**: 实时显示上传进度和状态
- 👀 **文件预览**: 提供文件预览功能
- 🗑️ **文件管理**: 支持文件删除和管理
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 组件属性 (Props)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `uploadType` | `'video' \| 'audio' \| 'clone-voice'` | `'video'` | 上传类型 |
| `multiple` | `boolean` | `true` | 是否支持多文件上传 |
| `maxCount` | `number` | `50` | 最大上传数量 |
| `accept` | `string` | 视频格式 | 接受的文件格式 |
| `hintText` | `string` | `'可上传多个文件'` | 上传提示文本 |
| `uploading` | `boolean` | `false` | 是否正在上传 |

## 组件事件 (Emits)

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `upload` | `{ file: File, onFinish: () => void, onError: () => void }` | 文件上传事件 |
| `preview` | `file: any` | 文件预览事件 |
| `remove` | `file: any` | 文件删除事件 |
| `showPreview` | - | 显示预览弹窗事件 |
| `update:fileList` | `value: any[]` | 文件列表更新事件 |

## 使用示例

### 基础用法

```vue
<template>
  <BusinessesVideoUploadModule
    v-model:file-list="fileList"
    upload-type="video"
    @upload="handleUpload"
    @preview="handlePreview"
    @remove="handleRemove"
    @show-preview="showPreview"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import BusinessesVideoUploadModule from './businesses-video-upload-module.vue';

const fileList = ref([]);

function handleUpload({ file, onFinish, onError }) {
  // 处理文件上传逻辑
  console.log('上传文件:', file);
  
  // 模拟上传
  setTimeout(() => {
    onFinish(); // 上传成功
    // 或者 onError(); // 上传失败
  }, 2000);
}

function handlePreview(file) {
  console.log('预览文件:', file);
}

function handleRemove(file) {
  console.log('删除文件:', file);
}

function showPreview() {
  console.log('显示预览弹窗');
}
</script>
```

### 视频上传

```vue
<BusinessesVideoUploadModule
  v-model:file-list="videoFiles"
  upload-type="video"
  :multiple="true"
  :max-count="10"
  accept=".mp4,.avi,.mov,.wmv,.flv,.mkv,.webm,.m4v,.3gp,.ts,.mts,.m2ts"
  hint-text="可上传多个视频文件"
  @upload="handleVideoUpload"
/>
```

### 音频上传

```vue
<BusinessesVideoUploadModule
  v-model:file-list="audioFiles"
  upload-type="audio"
  :multiple="true"
  :max-count="100"
  accept=".mp3,.wav,.ogg,.aac,.flac,.m4a,.wma,.aiff,.alac"
  hint-text="可上传多个音频文件"
  @upload="handleAudioUpload"
/>
```

### 克隆音色上传

```vue
<BusinessesVideoUploadModule
  v-model:file-list="cloneVoiceFiles"
  upload-type="clone-voice"
  :multiple="false"
  :max-count="1"
  accept=".mp3,.wav,.ogg,.aac,.flac,.m4a,.wma,.aiff,.alac"
  hint-text="仅限上传1个音色文件"
  @upload="handleCloneVoiceUpload"
/>
```

## 样式特性

### 上传类型样式

- **视频上传**: 绿色主题 (#52c41a)
- **音频上传**: 橙色主题 (#fa8c16)
- **克隆音色**: 紫色主题 (#722ed1)

### 交互效果

- 悬停时组件上移并显示阴影
- 上传时显示脉冲动画
- 图标缩放和颜色变化
- 渐变背景过渡

### 响应式设计

- 自适应容器宽度
- 移动端友好的触摸交互
- 灵活的布局适配

## 迁移说明

这个组件是从 `src/views/media/task/modules/task-operate-drawer.vue` 中的上传功能迁移而来，主要改进包括：

1. **组件化**: 将上传功能独立为可复用组件
2. **类型安全**: 使用 TypeScript 提供完整的类型定义
3. **样式优化**: 提取并优化了所有上传相关的样式
4. **功能增强**: 支持多种上传类型和自定义配置
5. **事件系统**: 提供完整的事件回调机制

## 依赖

- Vue 3
- Naive UI
- TypeScript (可选)

## 注意事项

1. 确保项目中已安装并配置了 Naive UI
2. 组件使用了 `defineModel` API，需要 Vue 3.3+ 版本
3. 样式使用了 SCSS，确保项目支持 SCSS 编译
4. 文件上传需要自行实现后端接口逻辑
