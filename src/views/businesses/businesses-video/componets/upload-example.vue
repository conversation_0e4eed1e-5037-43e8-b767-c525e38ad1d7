<template>
  <div class="upload-example-container">
    <h2>上传组件使用示例</h2>
    
    <!-- 视频上传示例 -->
    <div class="example-section">
      <h3>视频上传</h3>
      <BusinessesVideoUploadModule
        v-model:file-list="videoFiles"
        upload-type="video"
        :multiple="true"
        :max-count="10"
        accept=".mp4,.avi,.mov,.wmv,.flv,.mkv,.webm,.m4v,.3gp,.ts,.mts,.m2ts"
        hint-text="可上传多个视频文件"
        :uploading="isVideoUploading"
        @upload="handleVideoUpload"
        @preview="handlePreview"
        @remove="handleRemove"
        @show-preview="showVideoPreview"
      />
    </div>

    <!-- 音频上传示例 -->
    <div class="example-section">
      <h3>音频上传</h3>
      <BusinessesVideoUploadModule
        v-model:file-list="audioFiles"
        upload-type="audio"
        :multiple="true"
        :max-count="100"
        accept=".mp3,.wav,.ogg,.aac,.flac,.m4a,.wma,.aiff,.alac"
        hint-text="可上传多个音频文件"
        :uploading="isAudioUploading"
        @upload="handleAudioUpload"
        @preview="handlePreview"
        @remove="handleRemove"
        @show-preview="showAudioPreview"
      />
    </div>

    <!-- 克隆音色上传示例 -->
    <div class="example-section">
      <h3>克隆音色上传</h3>
      <BusinessesVideoUploadModule
        v-model:file-list="cloneVoiceFiles"
        upload-type="clone-voice"
        :multiple="false"
        :max-count="1"
        accept=".mp3,.wav,.ogg,.aac,.flac,.m4a,.wma,.aiff,.alac"
        hint-text="仅限上传1个音色文件"
        :uploading="isCloneVoiceUploading"
        @upload="handleCloneVoiceUpload"
        @preview="handlePreview"
        @remove="handleRemove"
        @show-preview="showCloneVoicePreview"
      />
    </div>

    <!-- 文件列表显示 -->
    <div class="file-lists">
      <div class="file-list-section">
        <h4>视频文件列表 ({{ videoFiles.length }})</h4>
        <pre>{{ JSON.stringify(videoFiles, null, 2) }}</pre>
      </div>
      
      <div class="file-list-section">
        <h4>音频文件列表 ({{ audioFiles.length }})</h4>
        <pre>{{ JSON.stringify(audioFiles, null, 2) }}</pre>
      </div>
      
      <div class="file-list-section">
        <h4>克隆音色文件列表 ({{ cloneVoiceFiles.length }})</h4>
        <pre>{{ JSON.stringify(cloneVoiceFiles, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import BusinessesVideoUploadModule from './businesses-video-upload-module.vue';

// 文件列表
const videoFiles = ref<any[]>([]);
const audioFiles = ref<any[]>([]);
const cloneVoiceFiles = ref<any[]>([]);

// 上传状态
const isVideoUploading = ref(false);
const isAudioUploading = ref(false);
const isCloneVoiceUploading = ref(false);

// 模拟上传函数
function simulateUpload(type: string, file: File, onFinish: () => void, onError: () => void) {
  console.log(`开始上传${type}:`, file.name);
  
  // 模拟上传过程
  setTimeout(() => {
    const success = Math.random() > 0.2; // 80% 成功率
    
    if (success) {
      console.log(`${type}上传成功:`, file.name);
      onFinish();
    } else {
      console.log(`${type}上传失败:`, file.name);
      onError();
    }
  }, 2000 + Math.random() * 3000); // 2-5秒随机延迟
}

// 视频上传处理
function handleVideoUpload({ file, onFinish, onError }: any) {
  isVideoUploading.value = true;
  simulateUpload('视频', file, () => {
    isVideoUploading.value = false;
    onFinish();
  }, () => {
    isVideoUploading.value = false;
    onError();
  });
}

// 音频上传处理
function handleAudioUpload({ file, onFinish, onError }: any) {
  isAudioUploading.value = true;
  simulateUpload('音频', file, () => {
    isAudioUploading.value = false;
    onFinish();
  }, () => {
    isAudioUploading.value = false;
    onError();
  });
}

// 克隆音色上传处理
function handleCloneVoiceUpload({ file, onFinish, onError }: any) {
  isCloneVoiceUploading.value = true;
  simulateUpload('克隆音色', file, () => {
    isCloneVoiceUploading.value = false;
    onFinish();
  }, () => {
    isCloneVoiceUploading.value = false;
    onError();
  });
}

// 文件预览
function handlePreview(file: any) {
  console.log('预览文件:', file);
  window.$message?.info(`预览文件: ${file.name || '未知文件'}`);
}

// 文件删除
function handleRemove(file: any) {
  console.log('删除文件:', file);
  window.$message?.success(`删除文件: ${file.name || '未知文件'}`);
}

// 显示预览弹窗
function showVideoPreview() {
  console.log('显示视频预览弹窗');
  window.$message?.info('显示视频预览弹窗');
}

function showAudioPreview() {
  console.log('显示音频预览弹窗');
  window.$message?.info('显示音频预览弹窗');
}

function showCloneVoicePreview() {
  console.log('显示克隆音色预览弹窗');
  window.$message?.info('显示克隆音色预览弹窗');
}
</script>

<style scoped lang="scss">
.upload-example-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 32px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: #ffffff;
}

.example-section h3 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 18px;
  font-weight: 600;
}

.file-lists {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 32px;
}

.file-list-section {
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.file-list-section h4 {
  margin: 0 0 12px 0;
  color: #4a5568;
  font-size: 14px;
  font-weight: 600;
}

.file-list-section pre {
  margin: 0;
  padding: 12px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
  color: #2d3748;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}
</style>
