package com.dock.utils;

import com.dock.domain.SubtitleStyle;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;

@Slf4j
public class SubtitleOverlayUtil {

    public static void drawSubtitleAt(String inputPath, String outputPath, int x, int y, SubtitleStyle style) throws Exception {
        BufferedImage image = ImageIO.read(new File(inputPath));

        // 获取图片实际尺寸
        int imageWidth = image.getWidth();
        int imageHeight = image.getHeight();
        double imageAspectRatio = (double) imageWidth / imageHeight;

        // 动态计算基准尺寸和缩放比例 - 不写死具体数值
        double fontScale;
        double coordinateScaleX, coordinateScaleY;

        if (imageAspectRatio > 1.0) {
            // 横屏视频：以较大尺寸作为基准
            int baseSize = Math.max(imageWidth, imageHeight);
            fontScale = (double) baseSize / 1920.0; // 使用1920作为参考基准
            coordinateScaleX = (double) imageWidth / 1920.0;
            coordinateScaleY = (double) imageHeight / 1080.0;
        } else {
            // 竖屏视频：以较大尺寸作为基准
            int baseSize = Math.max(imageWidth, imageHeight);
            fontScale = (double) baseSize / 1920.0; // 使用1920作为参考基准
            coordinateScaleX = (double) imageWidth / 1080.0;
            coordinateScaleY = (double) imageHeight / 1920.0;
        }

        // 调整字体大小，确保在合理范围内
        int adjustedFontSize = Math.max(8, (int) Math.round(style.getFontSize() * fontScale));

        log.info("图片尺寸: {}x{}, 宽高比: {:.3f}, 字体缩放: {:.3f}, 原始字体: {}pt, 调整后字体: {}pt",
                imageWidth, imageHeight, imageAspectRatio, fontScale, style.getFontSize(), adjustedFontSize);

        Graphics2D g2d = image.createGraphics();
        // 开启抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        int fontStyle = switch (style.getFontStyle().toLowerCase()) {
            case "bold" -> Font.BOLD;
            case "italic" -> Font.ITALIC;
            default -> Font.PLAIN;
        };
        g2d.setFont(new Font(style.getFontName(), fontStyle, adjustedFontSize));
        g2d.setColor(new Color(style.getR(), style.getG(), style.getB()));
        g2d.setStroke(new BasicStroke(2));

//        String text = style.getText();
//        FontMetrics fm = g2d.getFontMetrics();

        // 使用动态计算的坐标缩放比例
        int adjustedX = (int) Math.round(x * coordinateScaleX);
        int adjustedY = (int) Math.round(y * coordinateScaleY);

        // 计算文本尺寸以便居中定位
        String text = style.getText();
        FontMetrics fm = g2d.getFontMetrics();
        int textWidth = fm.stringWidth(text);
        int textHeight = fm.getHeight();

        // 将坐标从中心点转换为左上角坐标
        int startX = adjustedX - (textWidth / 2);
        int startY = adjustedY - (textHeight / 2) + fm.getAscent(); // 加上ascent以正确定位基线

        // 允许更灵活的边界，只要文本的主要部分在图片内即可
        // 允许文本边缘稍微超出图片边界，但确保至少50%的文本可见
        int minX = -(textWidth / 2);  // 允许左边一半超出
        int maxX = imageWidth + (textWidth / 2);  // 允许右边一半超出
        int minY = fm.getAscent();  // 确保文本基线在图片内
        int maxY = imageHeight;  // 允许底部稍微超出

        startX = Math.max(minX, Math.min(maxX - textWidth, startX));
        startY = Math.max(minY, Math.min(maxY, startY));

        log.info("原始中心坐标: ({}, {}), 调整后坐标: ({}, {}), 转换后左上角坐标: ({}, {}), 文本尺寸: {}x{}",
                x, y, adjustedX, adjustedY, startX, startY, textWidth, textHeight);

        // 重新计算可用宽度
        int maxWidth = imageWidth - startX - 5;
        int lineHeight = fm.getHeight();
        int curY = startY;

        StringBuilder line = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            line.append(c);
            int lineWidth = fm.stringWidth(line.toString());
            if (lineWidth > maxWidth) {
                line.deleteCharAt(line.length() - 1);
                g2d.drawString(line.toString(), startX, curY);
                curY += lineHeight;
                line = new StringBuilder();
                line.append(c);
            }
        }

        if (line.length() > 0) {
            g2d.drawString(line.toString(), startX, curY);
        }

        g2d.dispose();
        ImageIO.write(image, "jpg", new File(outputPath));
    }






    public static void deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            boolean deleted = file.delete();
            if (!deleted) {
                log.warn("无法删除临时文件: {}", filePath);
            }
        }
    }


}
