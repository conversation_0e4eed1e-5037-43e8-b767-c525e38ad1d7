package com.dock.utils;

import java.io.File;
import java.io.FileInputStream;
import java.util.Base64;

public class ImageBase64Util {
    public static String imageToBase64(String imagePath) throws Exception {
        File file = new File(imagePath);
        FileInputStream fis = new FileInputStream(file);
        byte[] bytes = fis.readAllBytes();
        fis.close();
        return Base64.getEncoder().encodeToString(bytes);
    }
}
