package com.dock.utils;

import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.text.TextContentRenderer;

/**
 * <AUTHOR>
 * @createtime 2025/07/8
 * @function 去除markdown格式，将markdown转成纯文本
 */
public class MarkdownUtils {
    public static String removeMarkdownTags(String markdownText) {
        Parser parser = Parser.builder().build();
        Node document = parser.parse(markdownText);
        // HtmlRenderer renderer = HtmlRenderer.builder().build(); //可以markdown转html
        TextContentRenderer renderer = TextContentRenderer.builder().build();
        return renderer.render(document);
    }
}
