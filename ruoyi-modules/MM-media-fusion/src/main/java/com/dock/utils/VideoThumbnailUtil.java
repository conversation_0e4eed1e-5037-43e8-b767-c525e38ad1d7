package com.dock.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.net.URL;

@Slf4j
public class VideoThumbnailUtil {

    /**
     * 视频信息类
     */
    @Data
    public static class VideoInfo {
        /** 视频宽度 */
        private int width;
        /** 视频高度 */
        private int height;
        /** 宽高比 */
        private double aspectRatio;
        /** 视频方向：landscape(横屏), portrait(竖屏), square(方形) */
        private String orientation;
    }

    /**
     * 获取视频信息
     * @param videoUrl 视频URL
     * @return 视频信息
     * @throws Exception 获取失败时抛出异常
     */
    public static VideoInfo getVideoInfo(String videoUrl) throws Exception {
        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(new URL(videoUrl));
        try {
            grabber.start();

            VideoInfo videoInfo = new VideoInfo();
            videoInfo.setWidth(grabber.getImageWidth());
            videoInfo.setHeight(grabber.getImageHeight());

            // 计算宽高比
            double aspectRatio = (double) videoInfo.getWidth() / videoInfo.getHeight();
            videoInfo.setAspectRatio(aspectRatio);

            // 判断视频方向 - 使用更灵活的判断标准
            if (aspectRatio > 1.0) {
                videoInfo.setOrientation("landscape"); // 横屏
            } else if (aspectRatio < 1.0) {
                videoInfo.setOrientation("portrait"); // 竖屏
            } else {
                videoInfo.setOrientation("square"); // 方形
            }

            log.info("视频信息: {}x{}, 宽高比: {:.3f}, 方向: {}",
                    videoInfo.getWidth(), videoInfo.getHeight(), aspectRatio, videoInfo.getOrientation());

            return videoInfo;
        } finally {
            grabber.stop();
        }
    }

    /**
     * 截取视频第一帧（原有方法保持兼容性）
     * @param videoUrl 视频URL
     * @param outputImagePath 输出图片路径
     * @throws Exception 截取失败时抛出异常
     */
    public static void grabFirstFrame(String videoUrl, String outputImagePath) throws Exception {
        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(new URL(videoUrl));
        try {
            grabber.start();

            Frame frame = grabber.grabImage(); // 抓取第一帧图像
            if (frame != null) {
                Java2DFrameConverter converter = new Java2DFrameConverter();
                BufferedImage bufferedImage = converter.convert(frame);
                ImageIO.write(bufferedImage, "jpg", new File(outputImagePath));
            }
        } finally {
            grabber.stop();
        }
    }
}
