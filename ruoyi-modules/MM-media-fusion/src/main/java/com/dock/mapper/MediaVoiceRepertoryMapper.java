package com.dock.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.dock.domain.MediaVoiceRepertory;
import com.dock.domain.vo.MediaVoiceRepertoryVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * voiceMapper接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
// 不再过滤租户id
@InterceptorIgnore(tenantLine = "true", dataPermission = "false")
public interface MediaVoiceRepertoryMapper extends BaseMapperPlus<MediaVoiceRepertory, MediaVoiceRepertoryVo> {

}
