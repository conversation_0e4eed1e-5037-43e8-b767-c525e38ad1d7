package com.dock.service;

import com.dock.domain.vo.MediaDigitalLensVo;
import com.dock.domain.bo.MediaDigitalLensBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * digitalLensService接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IMediaDigitalLensService {

    /**
     * 查询digitalLens
     *
     * @param id 主键
     * @return digitalLens
     */
    MediaDigitalLensVo queryById(Long id);

    /**
     * 分页查询digitalLens列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return digitalLens分页列表
     */
    TableDataInfo<MediaDigitalLensVo> queryPageList(MediaDigitalLensBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的digitalLens列表
     *
     * @param bo 查询条件
     * @return digitalLens列表
     */
    List<MediaDigitalLensVo> queryList(MediaDigitalLensBo bo);

    /**
     * 新增digitalLens
     *
     * @param bo digitalLens
     * @return 是否新增成功
     */
    Boolean insertByBo(MediaDigitalLensBo bo);

    /**
     * 批量新增digitalLens
     *
     * @param bos digitalLens列表
     * @return 是否新增成功
     */
    Boolean batchInsertByBos(List<MediaDigitalLensBo> bos);

    /**
     * 修改digitalLens
     *
     * @param bo digitalLens
     * @return 是否修改成功
     */
    Boolean updateByBo(MediaDigitalLensBo bo);

    /**
     * 校验并批量删除digitalLens信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    
    /**
     * 处理目标文本 - 将使用&&分隔的多段文本转换为JSON格式
     *
     * @param bo 数据对象
     */
    void processTargetText(MediaDigitalLensBo bo);
    
    /**
     * 处理素材分离 - 将素材根据类型分离到视频和音频字段
     *
     * @param bo 数据对象
     */
    void processMaterialsSeparation(MediaDigitalLensBo bo);
}
