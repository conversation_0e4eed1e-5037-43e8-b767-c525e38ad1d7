package com.dock.service.impl;
import com.dock.domain.MediaTask;
import com.dock.domain.bo.MediaTaskBo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.dock.domain.SubtitleStyle;
import com.dock.domain.VideoInfoResponse;
import com.dock.mapper.MediaTaskMapper;
import com.dock.service.IMediaInterceptService;
import com.dock.service.IMediaTaskService;
import com.dock.utils.ImageBase64Util;
import com.dock.utils.SubtitleOverlayUtil;
import com.dock.utils.VideoThumbnailUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.system.service.impl.SysDictTypeServiceImpl;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class IMediaInterceptServiceImpl implements IMediaInterceptService {

    private final IMediaTaskService iMediaTaskService;
    private final MediaTaskMapper mediaTaskMapper;
    private final SysDictTypeServiceImpl sysDictTypeServiceImpl;

    @Override
    public List<String> interceptAspect(String videoUrl, int x, int y, SubtitleStyle subtitleStyle) {
        List<String> base64Images = new ArrayList<>();
        try {

            String uniqueId = UUID.randomUUID().toString().replace("-", "");
            String basePath = System.getProperty("user.dir"); // 获取项目运行目录
            String outputDirPath = basePath + "/thumbnails";
            String outputPath = outputDirPath + "/preview_" + uniqueId + ".jpg";
            String previewPath = outputPath.replace(".jpg", "_preview.jpg");

// 确保目录存在
            File outputDir = new File(outputDirPath);
            if (!outputDir.exists()) {
                outputDir.mkdirs(); // 递归创建目录
            }
            // 截取视频第一帧
            VideoThumbnailUtil.grabFirstFrame(videoUrl, outputPath);

            // 在指定x, y绘制字幕
            SubtitleOverlayUtil.drawSubtitleAt(outputPath, previewPath, x, y, subtitleStyle);
            String text = subtitleStyle.getText();
            String[] lines = text != null ? text.split("\\r?\\n") : new String[0]; // 支持 Windows/Linux 换行
            subtitleStyle.setLines(Arrays.asList(lines));
            log.info("Arrays.asList(lines){}",Arrays.asList(lines));
            String hexadecimal = String.format("#%02X%02X%02X", subtitleStyle.getR(), subtitleStyle.getG(), subtitleStyle.getB());
            log.info("十六进制颜色为：{}", hexadecimal);
            subtitleStyle.setHexadecimal(hexadecimal);
            Map<String, Object> positionMap = new HashMap<>();
            positionMap.put("x", x);
            positionMap.put("y", y);
            positionMap.put("subtitleStyle", subtitleStyle);

            ObjectMapper objectMapper = new ObjectMapper();
            String position = objectMapper.writeValueAsString(positionMap);

            // 字幕预览不需要更新数据库，只生成预览图片
            log.info("字幕预览位置信息: {}", position);
            MediaTaskBo task = new MediaTaskBo();
            task.setSubtitlePosition(position);
            // 转base64
            String base64 = ImageBase64Util.imageToBase64(outputPath);
            base64Images.add("data:image/jpeg;base64," + base64);
            SubtitleOverlayUtil.deleteFile(previewPath);
            SubtitleOverlayUtil.deleteFile(outputPath);
        } catch (Exception e) {
            log.error("处理视频预览时出错: {}", e.getMessage(), e);
        }

        return base64Images;
    }

    @Override
    public VideoInfoResponse getVideoInfo(String videoUrl) {
        try {
            VideoThumbnailUtil.VideoInfo videoInfo = VideoThumbnailUtil.getVideoInfo(videoUrl);

            VideoInfoResponse response = new VideoInfoResponse();
            response.setWidth(videoInfo.getWidth());
            response.setHeight(videoInfo.getHeight());
            response.setAspectRatio(videoInfo.getAspectRatio());
            response.setOrientation(videoInfo.getOrientation());

            log.info("获取视频信息成功: {}x{}, 方向: {}",
                    response.getWidth(), response.getHeight(), response.getOrientation());

            return response;
        } catch (Exception e) {
            log.error("获取视频信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取视频信息失败: " + e.getMessage());
        }
    }
}
