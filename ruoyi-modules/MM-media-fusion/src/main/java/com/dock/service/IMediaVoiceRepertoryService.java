package com.dock.service;

import com.dock.domain.vo.MediaVoiceRepertoryVo;
import com.dock.domain.bo.MediaVoiceRepertoryBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * voiceService接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IMediaVoiceRepertoryService {

    /**
     * 查询voice
     *
     * @param id 主键
     * @return voice
     */
    MediaVoiceRepertoryVo queryById(Long id);

    /**
     * 分页查询voice列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return voice分页列表
     */
    TableDataInfo<MediaVoiceRepertoryVo> queryPageList(MediaVoiceRepertoryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的voice列表
     *
     * @param bo 查询条件
     * @return voice列表
     */
    List<MediaVoiceRepertoryVo> queryList(MediaVoiceRepertoryBo bo);

    /**
     * 新增voice
     *
     * @param bo voice
     * @return 是否新增成功
     */
    Boolean insertByBo(MediaVoiceRepertoryBo bo);

    /**
     * 修改voice
     *
     * @param bo voice
     * @return 是否修改成功
     */
    Boolean updateByBo(MediaVoiceRepertoryBo bo);

    /**
     * 校验并批量删除voice信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
