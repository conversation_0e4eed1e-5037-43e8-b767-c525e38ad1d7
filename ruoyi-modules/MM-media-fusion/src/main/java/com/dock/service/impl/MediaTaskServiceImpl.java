package com.dock.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dock.domain.MediaTask;
import com.dock.domain.bo.MediaTaskBo;
import com.dock.domain.vo.MediaTaskVo;
import com.dock.mapper.MediaTaskMapper;
import com.dock.service.IMediaTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.*;

import org.json.JSONArray;
import org.json.JSONException;
import org.springframework.transaction.annotation.Transactional;

/**
 * media服务实现
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MediaTaskServiceImpl extends ServiceImpl<MediaTaskMapper, MediaTask> implements IMediaTaskService {

    @Override
    public MediaTaskVo queryById(Long id) {
        MediaTask mediaTask = getById(id);
        if (mediaTask != null) {
            // 处理JSON格式的字段
            processJsonFields(mediaTask);
            return MapstructUtils.convert(mediaTask, MediaTaskVo.class);
        }
        return null;
    }

    @Override
    public TableDataInfo<MediaTaskVo> queryPageList(MediaTaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MediaTask> lqw = buildQueryWrapper(bo);
        Page<MediaTask> result = page(pageQuery.build(), lqw);
        
        // 处理结果中的JSON格式字段
        for (MediaTask task : result.getRecords()) {
            processJsonFields(task);
        }
        
        // 转换为VO对象列表
        List<MediaTaskVo> voList = MapstructUtils.convert(result.getRecords(), MediaTaskVo.class);
        
        // 创建TableDataInfo对象
        TableDataInfo<MediaTaskVo> tableDataInfo = new TableDataInfo<>(voList, result.getTotal());
        return tableDataInfo;
    }

    @Override
    public List<MediaTaskVo> queryList(MediaTaskBo bo) {
        LambdaQueryWrapper<MediaTask> lqw = buildQueryWrapper(bo);
        List<MediaTask> list = list(lqw);
        
        // 处理结果中的JSON格式字段
        for (MediaTask task : list) {
            processJsonFields(task);
        }
        
        return MapstructUtils.convert(list, MediaTaskVo.class);
    }

    private LambdaQueryWrapper<MediaTask> buildQueryWrapper(MediaTaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MediaTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTaskId() != null, MediaTask::getTaskId, bo.getTaskId());
        lqw.like(StringUtils.isNotBlank(bo.getTaskName()), MediaTask::getTaskName, bo.getTaskName());
        lqw.like(StringUtils.isNotBlank(bo.getVideoTitle()), MediaTask::getVideoTitle, bo.getVideoTitle());
        lqw.like(StringUtils.isNotBlank(bo.getFixedSubtitle()), MediaTask::getFixedSubtitle, bo.getFixedSubtitle());
        lqw.eq(StringUtils.isNotBlank(bo.getSubtitlePosition()), MediaTask::getSubtitlePosition, bo.getSubtitlePosition());
        lqw.like(StringUtils.isNotBlank(bo.getRelatedSubtitle()), MediaTask::getRelatedSubtitle, bo.getRelatedSubtitle());
        lqw.eq(StringUtils.isNotBlank(bo.getVoiceType()), MediaTask::getVoiceType, bo.getVoiceType());
        lqw.like(StringUtils.isNotBlank(bo.getVoicePreset()), MediaTask::getVoicePreset, bo.getVoicePreset());
        lqw.like(StringUtils.isNotBlank(bo.getCloneVoiceReferenceText()), MediaTask::getCloneVoiceReferenceText, bo.getCloneVoiceReferenceText());
        lqw.like(StringUtils.isNotBlank(bo.getCloneVoiceTargetText()), MediaTask::getCloneVoiceTargetText, bo.getCloneVoiceTargetText());
        lqw.eq(StringUtils.isNotBlank(bo.getClipCount()), MediaTask::getClipCount, bo.getClipCount());
        lqw.eq(StringUtils.isNotBlank(bo.getClipDuration()), MediaTask::getClipDuration, bo.getClipDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getShotType()), MediaTask::getShotType, bo.getShotType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MediaTask::getStatus, bo.getStatus());
        return lqw;
    }

    @Override
    public Boolean insertByBo(MediaTaskBo bo) {
        // 处理提交的JSON格式字段
        processJsonFieldsForSubmit(bo);

        // 生成UUID作为taskId
        if (StringUtils.isBlank(bo.getTaskId())) {
            bo.setTaskId(UUID.randomUUID().toString());
        }

        MediaTask add = MapstructUtils.convert(bo, MediaTask.class);
        // 强制设置ID为null，让数据库自增
        add.setId(null);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(MediaTaskBo bo) {
        // 处理提交的JSON格式字段
        processJsonFieldsForSubmit(bo);
        
        MediaTask update = MapstructUtils.convert(bo, MediaTask.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsertByBos(List<MediaTaskBo> bos) {
        if (bos == null || bos.isEmpty()) {
            return false;
        }

        List<MediaTask> tasks = new ArrayList<>(bos.size());
        
        for (MediaTaskBo bo : bos) {
            // 处理提交的JSON格式字段
            processJsonFieldsForSubmit(bo);

            // 记录字幕位置信息
            log.info("批量插入任务 - 字幕位置信息: {}", bo.getSubtitlePosition());

            // 如果没有设置任务名称，使用视频标题作为任务名称
            if (StringUtils.isBlank(bo.getTaskName()) && StringUtils.isNotBlank(bo.getVideoTitle())) {
                bo.setTaskName(bo.getVideoTitle());
            }
            
            // 默认设置voiceType为2(克隆音色)，status为0(待处理)
            if (StringUtils.isBlank(bo.getVoiceType())) {
                bo.setVoiceType("2");
            }
            if (StringUtils.isBlank(bo.getStatus())) {
                bo.setStatus("0");
            }
            
//            // 默认设置字幕位置为bottom-center
//            if (StringUtils.isBlank(bo.getSubtitlePosition())) {
//                bo.setSubtitlePosition("bottom-center");
//            }

            // 生成UUID作为taskId
            if (StringUtils.isBlank(bo.getTaskId())) {
                bo.setTaskId(UUID.randomUUID().toString());
            }

            MediaTask task = MapstructUtils.convert(bo, MediaTask.class);
            // 强制设置ID为null，让数据库自增
            task.setId(null);
            validEntityBeforeSave(task);
            tasks.add(task);
        }
        
        // 批量保存所有任务
        boolean flag = saveBatch(tasks);
        
        // 如果保存成功，更新所有BO对象的ID
        if (flag) {
            for (int i = 0; i < bos.size(); i++) {
                bos.get(i).setId(tasks.get(i).getId());
            }
        }
        
        return flag;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(MediaTask entity) {
        // 添加数据校验规则
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
    
    /**
     * 处理JSON格式的字段，确保前端能正确显示
     * 
     * @param task 媒体任务对象
     */
    private void processJsonFields(MediaTask task) {
        // 处理视频素材列表
        if (StringUtils.isNotEmpty(task.getVideoMaterials())) {
            if (task.getVideoMaterials().startsWith("[") && task.getVideoMaterials().endsWith("]")) {
                // 已经是JSON格式，保持不变
                // 注意：可能是二维数组 [[...]] 或多层二维数组 [[...], [...]]，都保持原样
            } else if (task.getVideoMaterials().contains(",")) {
                // 如果是逗号分隔的字符串，根据镜头类型转换为不同的JSON数组格式
                String[] urls = task.getVideoMaterials().split(",");
                JSONArray jsonArray = new JSONArray();
                
                if ("1".equals(task.getShotType())) {
                    // 随机混剪模式 - 二维数组 [[视频1, 视频2]]
                    JSONArray innerArray = new JSONArray();
                    for (String url : urls) {
                        innerArray.put(url.trim());
                    }
                    jsonArray.put(innerArray);
                } else {
                    // 指定镜头模式 - 一维数组转为单个分镜的二维数组
                for (String url : urls) {
                    jsonArray.put(url.trim());
                    }
                }
                
                task.setVideoMaterials(jsonArray.toString());
            } else {
                // 单个URL，根据镜头类型转换为不同的JSON数组格式
                JSONArray jsonArray = new JSONArray();
                
                if ("1".equals(task.getShotType())) {
                    // 随机混剪模式 - 二维数组 [[视频1]]
                    JSONArray innerArray = new JSONArray();
                    innerArray.put(task.getVideoMaterials());
                    jsonArray.put(innerArray);
                } else {
                    // 指定镜头模式 - 一维数组
                jsonArray.put(task.getVideoMaterials());
                }
                
                task.setVideoMaterials(jsonArray.toString());
            }
        }
        
        // 处理背景音乐素材列表 - 始终保持为一维数组
        if (StringUtils.isNotEmpty(task.getBackgroundMusic())) {
            if (task.getBackgroundMusic().startsWith("[") && task.getBackgroundMusic().endsWith("]")) {
                // 已经是JSON格式，保持不变
            } else if (task.getBackgroundMusic().contains(",")) {
                // 如果是逗号分隔的字符串，转换为JSON数组
                String[] urls = task.getBackgroundMusic().split(",");
                JSONArray jsonArray = new JSONArray();
                for (String url : urls) {
                    jsonArray.put(url.trim());
                }
                task.setBackgroundMusic(jsonArray.toString());
            } else {
                // 单个URL，转换为JSON数组
                JSONArray jsonArray = new JSONArray();
                jsonArray.put(task.getBackgroundMusic());
                task.setBackgroundMusic(jsonArray.toString());
            }
        }
        
        // 处理克隆音色文件 - 保持为字符串，不做JSON处理
        // 不再需要将克隆音色文件转换为JSON数组
    }

    /**
     * 处理提交的JSON格式字段，确保能正确保存到数据库
     *
     * @param bo 媒体任务业务对象
     */
    public void processJsonFieldsForSubmit(MediaTaskBo bo) {
        bo.setSubtitlePosition(bo.getSubtitlePosition());
        try {
            // 处理背景音乐 - 确保始终为JSON数组格式
            if (StringUtils.isNotEmpty(bo.getBackgroundMusic())) {
                if (bo.getBackgroundMusic().startsWith("[")) {
                    // 已经是JSON数组格式，验证一下格式
                    try {
                        new JSONArray(bo.getBackgroundMusic());
                        // 格式正确，保持不变
                    } catch (JSONException e) {
                        // JSON格式不正确，尝试其他处理方式
                        handleBackgroundMusicFormat(bo);
                    }
                } else {
                    // 不是JSON格式，需要转换
                    handleBackgroundMusicFormat(bo);
                }
            } else {
                // 为空时设置为空数组
                bo.setBackgroundMusic("[]");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
                
    /**
     * 处理背景音乐格式，确保转换为JSON数组
     */
    private void handleBackgroundMusicFormat(MediaTaskBo bo) {
        try {
            String music = bo.getBackgroundMusic();
            JSONArray jsonArray = new JSONArray();
            
            if (music.contains(",")) {
                // 逗号分隔的字符串转为JSON数组
                String[] urls = music.split(",");
                for (String url : urls) {
                    if (StringUtils.isNotBlank(url)) {
                        jsonArray.put(url.trim());
                    }
                }
            } else if (StringUtils.isNotBlank(music)) {
                // 单个URL转为JSON数组
                jsonArray.put(music.trim());
            }
            
            bo.setBackgroundMusic(jsonArray.toString());
        } catch (Exception e) {
            // 处理失败时，设置为空数组
            bo.setBackgroundMusic("[]");
        }
    }
}
