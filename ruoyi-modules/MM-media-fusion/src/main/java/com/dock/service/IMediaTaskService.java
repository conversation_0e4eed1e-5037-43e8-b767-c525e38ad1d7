package com.dock.service;

import com.dock.domain.vo.MediaTaskVo;
import com.dock.domain.bo.MediaTaskBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import java.util.Collection;
import java.util.List;

/**
 * 媒体任务Service接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface IMediaTaskService {

    /**
     * 查询媒体任务
     *
     * @param id 主键
     * @return 媒体任务
     */
    MediaTaskVo queryById(Long id);

    /**
     * 分页查询媒体任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 媒体任务分页列表
     */
    TableDataInfo<MediaTaskVo> queryPageList(MediaTaskBo bo, PageQuery pageQuery);

    /**
     * 查询媒体任务列表
     *
     * @param bo 查询条件
     * @return 媒体任务列表
     */
    List<MediaTaskVo> queryList(MediaTaskBo bo);

    /**
     * 新增媒体任务
     *
     * @param bo 媒体任务
     * @return 是否新增成功
     */
    Boolean insertByBo(MediaTaskBo bo);

    /**
     * 批量新增媒体任务
     *
     * @param bos 媒体任务列表
     * @return 是否新增成功
     */
    Boolean batchInsertByBos(List<MediaTaskBo> bos);

    /**
     * 修改媒体任务
     *
     * @param bo 媒体任务
     * @return 是否修改成功
     */
    Boolean updateByBo(MediaTaskBo bo);

    /**
     * 校验并批量删除媒体任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    
//    /**
//     * 处理JSON格式的字段，确保前端能正确显示
//     *
//     * @param bo 媒体任务业务对象
//     */
//    void processJsonFields(MediaTaskBo bo);
//
    /**
     * 处理提交的JSON格式字段，确保能正确保存到数据库
     * 
     * @param bo 媒体任务业务对象
     */
    void processJsonFieldsForSubmit(MediaTaskBo bo);
    
//    /**
//     * 设置默认值
//     *
//     * @param bo 媒体任务业务对象
//     */
//    void setDefaultValues(MediaTaskBo bo);
}
