package com.dock.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MediaVideoRepertoryBo;
import com.dock.domain.vo.MediaVideoRepertoryVo;
import com.dock.domain.MediaVideoRepertory;
import com.dock.mapper.MediaVideoRepertoryMapper;
import com.dock.service.IMediaVideoRepertoryService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * videoService业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MediaVideoRepertoryServiceImpl implements IMediaVideoRepertoryService {

    private final MediaVideoRepertoryMapper baseMapper;

    /**
     * 查询video
     *
     * @param id 主键
     * @return video
     */
    @Override
    public MediaVideoRepertoryVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询video列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return video分页列表
     */
    @Override
    public TableDataInfo<MediaVideoRepertoryVo> queryPageList(MediaVideoRepertoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MediaVideoRepertory> lqw = buildQueryWrapper(bo);
        Page<MediaVideoRepertoryVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的video列表
     *
     * @param bo 查询条件
     * @return video列表
     */
    @Override
    public List<MediaVideoRepertoryVo> queryList(MediaVideoRepertoryBo bo) {
        LambdaQueryWrapper<MediaVideoRepertory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MediaVideoRepertory> buildQueryWrapper(MediaVideoRepertoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MediaVideoRepertory> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MediaVideoRepertory::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getVideoPath()), MediaVideoRepertory::getVideoPath, bo.getVideoPath());
        return lqw;
    }

    /**
     * 新增video
     *
     * @param bo video
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MediaVideoRepertoryBo bo) {
        MediaVideoRepertory add = MapstructUtils.convert(bo, MediaVideoRepertory.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改video
     *
     * @param bo video
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MediaVideoRepertoryBo bo) {
        MediaVideoRepertory update = MapstructUtils.convert(bo, MediaVideoRepertory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MediaVideoRepertory entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除video信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
