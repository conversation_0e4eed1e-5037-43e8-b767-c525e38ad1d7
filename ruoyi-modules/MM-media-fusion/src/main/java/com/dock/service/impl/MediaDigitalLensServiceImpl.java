package com.dock.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MediaDigitalLensBo;
import com.dock.domain.vo.MediaDigitalLensVo;
import com.dock.domain.MediaDigitalLens;
import com.dock.mapper.MediaDigitalLensMapper;
import com.dock.service.IMediaDigitalLensService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * digitalLensService业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MediaDigitalLensServiceImpl implements IMediaDigitalLensService {

    private final MediaDigitalLensMapper baseMapper;

    // 允许的视频文件类型
    private static final String[] ALLOWED_VIDEO_EXTENSIONS = {
        ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", 
        ".webm", ".m4v", ".3gp", ".ts", ".mts", ".m2ts"
    };
    // 允许的音频文件类型
    private static final String[] ALLOWED_AUDIO_EXTENSIONS = {
        ".mp3", ".wav", ".ogg", ".aac", 
        ".flac", ".m4a", ".wma", ".aiff", ".alac"
    };

    /**
     * 查询digitalLens
     *
     * @param id 主键
     * @return digitalLens
     */
    @Override
    public MediaDigitalLensVo queryById(Long id) {
        MediaDigitalLensVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            // 兼容旧版本前端，将新字段合并到旧字段
            mergeOtherMaterials(vo);
        }
        return vo;
    }

    /**
     * 分页查询digitalLens列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return digitalLens分页列表
     */
    @Override
    public TableDataInfo<MediaDigitalLensVo> queryPageList(MediaDigitalLensBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MediaDigitalLens> lqw = buildQueryWrapper(bo);
        Page<MediaDigitalLensVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        
        // 处理每个结果，兼容旧版本前端
        for (MediaDigitalLensVo vo : result.getRecords()) {
            mergeOtherMaterials(vo);
        }
        
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的digitalLens列表
     *
     * @param bo 查询条件
     * @return digitalLens列表
     */
    @Override
    public List<MediaDigitalLensVo> queryList(MediaDigitalLensBo bo) {
        LambdaQueryWrapper<MediaDigitalLens> lqw = buildQueryWrapper(bo);
        List<MediaDigitalLensVo> list = baseMapper.selectVoList(lqw);
        
        // 处理每个结果，兼容旧版本前端
        for (MediaDigitalLensVo vo : list) {
            mergeOtherMaterials(vo);
        }
        
        return list;
    }
    
    /**
     * 合并其他素材字段，兼容旧版本前端
     */
    private void mergeOtherMaterials(MediaDigitalLensVo vo) {
        List<String> allMaterials = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();

        // 添加视频素材 - 支持JSON格式和逗号分隔格式
        if (StringUtils.isNotBlank(vo.getOtherVideoMaterials())) {
            if (vo.getOtherVideoMaterials().startsWith("[")) {
                // JSON格式
                try {
                    List<String> videoMaterialsList = objectMapper.readValue(vo.getOtherVideoMaterials(), new TypeReference<List<String>>() {});
                    allMaterials.addAll(videoMaterialsList);
                } catch (JsonProcessingException e) {
                    log.error("解析视频素材JSON失败", e);
                    // 降级处理：按逗号分隔
                    String[] videoMaterials = vo.getOtherVideoMaterials().split(",");
                    allMaterials.addAll(Arrays.asList(videoMaterials));
                }
            } else {
                // 逗号分隔格式
                String[] videoMaterials = vo.getOtherVideoMaterials().split(",");
                allMaterials.addAll(Arrays.asList(videoMaterials));
            }
        }

        // 添加音频素材 - 支持JSON格式和逗号分隔格式
        if (StringUtils.isNotBlank(vo.getOtherAudioMaterials())) {
            if (vo.getOtherAudioMaterials().startsWith("[")) {
                // JSON格式
                try {
                    List<String> audioMaterialsList = objectMapper.readValue(vo.getOtherAudioMaterials(), new TypeReference<List<String>>() {});
                    allMaterials.addAll(audioMaterialsList);
                } catch (JsonProcessingException e) {
                    log.error("解析音频素材JSON失败", e);
                    // 降级处理：按逗号分隔
                    String[] audioMaterials = vo.getOtherAudioMaterials().split(",");
                    allMaterials.addAll(Arrays.asList(audioMaterials));
                }
            } else {
                // 逗号分隔格式
                String[] audioMaterials = vo.getOtherAudioMaterials().split(",");
                allMaterials.addAll(Arrays.asList(audioMaterials));
            }
        }

        // 设置合并后的素材字段
        if (!allMaterials.isEmpty()) {
            vo.setOtherMaterials(String.join(",", allMaterials));
        }
    }

    private LambdaQueryWrapper<MediaDigitalLens> buildQueryWrapper(MediaDigitalLensBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MediaDigitalLens> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MediaDigitalLens::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), MediaDigitalLens::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getCharacterVideo()), MediaDigitalLens::getCharacterVideo, bo.getCharacterVideo());
        lqw.eq(StringUtils.isNotBlank(bo.getClonedVoice()), MediaDigitalLens::getClonedVoice, bo.getClonedVoice());
        lqw.eq(StringUtils.isNotBlank(bo.getReferenceText()), MediaDigitalLens::getReferenceText, bo.getReferenceText());
        lqw.eq(StringUtils.isNotBlank(bo.getTargetText()), MediaDigitalLens::getTargetText, bo.getTargetText());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherVideoMaterials()), MediaDigitalLens::getOtherVideoMaterials, bo.getOtherVideoMaterials());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherAudioMaterials()), MediaDigitalLens::getOtherAudioMaterials, bo.getOtherAudioMaterials());
        lqw.eq(bo.getStatus() != null, MediaDigitalLens::getStatus, bo.getStatus());
        lqw.eq(bo.getTaskId() != null, MediaDigitalLens::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getSubtitlePosition()), MediaDigitalLens::getSubtitlePosition, bo.getSubtitlePosition());
        return lqw;
    }

    /**
     * 新增digitalLens
     *
     * @param bo digitalLens
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MediaDigitalLensBo bo) {
        // 处理数据，包括目标文本和素材分离
        processData(bo);

        // 设置默认状态为待处理
        if (bo.getStatus() == null) {
            bo.setStatus(0);
        }

        // 生成UUID作为taskId
        if (StringUtils.isBlank(bo.getTaskId())) {
            bo.setTaskId(UUID.randomUUID().toString());
        }

        String clonedVoiceJson = bo.getClonedVoice();  // 原始是 [""]
        ObjectMapper objectMapper = new ObjectMapper();
        String clonedVoiceUrl = "";

        try {
            List<String> voiceList = objectMapper.readValue(clonedVoiceJson, new TypeReference<List<String>>() {});
            if (!voiceList.isEmpty()) {
                clonedVoiceUrl = voiceList.get(0);  // 如果你只需要第一个 URL
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }



        MediaDigitalLens add = MapstructUtils.convert(bo, MediaDigitalLens.class);
        // 强制设置ID为null，让数据库自增
        add.setId(null);
        add.setClonedVoice(clonedVoiceUrl);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量新增digitalLens
     *
     * @param bos digitalLens列表
     * @return 是否新增成功
     */
    @Override
    public Boolean batchInsertByBos(List<MediaDigitalLensBo> bos) {
        if (bos == null || bos.isEmpty()) {
            return false;
        }

        boolean allSuccess = true;
        for (MediaDigitalLensBo bo : bos) {
            // 对每个对象进行处理并插入
            boolean success = insertByBo(bo);
            if (!success) {
                allSuccess = false;
                log.error("批量插入数字镜像失败，标题: {}", bo.getTitle());
            }
        }
        
        return allSuccess;
    }

    /**
     * 修改digitalLens
     *
     * @param bo digitalLens
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MediaDigitalLensBo bo) {
        // 处理数据，包括目标文本和素材分离
        processData(bo);
        
        MediaDigitalLens update = MapstructUtils.convert(bo, MediaDigitalLens.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }
    
    /**
     * 处理数据 - 包括处理目标文本、素材分离和JSON格式转换
     *
     * @param bo 数据对象
     */
    private void processData(MediaDigitalLensBo bo) {
        // 处理目标文本
        processTargetText(bo);

        // 处理素材分离
        processMaterialsSeparation(bo);

        // 处理JSON格式的素材数据
        processJsonMaterials(bo);
    }

    /**
     * 处理素材数据 - 保持JSON格式存储
     *
     * @param bo 数据对象
     */
    private void processJsonMaterials(MediaDigitalLensBo bo) {
        ObjectMapper objectMapper = new ObjectMapper();

        // 处理其他音频素材 - 确保存储为JSON格式
        String otherAudioMaterialsStr = bo.getOtherAudioMaterials();
        if (StringUtils.isNotBlank(otherAudioMaterialsStr)) {
            if (!otherAudioMaterialsStr.startsWith("[")) {
                // 如果不是JSON格式，尝试转换
                if (otherAudioMaterialsStr.contains(",")) {
                    // 逗号分隔的字符串，转换为JSON数组
                    String[] urls = otherAudioMaterialsStr.split(",");
                    List<String> urlList = Arrays.asList(urls);
                    try {
                        bo.setOtherAudioMaterials(objectMapper.writeValueAsString(urlList));
                    } catch (JsonProcessingException e) {
                        log.error("转换其他音频素材为JSON失败", e);
                    }
                } else {
                    // 单个URL，转换为JSON数组
                    try {
                        bo.setOtherAudioMaterials(objectMapper.writeValueAsString(Arrays.asList(otherAudioMaterialsStr)));
                    } catch (JsonProcessingException e) {
                        log.error("转换其他音频素材为JSON失败", e);
                    }
                }
            }
            // 如果已经是JSON格式，保持不变
        }

        // 处理其他视频素材 - 确保存储为JSON格式
        String otherVideoMaterialsStr = bo.getOtherVideoMaterials();
        if (StringUtils.isNotBlank(otherVideoMaterialsStr)) {
            if (!otherVideoMaterialsStr.startsWith("[")) {
                // 如果不是JSON格式，尝试转换
                if (otherVideoMaterialsStr.contains(",")) {
                    // 逗号分隔的字符串，转换为JSON数组
                    String[] urls = otherVideoMaterialsStr.split(",");
                    List<String> urlList = Arrays.asList(urls);
                    try {
                        bo.setOtherVideoMaterials(objectMapper.writeValueAsString(urlList));
                    } catch (JsonProcessingException e) {
                        log.error("转换其他视频素材为JSON失败", e);
                    }
                } else {
                    // 单个URL，转换为JSON数组
                    try {
                        bo.setOtherVideoMaterials(objectMapper.writeValueAsString(Arrays.asList(otherVideoMaterialsStr)));
                    } catch (JsonProcessingException e) {
                        log.error("转换其他视频素材为JSON失败", e);
                    }
                }
            }
            // 如果已经是JSON格式，保持不变
        }
    }
    
    /**
     * 处理目标文本 - 将使用&&分隔的多段文本转换为JSON格式
     * 
     * @param bo 数据对象
     */
    @Override
    public void processTargetText(MediaDigitalLensBo bo) {
        try {
            // 获取目标文本
            String targetText = bo.getTargetText();
            if (StringUtils.isNotBlank(targetText) && targetText.contains("&&")) {
                // 使用&&分割文本
                String[] textSegments = targetText.split("&&");
                
                // 去除每段文本的前后空格
                for (int i = 0; i < textSegments.length; i++) {
                    textSegments[i] = textSegments[i].trim();
                }
                
                // 转换为JSON格式
                List<String> textList = Arrays.asList(textSegments);
                String jsonText = new ObjectMapper().writeValueAsString(textList);
                
                // 设置转换后的目标文本
                bo.setTargetText(jsonText);
                
                log.info("目标文本已转换为JSON格式: {}", jsonText);
            }
        } catch (Exception e) {
            log.error("处理目标文本失败", e);
        }
    }
    
    /**
     * 处理素材分离 - 将素材根据类型分离到视频和音频字段
     */
    @Override
    public void processMaterialsSeparation(MediaDigitalLensBo bo) {
        // 如果前端传入了旧的otherMaterials字段（兼容旧版本），则进行分离处理
        if (bo.getOtherVideoMaterials() == null && bo.getOtherAudioMaterials() == null) {
            // 获取原始的其他素材字段
            String otherMaterials = bo.getOtherMaterials();
            if (StringUtils.isNotBlank(otherMaterials)) {
                try {
                    List<String> videoMaterials = new ArrayList<>();
                    List<String> audioMaterials = new ArrayList<>();
                    
                    // 解析素材列表
                    List<String> materialsList;
                    if (otherMaterials.startsWith("[")) {
                        // JSON格式
                        materialsList = new ObjectMapper().readValue(otherMaterials, new TypeReference<List<String>>() {});
                    } else if (otherMaterials.contains(",")) {
                        // 逗号分隔
                        materialsList = Arrays.asList(otherMaterials.split(","));
                    } else {
                        // 单个URL
                        materialsList = Collections.singletonList(otherMaterials);
                    }
                    
                    // 根据扩展名分类
                    for (String material : materialsList) {
                        String lowerCaseMaterial = material.toLowerCase();
                        boolean isVideo = false;
                        boolean isAudio = false;
                        
                        // 检查是否为视频
                        for (String ext : ALLOWED_VIDEO_EXTENSIONS) {
                            if (lowerCaseMaterial.endsWith(ext)) {
                                isVideo = true;
                                break;
                            }
                        }
                        
                        // 检查是否为音频
                        if (!isVideo) {
                            for (String ext : ALLOWED_AUDIO_EXTENSIONS) {
                                if (lowerCaseMaterial.endsWith(ext)) {
                                    isAudio = true;
                                    break;
                                }
                            }
                        }
                        
                        // 添加到相应列表
                        if (isVideo) {
                            videoMaterials.add(material);
                        } else if (isAudio) {
                            audioMaterials.add(material);
                        }
                    }
                    
                    // 设置分离后的字段
                    if (!videoMaterials.isEmpty()) {
                        bo.setOtherVideoMaterials(String.join(",", videoMaterials));
                    }
                    
                    if (!audioMaterials.isEmpty()) {
                        bo.setOtherAudioMaterials(String.join(",", audioMaterials));
                    }
                    
                } catch (Exception e) {
                    log.error("处理素材分离失败", e);
                }
            }
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MediaDigitalLens entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除digitalLens信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
