package com.dock.service;

import com.dock.domain.vo.MediaVideoRepertoryVo;
import com.dock.domain.bo.MediaVideoRepertoryBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * videoService接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IMediaVideoRepertoryService {

    /**
     * 查询video
     *
     * @param id 主键
     * @return video
     */
    MediaVideoRepertoryVo queryById(Long id);

    /**
     * 分页查询video列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return video分页列表
     */
    TableDataInfo<MediaVideoRepertoryVo> queryPageList(MediaVideoRepertoryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的video列表
     *
     * @param bo 查询条件
     * @return video列表
     */
    List<MediaVideoRepertoryVo> queryList(MediaVideoRepertoryBo bo);

    /**
     * 新增video
     *
     * @param bo video
     * @return 是否新增成功
     */
    Boolean insertByBo(MediaVideoRepertoryBo bo);

    /**
     * 修改video
     *
     * @param bo video
     * @return 是否修改成功
     */
    Boolean updateByBo(MediaVideoRepertoryBo bo);

    /**
     * 校验并批量删除video信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
