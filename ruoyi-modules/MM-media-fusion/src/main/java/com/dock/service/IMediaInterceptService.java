package com.dock.service;

import com.dock.domain.SubtitleStyle;
import com.dock.domain.VideoInfoResponse;

import java.util.List;

/**
 * mediaService接口
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface IMediaInterceptService {

    /**
     * 截取视频第一帧并添加字幕预览
     *
     * @param videoUrl 视频URL
     * @param subtitleStyle 字幕位置
     * @return 带字幕预览的图片base64列表
     */
    public List<String> interceptAspect(String videoUrl, int x, int y, SubtitleStyle subtitleStyle);

    /**
     * 获取视频信息
     *
     * @param videoUrl 视频URL
     * @return 视频信息
     */
    public VideoInfoResponse getVideoInfo(String videoUrl);
}
