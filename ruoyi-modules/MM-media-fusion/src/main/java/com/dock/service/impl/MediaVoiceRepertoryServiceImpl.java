package com.dock.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MediaVoiceRepertoryBo;
import com.dock.domain.vo.MediaVoiceRepertoryVo;
import com.dock.domain.MediaVoiceRepertory;
import com.dock.mapper.MediaVoiceRepertoryMapper;
import com.dock.service.IMediaVoiceRepertoryService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * voiceService业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MediaVoiceRepertoryServiceImpl implements IMediaVoiceRepertoryService {

    private final MediaVoiceRepertoryMapper baseMapper;

    /**
     * 查询voice
     *
     * @param id 主键
     * @return voice
     */
    @Override
    public MediaVoiceRepertoryVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询voice列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return voice分页列表
     */
    @Override
    public TableDataInfo<MediaVoiceRepertoryVo> queryPageList(MediaVoiceRepertoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MediaVoiceRepertory> lqw = buildQueryWrapper(bo);
        Page<MediaVoiceRepertoryVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的voice列表
     *
     * @param bo 查询条件
     * @return voice列表
     */
    @Override
    public List<MediaVoiceRepertoryVo> queryList(MediaVoiceRepertoryBo bo) {
        LambdaQueryWrapper<MediaVoiceRepertory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MediaVoiceRepertory> buildQueryWrapper(MediaVoiceRepertoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MediaVoiceRepertory> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MediaVoiceRepertory::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getViocePath()), MediaVoiceRepertory::getViocePath, bo.getViocePath());
        return lqw;
    }

    /**
     * 新增voice
     *
     * @param bo voice
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MediaVoiceRepertoryBo bo) {
        MediaVoiceRepertory add = MapstructUtils.convert(bo, MediaVoiceRepertory.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改voice
     *
     * @param bo voice
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MediaVoiceRepertoryBo bo) {
        MediaVoiceRepertory update = MapstructUtils.convert(bo, MediaVoiceRepertory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MediaVoiceRepertory entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除voice信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
