package com.dock.config;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.dock.domain.MediaDigitalLens;
import com.dock.domain.MediaTask;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * 自定义ID生成器
 * 对特定实体使用数据库自增，其他实体使用雪花算法
 *
 * <AUTHOR>
 */
@Component
@Primary
public class CustomIdGenerator implements IdentifierGenerator {

    private final DefaultIdentifierGenerator defaultGenerator = new DefaultIdentifierGenerator();

    @Override
    public Number nextId(Object entity) {
        // 对于MediaDigitalLens和MediaTask使用数据库自增
        if (entity instanceof MediaDigitalLens || entity instanceof MediaTask) {
            return null; // 返回null让数据库自增
        }
        
        // 其他实体使用雪花算法
        return defaultGenerator.nextId(entity);
    }

    @Override
    public String nextUUID(Object entity) {
        return defaultGenerator.nextUUID(entity);
    }
}
