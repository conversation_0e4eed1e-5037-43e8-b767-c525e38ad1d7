package com.dock.domain.bo;

import com.dock.domain.MediaVoiceRepertory;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * voice业务对象 media_voice_repertory
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MediaVoiceRepertory.class, reverseConvertGenerate = false)
public class MediaVoiceRepertoryBo extends BaseEntity {

    /**
     * 音频库id
     */
    private Long id;

    /**
     * 音频地址
     */
    @NotBlank(message = "音频地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String viocePath;
    
    /**
     * 音频名称
     */
    private String vioceName;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
