package com.dock.domain.vo;

import com.dock.domain.MediaVoiceRepertory;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * voice视图对象 media_voice_repertory
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MediaVoiceRepertory.class)
public class MediaVoiceRepertoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 音频库id
     */
    @ExcelProperty(value = "音频库id")
    private Long id;

    /**
     * 音频地址
     */
    @ExcelProperty(value = "音频地址")
    private String viocePath;
    
    /**
     * 音频名称
     */
    @ExcelProperty(value = "音频名称")
    private String vioceName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
