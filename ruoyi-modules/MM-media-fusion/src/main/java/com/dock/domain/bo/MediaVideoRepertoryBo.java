package com.dock.domain.bo;

import com.dock.domain.MediaVideoRepertory;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * video业务对象 media_video_repertory
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MediaVideoRepertory.class, reverseConvertGenerate = false)
public class MediaVideoRepertoryBo extends BaseEntity {

    /**
     * 视频库id
     */
    private Long id;

    /**
     * 视频地址
     */
    @NotBlank(message = "视频地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String videoPath;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
