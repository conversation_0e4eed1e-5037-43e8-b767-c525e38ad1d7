package com.dock.domain.vo;

import com.dock.domain.MediaDigitalLens;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * digitalLens视图对象 media_digital_lens
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MediaDigitalLens.class)
public class MediaDigitalLensVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 人物视频URL
     */
    @ExcelProperty(value = "人物视频URL")
    private String characterVideo;

    /**
     * 克隆音色URL
     */
    @ExcelProperty(value = "克隆音色URL")
    private String clonedVoice;

    /**
     * 参考文本，用于语音合成参考
     */
    @ExcelProperty(value = "参考文本，用于语音合成参考")
    private String referenceText;

    /**
     * 目标文本，用于语音合成输出
     */
    @ExcelProperty(value = "目标文本，用于语音合成输出")
    private String targetText;

    /**
     * 其他视频素材URL列表
     */
    @ExcelProperty(value = "其他视频素材URL列表")
    private String otherVideoMaterials;

    /**
     * 其他音频素材URL列表
     */
    @ExcelProperty(value = "其他音频素材URL列表")
    private String otherAudioMaterials;
    
    /**
     * 旧版本兼容字段 - 其他素材URL
     * 用于兼容旧版本前端
     */
    @ExcelProperty(value = "其他素材URL")
    private String otherMaterials;

    /**
     * 状态：0-待处理，1-成功，2-处理中，3-失败
     */
    @ExcelProperty(value = "状态")
    private Integer status;

    /**
     * 关联任务UUID
     */
    @ExcelProperty(value = "关联任务UUID")
    private String taskId;

    /**
     * 字幕位置
     */
    @ExcelProperty(value = "字幕位置")
    private String subtitlePosition;
}
