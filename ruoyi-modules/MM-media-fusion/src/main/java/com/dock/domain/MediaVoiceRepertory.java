package com.dock.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * voice对象 media_voice_repertory
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("media_voice_repertory")
public class MediaVoiceRepertory extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 音频库id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 音频地址
     */
    private String viocePath;
    
    /**
     * 音频名称
     */
    private String vioceName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
