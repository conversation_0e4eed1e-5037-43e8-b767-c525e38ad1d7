package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * media对象 media_task
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("media_task")
public class MediaTask extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 任务UUID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 视频标题
     */
    private String videoTitle;

    /**
     * 固定字幕内容
     */
    private String fixedSubtitle;

    /**
     * 字幕位置
     */
    private String subtitlePosition;

    /**
     * 关联字幕
     */
    private String relatedSubtitle;

    /**
     * 配音类型（1-普通配音 2-克隆音色）
     */
    private String voiceType;

    /**
     * 预设音色名称
     */
    private String voicePreset;

    /**
     * 克隆音色文件（JSON格式，存储音频URL）
     */
    private String cloneVoiceFile;

    /**
     * 克隆音色参考文本
     */
    private String cloneVoiceReferenceText;

    /**
     * 克隆音色目标文本
     */
    private String cloneVoiceTargetText;

    /**
     * 视频素材列表（JSON格式，存储多个视频URL）
     */
    private String videoMaterials;

    /**
     * 截取数量
     */
    private String clipCount;

    /**
     * 截取时长
     */
    private String clipDuration;

    /**
     * 背景音乐素材列表（JSON格式，存储多个音频URL）
     */
    private String backgroundMusic;
    
    /**
     * 镜头类型（1-固定镜头 2-指定镜头）
     */
    private String shotType;

    /**
     * 任务状态（0-待处理 1-处理中 2-已完成 3-失败）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
