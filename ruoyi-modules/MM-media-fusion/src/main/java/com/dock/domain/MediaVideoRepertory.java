package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * video对象 media_video_repertory
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("media_video_repertory")
public class MediaVideoRepertory extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频库id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 视频地址
     */
    private String videoPath;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
