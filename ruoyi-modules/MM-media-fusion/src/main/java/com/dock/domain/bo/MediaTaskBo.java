package com.dock.domain.bo;

import com.dock.domain.MediaTask;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * media业务对象 media_task
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MediaTask.class, reverseConvertGenerate = false)
public class MediaTaskBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务UUID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 视频标题
     */
    private String videoTitle;

    /**
     * 固定字幕内容
     */
//    @NotBlank(message = "固定字幕内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fixedSubtitle;

    /**
     * 字幕位置
     */
    private String subtitlePosition;

    /**
     * 关联字幕
     */
//    @NotBlank(message = "关联字幕不能为空", groups = { AddGroup.class, EditGroup.class })
    private String relatedSubtitle;

    /**
     * 配音类型（1-普通配音 2-克隆音色）
     */
//    @NotBlank(message = "配音类型（1-普通配音 2-克隆音色）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String voiceType;

    /**
     * 预设音色名称
     */
//    @NotBlank(message = "预设音色名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String voicePreset;

    /**
     * 克隆音色文件（JSON格式或字符串，存储音频URL）
     */
//    @NotBlank(message = "克隆音色文件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cloneVoiceFile;

    /**
     * 克隆音色参考文本
     */
//    @NotBlank(message = "克隆音色参考文本不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cloneVoiceReferenceText;

    /**
     * 克隆音色目标文本
     */
//    @NotBlank(message = "克隆音色目标文本不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cloneVoiceTargetText;

    /**
     * 视频素材列表（JSON格式或逗号分隔的URL字符串，存储多个视频URL）
     */
//    @NotBlank(message = "视频素材列表不能为空", groups = { AddGroup.class, EditGroup.class })
    private String videoMaterials;

    /**
     * 截取数量
     */
//    @NotBlank(message = "截取数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String clipCount;

    /**
     * 截取时长
     */
//    @NotBlank(message = "截取时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private String clipDuration;

    /**
     * 背景音乐素材列表（JSON格式或逗号分隔的URL字符串，存储多个音频URL）
     */
//    @NotBlank(message = "背景音乐素材列表不能为空", groups = { AddGroup.class, EditGroup.class })
    private String backgroundMusic;
    
    /**
     * 镜头类型（1-固定镜头 2-指定镜头）
     */
    private String shotType;

    /**
     * 任务状态（0-待处理 1-处理中 2-已完成 3-失败）
     */
//    @NotBlank(message = "任务状态（0-待处理 1-处理中 2-已完成 3-失败）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
//    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
