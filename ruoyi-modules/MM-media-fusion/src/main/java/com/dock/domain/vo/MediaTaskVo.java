package com.dock.domain.vo;

import com.dock.domain.MediaTask;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * media视图对象 media_task
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MediaTask.class)
public class MediaTaskVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 任务UUID
     */
    @ExcelProperty(value = "任务UUID")
    private String taskId;

    /**
     * 任务名称
     */
    @ExcelProperty(value = "任务名称")
    private String taskName;

    /**
     * 视频标题
     */
    @ExcelProperty(value = "视频标题")
    private String videoTitle;

    /**
     * 固定字幕内容
     */
    @ExcelProperty(value = "固定字幕内容")
    private String fixedSubtitle;

    /**
     * 字幕位置
     */
    @ExcelProperty(value = "字幕位置")
    private String subtitlePosition;

    /**
     * 关联字幕
     */
    @ExcelProperty(value = "关联字幕")
    private String relatedSubtitle;

    /**
     * 配音类型（1-普通配音 2-克隆音色）
     */
    @ExcelProperty(value = "配音类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-普通配音,2=-克隆音色")
    private String voiceType;

    /**
     * 预设音色名称
     */
    @ExcelProperty(value = "预设音色名称")
    private String voicePreset;

    /**
     * 克隆音色文件（JSON格式，存储音频URL）
     */
    @ExcelProperty(value = "克隆音色文件")
    private String cloneVoiceFile;

    /**
     * 克隆音色参考文本
     */
    @ExcelProperty(value = "克隆音色参考文本")
    private String cloneVoiceReferenceText;

    /**
     * 克隆音色目标文本
     */
    @ExcelProperty(value = "克隆音色目标文本")
    private String cloneVoiceTargetText;

    /**
     * 视频素材列表（JSON格式，存储多个视频URL）
     */
    @ExcelProperty(value = "视频素材列表")
    private String videoMaterials;

    /**
     * 截取数量
     */
    @ExcelProperty(value = "截取数量")
    private String clipCount;

    /**
     * 截取时长
     */
    @ExcelProperty(value = "截取时长")
    private String clipDuration;

    /**
     * 背景音乐素材列表（JSON格式，存储多个音频URL）
     */
    @ExcelProperty(value = "背景音乐素材列表")
    private String backgroundMusic;
    
    /**
     * 镜头类型（1-固定镜头 2-指定镜头）
     */
    @ExcelProperty(value = "镜头类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-固定镜头,2=-指定镜头")
    private String shotType;

    /**
     * 任务状态（0-待处理 1-处理中 2-已完成 3-失败）
     */
    @ExcelProperty(value = "任务状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-待处理,1=-处理中,2=-已完成,3=-失败")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
