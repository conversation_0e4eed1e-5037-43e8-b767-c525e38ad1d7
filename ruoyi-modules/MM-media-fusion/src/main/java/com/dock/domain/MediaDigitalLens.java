package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * digitalLens对象 media_digital_lens
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("media_digital_lens")
public class MediaDigitalLens extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 删除标记(0-正常,1-已删除)，逻辑删除字段
     */
    @TableLogic
    private Long delFlag;

    /**
     * 乐观锁版本号，用于并发控制
     */
    @Version
    private Long version;

    /**
     * 标题
     */
    private String title;

    /**
     * 人物视频URL
     */
    private String characterVideo;

    /**
     * 克隆音色URL
     */
    private String clonedVoice;

    /**
     * 参考文本，用于语音合成参考
     */
    private String referenceText;

    /**
     * 目标文本，用于语音合成输出
     */
    private String targetText;

    /**
     * 其他视频素材URL列表，格式：video_视频URL,video_另一视频URL
     */
    private String otherVideoMaterials;

    /**
     * 其他音频素材URL列表，格式：audio_音频URL,audio_另一音频URL
     */
    private String otherAudioMaterials;

    /**
     * 状态：0-待处理，1-成功，2-处理中，3-失败
     */
    private Integer status;

    /**
     * 关联任务UUID
     */
    private String taskId;

    /**
     * 字幕位置
     */
    private String subtitlePosition;
}
