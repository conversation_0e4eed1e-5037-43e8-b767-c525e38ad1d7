package com.dock.domain;


import lombok.Data;

import java.util.List;

/**
 * 字幕样式配置类，用于控制字幕的字体、颜色、位置、样式等渲染细节
 */
@Data
public class SubtitleStyle {

    private String fontName;   // 字体名称，如 "Arial"
    private Integer fontSize;  // 字体大小（pt单位），如 36
    private String fontStyle;  // 预留字段，如 "bold"、"italic"，可由下面的 bold/italic 字段代替
    private Integer r;         // 字体颜色 R（0-255）
    private Integer g;         // 字体颜色 G（0-255）
    private Integer b;         // 字体颜色 B（0-255）
    private String text;       // 要绘制的字幕内容，如 "这是预览字幕"

    private List<String> lines;

    private String hexadecimal;// 将rgb转为十六进制颜色


//    private Integer bold;         // 是否加粗：true = 加粗，false/null = 不加粗
//    private Integer italic;       // 是否斜体：true = 斜体
//    private Integer underline;    // 是否下划线（目前未实现）
//    private Integer strikeOut;    // 是否删除线（目前未实现）
//
//    private Integer scaleX;       // 水平缩放百分比，例如 100 表示不缩放
//    private Integer scaleY;       // 垂直缩放百分比
//    private Integer spacing;      // 字符间距（px）
//
//    private Integer angle;        // 旋转角度（目前未实现）
//    private Integer borderStyle;  // 边框样式（1=边框+阴影，3=不透明背景块，预留）
//
//    private Integer outline;      // 描边宽度（px）
//    private Integer shadow;       // 阴影大小（px，预留）
//
//    private Integer alignment;    // 对齐方式（1=左下，2=中下，3=右下，4=左中，5=正中，6=右中，7=左上，8=中上，9=右上）
//    private Integer marginL;      // 左边距
//    private Integer marginR;      // 右边距
//    private Integer marginV;      // 垂直边距（底部/顶部）
//
//    private Integer encoding;     // 字符编码（预留）
}
