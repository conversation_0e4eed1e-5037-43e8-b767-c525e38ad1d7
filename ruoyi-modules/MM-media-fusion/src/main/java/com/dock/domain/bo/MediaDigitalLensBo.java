package com.dock.domain.bo;

import com.dock.domain.MediaDigitalLens;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * digitalLens业务对象 media_digital_lens
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MediaDigitalLens.class, reverseConvertGenerate = false)
public class MediaDigitalLensBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 人物视频URL
     */
    @NotBlank(message = "人物视频URL不能为空", groups = { AddGroup.class, EditGroup.class })
    private String characterVideo;

    /**
     * 克隆音色URL
     */
    @NotBlank(message = "克隆音色URL不能为空", groups = { AddGroup.class, EditGroup.class })
    private String clonedVoice;

    /**
     * 参考文本，用于语音合成参考
     */
    @NotBlank(message = "参考文本，用于语音合成参考不能为空", groups = { AddGroup.class, EditGroup.class })
    private String referenceText;

    /**
     * 目标文本，用于语音合成输出
     */
    @NotBlank(message = "目标文本，用于语音合成输出不能为空", groups = { AddGroup.class, EditGroup.class })
    private String targetText;

    /**
     * 其他视频素材URL列表
     */
    private String otherVideoMaterials;

    /**
     * 其他音频素材URL列表
     */
    private String otherAudioMaterials;
    
    /**
     * 旧版本兼容字段 - 其他素材URL
     * 仅用于接收旧版本前端传递的数据，不会被映射到实体类
     */
    @JsonIgnore
    private String otherMaterials;

    /**
     * 状态：0-待处理，1-成功，2-处理中，3-失败
     */
    private Integer status;

    /**
     * 关联任务UUID
     */
    private String taskId;

    /**
     * 字幕位置
     */
    private String subtitlePosition;
}
