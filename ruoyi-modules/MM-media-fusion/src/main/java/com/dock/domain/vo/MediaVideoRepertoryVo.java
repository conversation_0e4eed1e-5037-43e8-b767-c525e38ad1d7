package com.dock.domain.vo;

import com.dock.domain.MediaVideoRepertory;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * video视图对象 media_video_repertory
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MediaVideoRepertory.class)
public class MediaVideoRepertoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频库id
     */
    @ExcelProperty(value = "视频库id")
    private Long id;

    /**
     * 视频地址
     */
    @ExcelProperty(value = "视频地址")
    private String videoPath;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
