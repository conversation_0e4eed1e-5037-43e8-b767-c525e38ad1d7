package com.dock.controller;

import com.dock.domain.RequestDTO;
import com.dock.domain.bo.MediaTaskBo;
import com.dock.utils.MarkdownUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.dromara.common.core.domain.R;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/media/aiAsk")
@Slf4j
public class MediaAIController {

    private final RestTemplate restTemplate;

//    private static final String GEMINI_API_URL ="https://steamgovernment.deno.dev/v1/chat/completions";
//
//    private static final String GEMINI_API_KEY = "AIzaSyCLojBNOvdm0YEpsRVwiOJxbDGupwjg6nw";
//
//    private static final String GEMINI_API_MODEL = "gemini-2.5-flash";
//
//    private static final String PROMPT = "请根据以下句子，润色并返回5个不同版本的结果，每个版本只包含润色后的文本内容，不需要任何解释、点评或编号，输出格式为JSON数组。例如：[\"版本1\", \"版本2\", ...]。\n句子：";

    @Value("${gemini.api.url}")
    private String apiUrl;

    @Value("${gemini.api.key}")
    private String apiKey;

    @Value("${gemini.api.model}")
    private String apiModel;

    @Value("${gemini.prompt.title}")
    private String promptTitle;

    @Value("${gemini.prompt.subtitle}")
    private String promptSubtitle;

    @Value("${gemini.prompt.target_text}")
    private String promptTargetText;


    @PostMapping
    public String aiAsk(@RequestBody RequestDTO dto) {
        String question = dto.getRequest();
        log.info("收到需要润色的文本: {}", question);
        String result = callGeminiApiToPolish(question,
                apiUrl,
                apiKey,
                apiModel,
                promptTitle);
        log.info("润色后的文本: {}", result);
        String markdownTags = MarkdownUtils.removeMarkdownTags(result);
        log.info("去除Markdown标签后的文本: {}", markdownTags);
        String cleanResult = cleanJsonString(markdownTags);
        log.info("最终返回的干净JSON字符串: {}", cleanResult);
        return cleanResult;
//        return R.ok(markdownTags);
    }


    @PostMapping("aiSubtitle")
    public String aiSubtitle(@RequestBody MediaTaskBo bo) {
        String videoTitle = bo.getVideoTitle();
        String result = callGeminiApiToPolish(videoTitle,
                apiUrl,
                apiKey,
                apiModel,
                promptSubtitle);
        String markdownTags = MarkdownUtils.removeMarkdownTags(result);
        return cleanJsonString(markdownTags);
    }


    @PostMapping("aiTargetText")
    public String aiTargetText(@RequestBody MediaTaskBo bo) {
        String videoTitle = bo.getVideoTitle();
        String result = callGeminiApiToPolish(videoTitle,
                apiUrl,
                apiKey,
                apiModel,
                promptTargetText);
        String markdownTags = MarkdownUtils.removeMarkdownTags(result);
        return cleanJsonString(markdownTags);
    }






    private String callGeminiApiToPolish(String text,
                                         String apiUrl,
                                         String apiKey,
                                         String apiModel,
                                         String prompt) {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // Authorization用Bearer加API KEY
        headers.set("Authorization", "Bearer " + apiKey);

        // 构造请求体，messages结构符合Gemini官方chat格式
        Map<String, Object> requestBody = Map.of(
                "model", apiModel,
                "messages", List.of(
                        Map.of("role", "user", "content", prompt + text)
                ),
                "temperature", 0.7
        );

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, requestEntity, Map.class);
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<?, ?> body = response.getBody();
                List<?> choices = (List<?>) body.get("choices");
                if (choices != null && !choices.isEmpty()) {
                    Map<?, ?> firstChoice = (Map<?, ?>) choices.get(0);
                    Map<?, ?> message = (Map<?, ?>) firstChoice.get("message");
                    return (String) message.get("content");
                }
            } else {
                log.error("调用Gemini接口失败，状态码: {}", response.getStatusCodeValue());
            }
        } catch (Exception e) {
            log.error("调用Gemini接口异常", e);
        }
        return "润色失败，请稍后重试。";
    }


    public static String cleanJsonString(String json) {
        if (json == null) return "";
        return json
                .replace("\\n", "")      // 去除换行转义符
                .replace("\\", "")       // 去除所有反斜杠
                .trim();                 // 去除首尾空白
    }




}
