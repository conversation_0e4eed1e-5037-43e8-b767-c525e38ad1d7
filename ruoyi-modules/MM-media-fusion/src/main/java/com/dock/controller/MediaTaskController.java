package com.dock.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.dromara.system.domain.vo.SysOssUploadVo;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MediaTaskVo;
import com.dock.domain.bo.MediaTaskBo;
import com.dock.service.IMediaTaskService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.ArrayList;

/**
 * 媒体任务处理
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/media/task")
@Slf4j
public class MediaTaskController extends BaseController {

    private final IMediaTaskService mediaTaskService;
    private final ISysOssService ossService;

    // 允许的视频文件类型
    private static final String[] ALLOWED_VIDEO_EXTENSIONS = {
        ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", 
        ".webm", ".m4v", ".3gp", ".ts", ".mts", ".m2ts"
    };
    // 允许的音频文件类型
    private static final String[] ALLOWED_AUDIO_EXTENSIONS = {
        ".mp3", ".wav", ".ogg", ".aac", 
        ".flac", ".m4a", ".wma", ".aiff", ".alac"
    };

    /**
     * 查询媒体任务列表
     */
    @SaCheckPermission("media:task:list")
    @GetMapping("/list")
    public TableDataInfo<MediaTaskVo> list(MediaTaskBo bo, PageQuery pageQuery) {
        return mediaTaskService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出媒体任务列表
     */
    @SaCheckPermission("media:task:export")
    @Log(title = "媒体任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MediaTaskBo bo, HttpServletResponse response) {
        List<MediaTaskVo> list = mediaTaskService.queryList(bo);
        ExcelUtil.exportExcel(list, "媒体任务", MediaTaskVo.class, response);
    }

    /**
     * 获取媒体任务详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("media:task:query")
    @GetMapping("/{id}")
    public R<MediaTaskVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mediaTaskService.queryById(id));
    }

    /**
     * 新增媒体任务
     */
    @SaCheckPermission("media:task:add")
    @Log(title = "媒体任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MediaTaskBo bo) {
        // 默认设置和处理逻辑已移至Service层
        return toAjax(mediaTaskService.insertByBo(bo));
    }

    /**
     * 批量新增媒体任务
     */
    @SaCheckPermission("media:task:add")
    @Log(title = "媒体任务批量新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batch")
    public R<Void> batchAdd(@Validated(AddGroup.class) @RequestBody List<MediaTaskBo> bos) {
        return toAjax(mediaTaskService.batchInsertByBos(bos));
    }

    /**
     * 修改媒体任务
     */
    @SaCheckPermission("media:task:edit")
    @Log(title = "媒体任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MediaTaskBo bo) {
        // 默认设置和处理逻辑已移至Service层
        return toAjax(mediaTaskService.updateByBo(bo));
    }

    /**
     * 删除媒体任务
     *
     * @param ids 主键串
     */
    @SaCheckPermission("media:task:remove")
    @Log(title = "媒体任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mediaTaskService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 上传单个视频或音频文件到OSS对象存储
     *
     * @param file 文件
     */
    @Log(title = "OSS对象存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysOssUploadVo> upload(@RequestPart("file") MultipartFile file) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }
        
        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename != null) {
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
            boolean isValidFileType = Arrays.asList(ALLOWED_VIDEO_EXTENSIONS).contains(fileExtension) || 
                                     Arrays.asList(ALLOWED_AUDIO_EXTENSIONS).contains(fileExtension);
            
            if (!isValidFileType) {
                return R.fail("不支持的文件类型，请上传视频(.mp4, .avi, .mov, .wmv, .flv, .mkv, .webm, .m4v, .3gp, .ts, .mts, .m2ts)或音频(.mp3, .wav, .ogg, .aac, .flac, .m4a, .wma, .aiff, .alac)文件");
            }
        }
        
//        // 检查文件大小
//        long fileSizeInMB = file.getSize() / (1024 * 1024);
//        if (fileSizeInMB > 500) { // 限制500MB
//            return R.fail("文件大小不能超过500MB");
//        }
        
        // 使用系统OSS服务上传文件到MinIO
        SysOssVo oss = ossService.upload(file);
        SysOssUploadVo uploadVo = new SysOssUploadVo();
        uploadVo.setUrl(oss.getUrl());
        uploadVo.setFileName(oss.getOriginalName());
        uploadVo.setOssId(oss.getOssId().toString());
        return R.ok(uploadVo);
    }

    /**
     * 批量上传多个视频或音频文件到OSS对象存储
     *
     * @param files 多个文件
     */
    @Log(title = "OSS对象存储批量上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/batch-upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<List<SysOssUploadVo>> batchUpload(@RequestPart("files") MultipartFile[] files) {
        if (ObjectUtil.isNull(files) || files.length == 0) {
            return R.fail("上传文件不能为空");
        }
        
        List<SysOssUploadVo> uploadResults = new ArrayList<>();
        
        for (MultipartFile file : files) {
            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename != null) {
                String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
                boolean isValidFileType = Arrays.asList(ALLOWED_VIDEO_EXTENSIONS).contains(fileExtension) || 
                                         Arrays.asList(ALLOWED_AUDIO_EXTENSIONS).contains(fileExtension);
                
                if (!isValidFileType) {
                    continue; // 跳过不支持的文件类型
                }
            }
            
//            // 检查文件大小
//            long fileSizeInMB = file.getSize() / (1024 * 1024);
//            if (fileSizeInMB > 500) { // 限制500MB
//                continue; // 跳过过大的文件
//            }
//
            try {
                // 使用系统OSS服务上传文件到MinIO
                SysOssVo oss = ossService.upload(file);
                SysOssUploadVo uploadVo = new SysOssUploadVo();
                uploadVo.setUrl(oss.getUrl());
                uploadVo.setFileName(oss.getOriginalName());
                uploadVo.setOssId(oss.getOssId().toString());
                uploadResults.add(uploadVo);
            } catch (Exception e) {
                // 记录错误但继续处理其他文件
                log.error("上传文件失败: " + originalFilename, e);
            }
        }
        
        if (uploadResults.isEmpty()) {
            return R.fail("所有文件上传失败");
        }
        
        return R.ok(uploadResults);
    }

    /**
     * 预览视频或音频文件
     * 
     * @param ossId OSS对象ID
     */
    @GetMapping("/preview/{ossId}")
    public void preview(@PathVariable Long ossId, HttpServletResponse response) throws IOException {
        ossService.download(ossId, response);
    }
    
    /**
     * 根据URL预览媒体文件
     * 
     * @param url MinIO URL地址
     */
    @GetMapping("/preview")
    public R<String> previewByUrl(@RequestParam String url) {
        if (ObjectUtil.isEmpty(url)) {
            return R.fail("预览URL不能为空");
        }
        
        // 直接返回URL，前端可以用video或audio标签直接播放
        return R.ok(url);
    }
}
