package com.dock.controller;


import com.dock.domain.SubtitleRequest;
import com.dock.domain.VideoInfoRequest;
import com.dock.domain.VideoInfoResponse;
import com.dock.service.IMediaInterceptService;
import com.dock.service.IMediaTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 从上传的视频中截取片段并生成字幕预览
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/media/intercept")
@Slf4j
public class MediaInterceptController {

    private final IMediaTaskService mediaTaskService;
    private final IMediaInterceptService mediaInterceptService;

    /**
     * 获取视频第一帧并添加字幕预览
     *
     * @param request 包含视频URL和字幕位置的请求
     * @return 带字幕预览的图片base64列表
     */
    @PostMapping("/aspect")
    public R<List<String>> interceptAspect(@RequestBody SubtitleRequest request) {
        List<String> base64List = mediaInterceptService.interceptAspect(
                request.getVideoUrl(), request.getX(), request.getY(),request.getSubtitleStyle());
        return R.ok(base64List);
    }

    /**
     * 获取视频信息
     *
     * @param request 包含视频URL的请求
     * @return 视频信息
     */
    @PostMapping("/video-info")
    public R<VideoInfoResponse> getVideoInfo(@RequestBody VideoInfoRequest request) {
        VideoInfoResponse videoInfo = mediaInterceptService.getVideoInfo(request.getVideoUrl());
        return R.ok(videoInfo);
    }
}
