package com.dock.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MediaVoiceRepertoryVo;
import com.dock.domain.bo.MediaVoiceRepertoryBo;
import com.dock.service.IMediaVoiceRepertoryService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * voice
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/voice/voiceRepertory")
public class MediaVoiceRepertoryController extends BaseController {

    private final IMediaVoiceRepertoryService mediaVoiceRepertoryService;

    /**
     * 查询voice列表
     */
    @SaCheckPermission("voice:voiceRepertory:list")
    @GetMapping("/list")
    public TableDataInfo<MediaVoiceRepertoryVo> list(MediaVoiceRepertoryBo bo, PageQuery pageQuery) {
        return mediaVoiceRepertoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出voice列表
     */
    @SaCheckPermission("voice:voiceRepertory:export")
    @Log(title = "voice", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MediaVoiceRepertoryBo bo, HttpServletResponse response) {
        List<MediaVoiceRepertoryVo> list = mediaVoiceRepertoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "voice", MediaVoiceRepertoryVo.class, response);
    }

    /**
     * 获取voice详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("voice:voiceRepertory:query")
    @GetMapping("/{id}")
    public R<MediaVoiceRepertoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mediaVoiceRepertoryService.queryById(id));
    }

    /**
     * 新增voice
     */
    @SaCheckPermission("voice:voiceRepertory:add")
    @Log(title = "voice", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MediaVoiceRepertoryBo bo) {
        return toAjax(mediaVoiceRepertoryService.insertByBo(bo));
    }

    /**
     * 修改voice
     */
    @SaCheckPermission("voice:voiceRepertory:edit")
    @Log(title = "voice", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MediaVoiceRepertoryBo bo) {
        return toAjax(mediaVoiceRepertoryService.updateByBo(bo));
    }

    /**
     * 删除voice
     *
     * @param ids 主键串
     */
    @SaCheckPermission("voice:voiceRepertory:remove")
    @Log(title = "voice", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mediaVoiceRepertoryService.deleteWithValidByIds(List.of(ids), true));
    }
}
