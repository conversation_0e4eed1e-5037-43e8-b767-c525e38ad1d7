package com.dock.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MediaVideoRepertoryVo;
import com.dock.domain.bo.MediaVideoRepertoryBo;
import com.dock.service.IMediaVideoRepertoryService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * video
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/video/videoRepertory")
public class MediaVideoRepertoryController extends BaseController {

    private final IMediaVideoRepertoryService mediaVideoRepertoryService;

    /**
     * 查询video列表
     */
    @SaCheckPermission("video:videoRepertory:list")
    @GetMapping("/list")
    public TableDataInfo<MediaVideoRepertoryVo> list(MediaVideoRepertoryBo bo, PageQuery pageQuery) {
        return mediaVideoRepertoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出video列表
     */
    @SaCheckPermission("video:videoRepertory:export")
    @Log(title = "video", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MediaVideoRepertoryBo bo, HttpServletResponse response) {
        List<MediaVideoRepertoryVo> list = mediaVideoRepertoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "video", MediaVideoRepertoryVo.class, response);
    }

    /**
     * 获取video详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("video:videoRepertory:query")
    @GetMapping("/{id}")
    public R<MediaVideoRepertoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mediaVideoRepertoryService.queryById(id));
    }

    /**
     * 新增video
     */
    @SaCheckPermission("video:videoRepertory:add")
    @Log(title = "video", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MediaVideoRepertoryBo bo) {
        return toAjax(mediaVideoRepertoryService.insertByBo(bo));
    }

    /**
     * 修改video
     */
    @SaCheckPermission("video:videoRepertory:edit")
    @Log(title = "video", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MediaVideoRepertoryBo bo) {
        return toAjax(mediaVideoRepertoryService.updateByBo(bo));
    }

    /**
     * 删除video
     *
     * @param ids 主键串
     */
    @SaCheckPermission("video:videoRepertory:remove")
    @Log(title = "video", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mediaVideoRepertoryService.deleteWithValidByIds(List.of(ids), true));
    }
}
