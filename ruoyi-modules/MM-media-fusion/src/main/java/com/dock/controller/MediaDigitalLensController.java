package com.dock.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.dromara.system.domain.vo.SysOssUploadVo;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MediaDigitalLensVo;
import com.dock.domain.bo.MediaDigitalLensBo;
import com.dock.service.IMediaDigitalLensService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.ArrayList;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import java.util.Collections;

/**
 * digitalLens
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/media/digitalLens")
@Slf4j
public class MediaDigitalLensController extends BaseController {

    private final IMediaDigitalLensService mediaDigitalLensService;
    private final ISysOssService ossService;

    // 允许的视频文件类型
    private static final String[] ALLOWED_VIDEO_EXTENSIONS = {
        ".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", 
        ".webm", ".m4v", ".3gp", ".ts", ".mts", ".m2ts"
    };
    // 允许的音频文件类型
    private static final String[] ALLOWED_AUDIO_EXTENSIONS = {
        ".mp3", ".wav", ".ogg", ".aac", 
        ".flac", ".m4a", ".wma", ".aiff", ".alac"
    };

    /**
     * 查询digitalLens列表
     */
    @SaCheckPermission("media:digitalLens:list")
    @GetMapping("/list")
    public TableDataInfo<MediaDigitalLensVo> list(MediaDigitalLensBo bo, PageQuery pageQuery) {
        return mediaDigitalLensService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出digitalLens列表
     */
    @SaCheckPermission("media:digitalLens:export")
    @Log(title = "digitalLens", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MediaDigitalLensBo bo, HttpServletResponse response) {
        List<MediaDigitalLensVo> list = mediaDigitalLensService.queryList(bo);
        ExcelUtil.exportExcel(list, "digitalLens", MediaDigitalLensVo.class, response);
    }

    /**
     * 获取digitalLens详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("media:digitalLens:query")
    @GetMapping("/{id}")
    public R<MediaDigitalLensVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mediaDigitalLensService.queryById(id));
    }

    /**
     * 新增digitalLens
     */
    @SaCheckPermission("media:digitalLens:add")
    @Log(title = "digitalLens", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MediaDigitalLensBo bo) {
        // 处理素材分离已移到Service层
        return toAjax(mediaDigitalLensService.insertByBo(bo));
    }

    /**
     * 批量新增digitalLens
     */
    @SaCheckPermission("media:digitalLens:add")
    @Log(title = "批量新增digitalLens", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batch")
    public R<Void> batchAdd(@Validated(AddGroup.class) @RequestBody List<MediaDigitalLensBo> bos) {
        return toAjax(mediaDigitalLensService.batchInsertByBos(bos));
    }

    /**
     * 修改digitalLens
     */
    @SaCheckPermission("media:digitalLens:edit")
    @Log(title = "digitalLens", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MediaDigitalLensBo bo) {
        // 处理素材分离已移到Service层
        return toAjax(mediaDigitalLensService.updateByBo(bo));
    }

    /**
     * 删除digitalLens
     *
     * @param ids 主键串
     */
    @SaCheckPermission("media:digitalLens:remove")
    @Log(title = "digitalLens", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mediaDigitalLensService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 上传文件
     */
//    @SaCheckPermission("media:digitalLens:upload")
    @Log(title = "文件上传", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public R<SysOssVo> upload(@RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("上传文件不能为空");
        }
        
        // 获取文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename != null ? 
            originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase() : "";
            
        // 检查文件类型
        boolean isVideoFile = Arrays.asList(ALLOWED_VIDEO_EXTENSIONS).contains(extension);
        boolean isAudioFile = Arrays.asList(ALLOWED_AUDIO_EXTENSIONS).contains(extension);
        
        if (!isVideoFile && !isAudioFile) {
            return R.fail("不支持的文件类型，请上传视频或音频文件");
        }
        
        // 上传到OSS
        SysOssVo oss = ossService.upload(file);
        
//
        
        return R.ok(oss);
    }

    /**
     * 批量上传多个视频或音频文件到OSS对象存储
     *
     * @param files 多个文件
     */
    @Log(title = "OSS对象存储批量上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/batch-upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<List<SysOssUploadVo>> batchUpload(@RequestPart("files") MultipartFile[] files) {
        if (ObjectUtil.isNull(files) || files.length == 0) {
            return R.fail("上传文件不能为空");
        }
        
        List<SysOssUploadVo> uploadResults = new ArrayList<>();
        
        for (MultipartFile file : files) {
            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename != null) {
                String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
                boolean isValidFileType = Arrays.asList(ALLOWED_VIDEO_EXTENSIONS).contains(fileExtension) || 
                                         Arrays.asList(ALLOWED_AUDIO_EXTENSIONS).contains(fileExtension);
                
                if (!isValidFileType) {
                    continue; // 跳过不支持的文件类型
                }
            }
            
            try {
                // 使用系统OSS服务上传文件到MinIO
                SysOssVo oss = ossService.upload(file);
                SysOssUploadVo uploadVo = new SysOssUploadVo();
                uploadVo.setUrl(oss.getUrl());
                uploadVo.setFileName(oss.getOriginalName());
                uploadVo.setOssId(oss.getOssId().toString());
                uploadResults.add(uploadVo);
            } catch (Exception e) {
                // 记录错误但继续处理其他文件
                log.error("上传文件失败: " + originalFilename, e);
            }
        }
        
        if (uploadResults.isEmpty()) {
            return R.fail("所有文件上传失败");
        }
        
        return R.ok(uploadResults);
    }

    /**
     * 预览视频或音频文件
     * 
     * @param ossId OSS对象ID
     */
    @GetMapping("/preview/{ossId}")
    public void preview(@PathVariable Long ossId, HttpServletResponse response) throws IOException {
        ossService.download(ossId, response);
    }
    
    /**
     * 根据URL预览媒体文件
     * 
     * @param url MinIO URL地址
     */
    @GetMapping("/preview")
    public R<String> previewByUrl(@RequestParam String url) {
        if (ObjectUtil.isEmpty(url)) {
            return R.fail("预览URL不能为空");
        }
        
        // 直接返回URL，前端可以用video或audio标签直接播放
        return R.ok(url);
    }
    
    // 以下方法已移到Service层
    // processTargetText()和processMaterialsSeparation()方法已移除
}
