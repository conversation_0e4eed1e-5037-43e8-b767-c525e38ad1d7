package com.dock.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MPointsPackageVo;
import com.dock.domain.bo.MPointsPackageBo;
import com.dock.service.IMPointsPackageService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 积分套餐
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/points/pointsPackage")
public class MPointsPackageController extends BaseController {

    private final IMPointsPackageService mPointsPackageService;

    /**
     * 查询积分套餐列表
     */
    @SaCheckPermission("points:pointsPackage:list")
    @GetMapping("/list")
    public TableDataInfo<MPointsPackageVo> list(MPointsPackageBo bo, PageQuery pageQuery) {
        return mPointsPackageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分套餐列表
     */
    @SaCheckPermission("points:pointsPackage:export")
    @Log(title = "积分套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MPointsPackageBo bo, HttpServletResponse response) {
        List<MPointsPackageVo> list = mPointsPackageService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分套餐", MPointsPackageVo.class, response);
    }

    /**
     * 获取积分套餐详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("points:pointsPackage:query")
    @GetMapping("/{id}")
    public R<MPointsPackageVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mPointsPackageService.queryById(id));
    }

    /**
     * 新增积分套餐
     */
    @SaCheckPermission("points:pointsPackage:add")
    @Log(title = "积分套餐", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MPointsPackageBo bo) {
        return toAjax(mPointsPackageService.insertByBo(bo));
    }

    /**
     * 修改积分套餐
     */
    @SaCheckPermission("points:pointsPackage:edit")
    @Log(title = "积分套餐", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MPointsPackageBo bo) {
        return toAjax(mPointsPackageService.updateByBo(bo));
    }

    /**
     * 删除积分套餐
     *
     * @param ids 主键串
     */
    @SaCheckPermission("points:pointsPackage:remove")
    @Log(title = "积分套餐", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mPointsPackageService.deleteWithValidByIds(List.of(ids), true));
    }
}
