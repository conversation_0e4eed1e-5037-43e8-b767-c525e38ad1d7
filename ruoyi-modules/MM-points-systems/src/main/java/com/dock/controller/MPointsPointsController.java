package com.dock.controller;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dock.domain.MPointsPoints;
import com.dock.mapper.MPointsPointsMapper;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MPointsPointsVo;
import com.dock.domain.bo.MPointsPointsBo;
import com.dock.service.IMPointsPointsService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 积分变动记录
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/points/pointsPoints")
public class MPointsPointsController extends BaseController {

    private final IMPointsPointsService mPointsPointsService;
    private final MPointsPointsMapper mPointsPointsMapper;

    /**
     * 查询积分变动记录列表
     */
    @SaCheckPermission("points:pointsPoints:list")
    @GetMapping("/list")
    public TableDataInfo<MPointsPointsVo> list(MPointsPointsBo bo, PageQuery pageQuery) {
        return mPointsPointsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分变动记录列表
     */
    @SaCheckPermission("points:pointsPoints:export")
    @Log(title = "积分变动记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MPointsPointsBo bo, HttpServletResponse response) {
        List<MPointsPointsVo> list = mPointsPointsService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分变动记录", MPointsPointsVo.class, response);
    }

    /**
     * 获取积分变动记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("points:pointsPoints:query")
    @GetMapping("/{id}")
    public R<MPointsPointsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mPointsPointsService.queryById(id));
    }

    /**
     * 新增积分变动记录
     */
    @SaCheckPermission("points:pointsPoints:add")
    @Log(title = "积分变动记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MPointsPointsBo bo) {
        return toAjax(mPointsPointsService.insertByBo(bo));
    }

    /**
     * 修改积分变动记录
     */
    @SaCheckPermission("points:pointsPoints:edit")
    @Log(title = "积分变动记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MPointsPointsBo bo) {
        return toAjax(mPointsPointsService.updateByBo(bo));
    }

    /**
     * 删除积分变动记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("points:pointsPoints:remove")
    @Log(title = "积分变动记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mPointsPointsService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 查询用户当前积分
     */
    @GetMapping("self/points")
    public R<Long> selfPoints(){
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<MPointsPoints> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MPointsPoints::getTenantId, tenantId);

        // 查询积分总额
        List<MPointsPoints> pointsList = mPointsPointsMapper.selectList(queryWrapper);
        long totalPoints = pointsList.stream()
                .mapToLong(MPointsPoints::getPointsAmount)
                .sum();
        return R.ok(totalPoints);
    }

}
