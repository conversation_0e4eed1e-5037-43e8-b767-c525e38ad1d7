package com.dock.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MPointsOrderVo;
import com.dock.domain.bo.MPointsOrderBo;
import com.dock.service.IMPointsOrderService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 积分订单
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/points/pointsOrder")
public class MPointsOrderController extends BaseController {

    private final IMPointsOrderService mPointsOrderService;

    /**
     * 查询积分订单列表
     */
    @SaCheckPermission("points:pointsOrder:list")
    @GetMapping("/list")
    public TableDataInfo<MPointsOrderVo> list(MPointsOrderBo bo, PageQuery pageQuery) {
        return mPointsOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分订单列表
     */
    @SaCheckPermission("points:pointsOrder:export")
    @Log(title = "积分订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MPointsOrderBo bo, HttpServletResponse response) {
        List<MPointsOrderVo> list = mPointsOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分订单", MPointsOrderVo.class, response);
    }

    /**
     * 获取积分订单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("points:pointsOrder:query")
    @GetMapping("/{id}")
    public R<MPointsOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mPointsOrderService.queryById(id));
    }

    /**
     * 新增积分订单
     */
    @SaCheckPermission("points:pointsOrder:add")
    @Log(title = "积分订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MPointsOrderBo bo) {
        return toAjax(mPointsOrderService.insertByBo(bo));
    }

    /**
     * 修改积分订单
     */
    @SaCheckPermission("points:pointsOrder:edit")
    @Log(title = "积分订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MPointsOrderBo bo) {
        return toAjax(mPointsOrderService.updateByBo(bo));
    }

    /**
     * 删除积分订单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("points:pointsOrder:remove")
    @Log(title = "积分订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mPointsOrderService.deleteWithValidByIds(List.of(ids), true));
    }
}
