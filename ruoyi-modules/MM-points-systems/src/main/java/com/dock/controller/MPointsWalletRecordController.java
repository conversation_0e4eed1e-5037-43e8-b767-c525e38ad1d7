package com.dock.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MPointsWalletRecordVo;
import com.dock.domain.bo.MPointsWalletRecordBo;
import com.dock.service.IMPointsWalletRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 钱包流水记录
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/points/pointsWalletRecord")
public class MPointsWalletRecordController extends BaseController {

    private final IMPointsWalletRecordService mPointsWalletRecordService;

    /**
     * 查询钱包流水记录列表
     */
    @SaCheckPermission("points:pointsWalletRecord:list")
    @GetMapping("/list")
    public TableDataInfo<MPointsWalletRecordVo> list(MPointsWalletRecordBo bo, PageQuery pageQuery) {
        return mPointsWalletRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出钱包流水记录列表
     */
    @SaCheckPermission("points:pointsWalletRecord:export")
    @Log(title = "钱包流水记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MPointsWalletRecordBo bo, HttpServletResponse response) {
        List<MPointsWalletRecordVo> list = mPointsWalletRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "钱包流水记录", MPointsWalletRecordVo.class, response);
    }

    /**
     * 获取钱包流水记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("points:pointsWalletRecord:query")
    @GetMapping("/{id}")
    public R<MPointsWalletRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mPointsWalletRecordService.queryById(id));
    }

    /**
     * 新增钱包流水记录
     */
    @SaCheckPermission("points:pointsWalletRecord:add")
    @Log(title = "钱包流水记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MPointsWalletRecordBo bo) {
        return toAjax(mPointsWalletRecordService.insertByBo(bo));
    }

    /**
     * 修改钱包流水记录
     */
    @SaCheckPermission("points:pointsWalletRecord:edit")
    @Log(title = "钱包流水记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MPointsWalletRecordBo bo) {
        return toAjax(mPointsWalletRecordService.updateByBo(bo));
    }

    /**
     * 删除钱包流水记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("points:pointsWalletRecord:remove")
    @Log(title = "钱包流水记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mPointsWalletRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
