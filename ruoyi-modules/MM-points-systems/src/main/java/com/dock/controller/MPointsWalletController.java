package com.dock.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MPointsWalletVo;
import com.dock.domain.bo.MPointsWalletBo;
import com.dock.service.IMPointsWalletService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 用户钱包
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/points/pointsWallet")
public class MPointsWalletController extends BaseController {

    private final IMPointsWalletService mPointsWalletService;

    /**
     * 查询用户钱包列表
     */
    @SaCheckPermission("points:pointsWallet:list")
    @GetMapping("/list")
    public TableDataInfo<MPointsWalletVo> list(MPointsWalletBo bo, PageQuery pageQuery) {
        return mPointsWalletService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户钱包列表
     */
    @SaCheckPermission("points:pointsWallet:export")
    @Log(title = "用户钱包", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MPointsWalletBo bo, HttpServletResponse response) {
        List<MPointsWalletVo> list = mPointsWalletService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户钱包", MPointsWalletVo.class, response);
    }

    /**
     * 获取用户钱包详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("points:pointsWallet:query")
    @GetMapping("/{id}")
    public R<MPointsWalletVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mPointsWalletService.queryById(id));
    }

    /**
     * 新增用户钱包
     */
    @SaCheckPermission("points:pointsWallet:add")
    @Log(title = "用户钱包", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MPointsWalletBo bo) {
        return toAjax(mPointsWalletService.insertByBo(bo));
    }

    /**
     * 修改用户钱包
     */
    @SaCheckPermission("points:pointsWallet:edit")
    @Log(title = "用户钱包", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MPointsWalletBo bo) {
        return toAjax(mPointsWalletService.updateByBo(bo));
    }

    /**
     * 删除用户钱包
     *
     * @param ids 主键串
     */
    @SaCheckPermission("points:pointsWallet:remove")
    @Log(title = "用户钱包", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mPointsWalletService.deleteWithValidByIds(List.of(ids), true));
    }
}
