package com.dock.domain.bo;

import com.dock.domain.MPointsPackage;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 积分套餐业务对象 m_points_package
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MPointsPackage.class, reverseConvertGenerate = false)
public class MPointsPackageBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 套餐描述
     */
    @NotBlank(message = "套餐描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String packageDesc;

    /**
     * 积分数量
     */
    private Long pointsAmount;

    /**
     * 原价
     */
    private Long originalPrice;

    /**
     * 售价
     */
    private Long salePrice;

    /**
     * 折扣率(%)
     */
    @NotNull(message = "折扣率(%)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long discountRate;

    /**
     * 赠送积分
     */
    @NotNull(message = "赠送积分不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bonusPoints;

    /**
     * 套餐类型（1普通套餐 2限时优惠 3新用户专享）
     */
    @NotBlank(message = "套餐类型（1普通套餐 2限时优惠 3新用户专享）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String packageType;

    /**
     * 是否热门（0否 1是）
     */
    @NotBlank(message = "是否热门（0否 1是）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isHot;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sortOrder;

    /**
     * 生效时间
     */
    @NotNull(message = "生效时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 失效时间
     */
    @NotNull(message = "失效时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 状态（0正常 1停用）
     */
    @NotBlank(message = "状态（0正常 1停用）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
