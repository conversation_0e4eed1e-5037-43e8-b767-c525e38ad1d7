package com.dock.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dock.domain.MPointsWallet;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户钱包视图对象 m_points_wallet
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MPointsWallet.class)
public class MPointsWalletVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 余额
     */
    @ExcelProperty(value = "余额")
    private Long balance;

    /**
     * 冻结金额
     */
    @ExcelProperty(value = "冻结金额")
    private Long frozenAmount;

    /**
     * 累计充值
     */
    @ExcelProperty(value = "累计充值")
    private Long totalRecharge;

    /**
     * 累计消费
     */
    @ExcelProperty(value = "累计消费")
    private Long totalConsume;

    /**
     * 积分余额
     */
    @ExcelProperty(value = "积分余额")
    private Long pointsBalance;

    /**
     * 累计获得积分
     */
    @ExcelProperty(value = "累计获得积分")
    private Long totalPoints;

    /**
     * 累计使用积分
     */
    @ExcelProperty(value = "累计使用积分")
    private Long usedPoints;

    /**
     * 支付密码
     */
    @ExcelProperty(value = "支付密码")
    private String payPassword;

    /**
     * 是否设置支付密码（0否 1是）
     */
    @ExcelProperty(value = "是否设置支付密码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=否,1=是")
    private String isPayPwdSet;

    /**
     * 钱包状态（0正常 1冻结 2注销）
     */
    @ExcelProperty(value = "钱包状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=冻结,2=注销")
    private String status;

    /**
     * 最后交易时间
     */
    @ExcelProperty(value = "最后交易时间")
    private Date lastTradeTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
