package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 用户钱包对象 m_points_wallet
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_points_wallet")
public class MPointsWallet extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 余额
     */
    private Long balance;

    /**
     * 冻结金额
     */
    private Long frozenAmount;

    /**
     * 累计充值
     */
    private Long totalRecharge;

    /**
     * 累计消费
     */
    private Long totalConsume;

    /**
     * 积分余额
     */
    private Long pointsBalance;

    /**
     * 累计获得积分
     */
    private Long totalPoints;

    /**
     * 累计使用积分
     */
    private Long usedPoints;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 是否设置支付密码（0否 1是）
     */
    private String isPayPwdSet;

    /**
     * 钱包状态（0正常 1冻结 2注销）
     */
    private String status;

    /**
     * 最后交易时间
     */
    private Date lastTradeTime;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Long version;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
