package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 积分变动记录对象 m_points_points
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_points_points")
public class MPointsPoints extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 变动类型（1收入 2支出）
     */
    private String changeType;

    /**
     * 变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）
     */
    private String changeReason;

    /**
     * 积分数量
     */
    private Long pointsAmount;

    /**
     * 变动前余额
     */
    private Long balanceBefore;

    /**
     * 变动后余额
     */
    private Long balanceAfter;

    /**
     * 关联ID（订单ID、消费记录ID等）
     */
    private Long relatedId;

    /**
     * 关联类型（order订单 consume消费 gift赠送）
     */
    private String relatedType;

    /**
     * 积分过期时间
     */
    private Date expireTime;

    /**
     * 描述说明
     */
    private String description;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
