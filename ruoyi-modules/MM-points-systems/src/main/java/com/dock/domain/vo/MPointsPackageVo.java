package com.dock.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dock.domain.MPointsPackage;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 积分套餐视图对象 m_points_package
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MPointsPackage.class)
public class MPointsPackageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 套餐名称
     */
    @ExcelProperty(value = "套餐名称")
    private String packageName;

    /**
     * 套餐描述
     */
    @ExcelProperty(value = "套餐描述")
    private String packageDesc;

    /**
     * 积分数量
     */
    @ExcelProperty(value = "积分数量")
    private Long pointsAmount;

    /**
     * 原价
     */
    @ExcelProperty(value = "原价")
    private Long originalPrice;

    /**
     * 售价
     */
    @ExcelProperty(value = "售价")
    private Long salePrice;

    /**
     * 折扣率(%)
     */
    @ExcelProperty(value = "折扣率(%)")
    private Long discountRate;

    /**
     * 赠送积分
     */
    @ExcelProperty(value = "赠送积分")
    private Long bonusPoints;

    /**
     * 套餐类型（1普通套餐 2限时优惠 3新用户专享）
     */
    @ExcelProperty(value = "套餐类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=普通套餐,2=限时优惠,3=新用户专享")
    private String packageType;

    /**
     * 是否热门（0否 1是）
     */
    @ExcelProperty(value = "是否热门", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=否,1=是")
    private String isHot;

    /**
     * 显示顺序
     */
    @ExcelProperty(value = "显示顺序")
    private Long sortOrder;

    /**
     * 生效时间
     */
    @ExcelProperty(value = "生效时间")
    private Date startTime;

    /**
     * 失效时间
     */
    @ExcelProperty(value = "失效时间")
    private Date endTime;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
