package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 钱包流水记录对象 m_points_wallet_record
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_points_wallet_record")
public class MPointsWalletRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 钱包ID
     */
    private Long walletId;

    /**
     * 交易流水号
     */
    private String tradeNo;

    /**
     * 交易类型（1收入 2支出）
     */
    private String tradeType;

    /**
     * 交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）
     */
    private String tradeReason;

    /**
     * 交易金额
     */
    private Long amount;

    /**
     * 交易前余额
     */
    private Long balanceBefore;

    /**
     * 交易后余额
     */
    private Long balanceAfter;

    /**
     * 关联ID
     */
    private Long relatedId;

    /**
     * 关联类型
     */
    private String relatedType;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付流水号
     */
    private String paymentNo;

    /**
     * 描述说明
     */
    private String description;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
