package com.dock.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dock.domain.MPointsOrder;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 积分订单视图对象 m_points_order
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MPointsOrder.class)
public class MPointsOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 套餐ID
     */
    @ExcelProperty(value = "套餐ID")
    private Long packageId;

    /**
     * 套餐名称
     */
    @ExcelProperty(value = "套餐名称")
    private String packageName;

    /**
     * 购买积分数量
     */
    @ExcelProperty(value = "购买积分数量")
    private Long pointsAmount;

    /**
     * 赠送积分数量
     */
    @ExcelProperty(value = "赠送积分数量")
    private Long bonusPoints;

    /**
     * 总积分数量
     */
    @ExcelProperty(value = "总积分数量")
    private Long totalPoints;

    /**
     * 原价
     */
    @ExcelProperty(value = "原价")
    private Long originalPrice;

    /**
     * 实付金额
     */
    @ExcelProperty(value = "实付金额")
    private Long payAmount;

    /**
     * 优惠金额
     */
    @ExcelProperty(value = "优惠金额")
    private Long discountAmount;

    /**
     * 支付方式（alipay支付宝 wechat微信 balance余额）
     */
    @ExcelProperty(value = "支付方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "a=lipay支付宝,w=echat微信,b=alance余额")
    private String paymentMethod;

    /**
     * 支付流水号
     */
    @ExcelProperty(value = "支付流水号")
    private String paymentNo;

    /**
     * 订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=待支付,1=已支付,2=已取消,3=已退款,4=支付失败")
    private String orderStatus;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private Date payTime;

    /**
     * 取消时间
     */
    @ExcelProperty(value = "取消时间")
    private Date cancelTime;

    /**
     * 退款时间
     */
    @ExcelProperty(value = "退款时间")
    private Date refundTime;

    /**
     * 订单过期时间
     */
    @ExcelProperty(value = "订单过期时间")
    private Date expireTime;

    /**
     * 客户端IP
     */
    @ExcelProperty(value = "客户端IP")
    private String clientIp;

    /**
     * 用户代理
     */
    @ExcelProperty(value = "用户代理")
    private String userAgent;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
