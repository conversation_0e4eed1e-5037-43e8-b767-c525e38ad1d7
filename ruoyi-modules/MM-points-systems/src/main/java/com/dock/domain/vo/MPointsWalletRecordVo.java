package com.dock.domain.vo;

import com.dock.domain.MPointsWalletRecord;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 钱包流水记录视图对象 m_points_wallet_record
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MPointsWalletRecord.class)
public class MPointsWalletRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 钱包ID
     */
    @ExcelProperty(value = "钱包ID")
    private Long walletId;

    /**
     * 交易流水号
     */
    @ExcelProperty(value = "交易流水号")
    private String tradeNo;

    /**
     * 交易类型（1收入 2支出）
     */
    @ExcelProperty(value = "交易类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=收入,2=支出")
    private String tradeType;

    /**
     * 交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）
     */
    @ExcelProperty(value = "交易原因", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "r=echarge充值,c=onsume消费,r=efund退款,t=ransfer转账,w=ithdraw提现")
    private String tradeReason;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    private Long amount;

    /**
     * 交易前余额
     */
    @ExcelProperty(value = "交易前余额")
    private Long balanceBefore;

    /**
     * 交易后余额
     */
    @ExcelProperty(value = "交易后余额")
    private Long balanceAfter;

    /**
     * 关联ID
     */
    @ExcelProperty(value = "关联ID")
    private Long relatedId;

    /**
     * 关联类型
     */
    @ExcelProperty(value = "关联类型")
    private String relatedType;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式")
    private String paymentMethod;

    /**
     * 支付流水号
     */
    @ExcelProperty(value = "支付流水号")
    private String paymentNo;

    /**
     * 描述说明
     */
    @ExcelProperty(value = "描述说明")
    private String description;

    /**
     * 客户端IP
     */
    @ExcelProperty(value = "客户端IP")
    private String clientIp;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
