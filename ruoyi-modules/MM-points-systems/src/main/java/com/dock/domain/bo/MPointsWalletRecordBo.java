package com.dock.domain.bo;

import com.dock.domain.MPointsWalletRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 钱包流水记录业务对象 m_points_wallet_record
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MPointsWalletRecord.class, reverseConvertGenerate = false)
public class MPointsWalletRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 钱包ID
     */
    private Long walletId;

    /**
     * 交易流水号
     */
    private String tradeNo;

    /**
     * 交易类型（1收入 2支出）
     */
    private String tradeType;

    /**
     * 交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）
     */
    private String tradeReason;

    /**
     * 交易金额
     */
    private Long amount;

    /**
     * 交易前余额
     */
    private Long balanceBefore;

    /**
     * 交易后余额
     */
    private Long balanceAfter;

    /**
     * 关联ID
     */
    @NotNull(message = "关联ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long relatedId;

    /**
     * 关联类型
     */
    @NotBlank(message = "关联类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String relatedType;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentMethod;

    /**
     * 支付流水号
     */
    @NotBlank(message = "支付流水号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentNo;

    /**
     * 描述说明
     */
    @NotBlank(message = "描述说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 客户端IP
     */
    @NotBlank(message = "客户端IP不能为空", groups = { AddGroup.class, EditGroup.class })
    private String clientIp;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
