package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 积分订单对象 m_points_order
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_points_order")
public class MPointsOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 套餐ID
     */
    private Long packageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 购买积分数量
     */
    private Long pointsAmount;

    /**
     * 赠送积分数量
     */
    private Long bonusPoints;

    /**
     * 总积分数量
     */
    private Long totalPoints;

    /**
     * 原价
     */
    private Long originalPrice;

    /**
     * 实付金额
     */
    private Long payAmount;

    /**
     * 优惠金额
     */
    private Long discountAmount;

    /**
     * 支付方式（alipay支付宝 wechat微信 balance余额）
     */
    private String paymentMethod;

    /**
     * 支付流水号
     */
    private String paymentNo;

    /**
     * 订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）
     */
    private String orderStatus;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 订单过期时间
     */
    private Date expireTime;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
