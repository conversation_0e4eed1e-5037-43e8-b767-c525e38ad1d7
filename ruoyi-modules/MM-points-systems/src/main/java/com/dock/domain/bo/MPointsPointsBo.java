package com.dock.domain.bo;

import com.dock.domain.MPointsPoints;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 积分变动记录业务对象 m_points_points
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MPointsPoints.class, reverseConvertGenerate = false)
public class MPointsPointsBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 变动类型（1收入 2支出）
     */
    private String changeType;

    /**
     * 变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）
     */
    private String changeReason;

    /**
     * 积分数量
     */
    private Long pointsAmount;

    /**
     * 变动前余额
     */
    private Long balanceBefore;

    /**
     * 变动后余额
     */
    private Long balanceAfter;

    /**
     * 关联ID（订单ID、消费记录ID等）
     */
    @NotNull(message = "关联ID（订单ID、消费记录ID等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long relatedId;

    /**
     * 关联类型（order订单 consume消费 gift赠送）
     */
    @NotBlank(message = "关联类型（order订单 consume消费 gift赠送）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String relatedType;

    /**
     * 积分过期时间
     */
    @NotNull(message = "积分过期时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expireTime;

    /**
     * 描述说明
     */
    @NotBlank(message = "描述说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
