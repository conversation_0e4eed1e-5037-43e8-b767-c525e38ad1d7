package com.dock.domain.bo;

import com.dock.domain.MPointsWallet;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户钱包业务对象 m_points_wallet
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MPointsWallet.class, reverseConvertGenerate = false)
public class MPointsWalletBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 余额
     */
    @NotNull(message = "余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long balance;

    /**
     * 冻结金额
     */
    @NotNull(message = "冻结金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long frozenAmount;

    /**
     * 累计充值
     */
    @NotNull(message = "累计充值不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalRecharge;

    /**
     * 累计消费
     */
    @NotNull(message = "累计消费不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalConsume;

    /**
     * 积分余额
     */
    @NotNull(message = "积分余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long pointsBalance;

    /**
     * 累计获得积分
     */
    @NotNull(message = "累计获得积分不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalPoints;

    /**
     * 累计使用积分
     */
    @NotNull(message = "累计使用积分不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long usedPoints;

    /**
     * 支付密码
     */
    @NotBlank(message = "支付密码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String payPassword;

    /**
     * 是否设置支付密码（0否 1是）
     */
    @NotBlank(message = "是否设置支付密码（0否 1是）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isPayPwdSet;

    /**
     * 钱包状态（0正常 1冻结 2注销）
     */
    @NotBlank(message = "钱包状态（0正常 1冻结 2注销）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 最后交易时间
     */
    @NotNull(message = "最后交易时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lastTradeTime;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
