package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 积分套餐对象 m_points_package
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_points_package")
public class MPointsPackage extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 套餐描述
     */
    private String packageDesc;

    /**
     * 积分数量
     */
    private Long pointsAmount;

    /**
     * 原价
     */
    private Long originalPrice;

    /**
     * 售价
     */
    private Long salePrice;

    /**
     * 折扣率(%)
     */
    private Long discountRate;

    /**
     * 赠送积分
     */
    private Long bonusPoints;

    /**
     * 套餐类型（1普通套餐 2限时优惠 3新用户专享）
     */
    private String packageType;

    /**
     * 是否热门（0否 1是）
     */
    private String isHot;

    /**
     * 显示顺序
     */
    private Long sortOrder;

    /**
     * 生效时间
     */
    private Date startTime;

    /**
     * 失效时间
     */
    private Date endTime;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
