package com.dock.domain.bo;

import com.dock.domain.MPointsOrder;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 积分订单业务对象 m_points_order
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MPointsOrder.class, reverseConvertGenerate = false)
public class MPointsOrderBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 套餐ID
     */
    private Long packageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 购买积分数量
     */
    private Long pointsAmount;

    /**
     * 赠送积分数量
     */
    @NotNull(message = "赠送积分数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bonusPoints;

    /**
     * 总积分数量
     */
    private Long totalPoints;

    /**
     * 原价
     */
    private Long originalPrice;

    /**
     * 实付金额
     */
    private Long payAmount;

    /**
     * 优惠金额
     */
    @NotNull(message = "优惠金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long discountAmount;

    /**
     * 支付方式（alipay支付宝 wechat微信 balance余额）
     */
    @NotBlank(message = "支付方式（alipay支付宝 wechat微信 balance余额）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentMethod;

    /**
     * 支付流水号
     */
    @NotBlank(message = "支付流水号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentNo;

    /**
     * 订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）
     */
    @NotBlank(message = "订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderStatus;

    /**
     * 支付时间
     */
    @NotNull(message = "支付时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date payTime;

    /**
     * 取消时间
     */
    @NotNull(message = "取消时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date cancelTime;

    /**
     * 退款时间
     */
    @NotNull(message = "退款时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date refundTime;

    /**
     * 订单过期时间
     */
    @NotNull(message = "订单过期时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expireTime;

    /**
     * 客户端IP
     */
    @NotBlank(message = "客户端IP不能为空", groups = { AddGroup.class, EditGroup.class })
    private String clientIp;

    /**
     * 用户代理
     */
    @NotBlank(message = "用户代理不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userAgent;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
