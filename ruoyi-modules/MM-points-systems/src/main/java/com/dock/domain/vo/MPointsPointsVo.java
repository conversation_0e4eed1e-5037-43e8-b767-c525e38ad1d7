package com.dock.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.dock.domain.MPointsPoints;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 积分变动记录视图对象 m_points_points
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MPointsPoints.class)
public class MPointsPointsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 变动类型（1收入 2支出）
     */
    @ExcelProperty(value = "变动类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=收入,2=支出")
    private String changeType;

    /**
     * 变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）
     */
    @ExcelProperty(value = "变动原因", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "p=urchase购买,c=onsume消费,r=efund退款,g=ift赠送,e=xpire过期,a=dmin管理员操作")
    private String changeReason;

    /**
     * 积分数量
     */
    @ExcelProperty(value = "积分数量")
    private Long pointsAmount;

    /**
     * 变动前余额
     */
    @ExcelProperty(value = "变动前余额")
    private Long balanceBefore;

    /**
     * 变动后余额
     */
    @ExcelProperty(value = "变动后余额")
    private Long balanceAfter;

    /**
     * 关联ID（订单ID、消费记录ID等）
     */
    @ExcelProperty(value = "关联ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "订=单ID、消费记录ID等")
    private Long relatedId;

    /**
     * 关联类型（order订单 consume消费 gift赠送）
     */
    @ExcelProperty(value = "关联类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "o=rder订单,c=onsume消费,g=ift赠送")
    private String relatedType;

    /**
     * 积分过期时间
     */
    @ExcelProperty(value = "积分过期时间")
    private Date expireTime;

    /**
     * 描述说明
     */
    @ExcelProperty(value = "描述说明")
    private String description;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
