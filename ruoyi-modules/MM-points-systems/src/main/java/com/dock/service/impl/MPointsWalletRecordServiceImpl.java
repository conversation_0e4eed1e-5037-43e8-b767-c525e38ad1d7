package com.dock.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MPointsWalletRecordBo;
import com.dock.domain.vo.MPointsWalletRecordVo;
import com.dock.domain.MPointsWalletRecord;
import com.dock.mapper.MPointsWalletRecordMapper;
import com.dock.service.IMPointsWalletRecordService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 钱包流水记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MPointsWalletRecordServiceImpl implements IMPointsWalletRecordService {

    private final MPointsWalletRecordMapper baseMapper;

    /**
     * 查询钱包流水记录
     *
     * @param id 主键
     * @return 钱包流水记录
     */
    @Override
    public MPointsWalletRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询钱包流水记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 钱包流水记录分页列表
     */
    @Override
    public TableDataInfo<MPointsWalletRecordVo> queryPageList(MPointsWalletRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MPointsWalletRecord> lqw = buildQueryWrapper(bo);
        Page<MPointsWalletRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的钱包流水记录列表
     *
     * @param bo 查询条件
     * @return 钱包流水记录列表
     */
    @Override
    public List<MPointsWalletRecordVo> queryList(MPointsWalletRecordBo bo) {
        LambdaQueryWrapper<MPointsWalletRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MPointsWalletRecord> buildQueryWrapper(MPointsWalletRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MPointsWalletRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MPointsWalletRecord::getId);
        lqw.eq(bo.getUserId() != null, MPointsWalletRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getWalletId() != null, MPointsWalletRecord::getWalletId, bo.getWalletId());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeNo()), MPointsWalletRecord::getTradeNo, bo.getTradeNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeType()), MPointsWalletRecord::getTradeType, bo.getTradeType());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeReason()), MPointsWalletRecord::getTradeReason, bo.getTradeReason());
        lqw.eq(bo.getAmount() != null, MPointsWalletRecord::getAmount, bo.getAmount());
        lqw.eq(bo.getBalanceBefore() != null, MPointsWalletRecord::getBalanceBefore, bo.getBalanceBefore());
        lqw.eq(bo.getBalanceAfter() != null, MPointsWalletRecord::getBalanceAfter, bo.getBalanceAfter());
        lqw.eq(bo.getRelatedId() != null, MPointsWalletRecord::getRelatedId, bo.getRelatedId());
        lqw.eq(StringUtils.isNotBlank(bo.getRelatedType()), MPointsWalletRecord::getRelatedType, bo.getRelatedType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), MPointsWalletRecord::getPaymentMethod, bo.getPaymentMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentNo()), MPointsWalletRecord::getPaymentNo, bo.getPaymentNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), MPointsWalletRecord::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getClientIp()), MPointsWalletRecord::getClientIp, bo.getClientIp());
        return lqw;
    }

    /**
     * 新增钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MPointsWalletRecordBo bo) {
        MPointsWalletRecord add = MapstructUtils.convert(bo, MPointsWalletRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MPointsWalletRecordBo bo) {
        MPointsWalletRecord update = MapstructUtils.convert(bo, MPointsWalletRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MPointsWalletRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除钱包流水记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
