package com.dock.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MPointsWalletBo;
import com.dock.domain.vo.MPointsWalletVo;
import com.dock.domain.MPointsWallet;
import com.dock.mapper.MPointsWalletMapper;
import com.dock.service.IMPointsWalletService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用户钱包Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MPointsWalletServiceImpl implements IMPointsWalletService {

    private final MPointsWalletMapper baseMapper;

    /**
     * 查询用户钱包
     *
     * @param id 主键
     * @return 用户钱包
     */
    @Override
    public MPointsWalletVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户钱包列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户钱包分页列表
     */
    @Override
    public TableDataInfo<MPointsWalletVo> queryPageList(MPointsWalletBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MPointsWallet> lqw = buildQueryWrapper(bo);
        Page<MPointsWalletVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户钱包列表
     *
     * @param bo 查询条件
     * @return 用户钱包列表
     */
    @Override
    public List<MPointsWalletVo> queryList(MPointsWalletBo bo) {
        LambdaQueryWrapper<MPointsWallet> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MPointsWallet> buildQueryWrapper(MPointsWalletBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MPointsWallet> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MPointsWallet::getId);
        lqw.eq(bo.getUserId() != null, MPointsWallet::getUserId, bo.getUserId());
        lqw.eq(bo.getBalance() != null, MPointsWallet::getBalance, bo.getBalance());
        lqw.eq(bo.getFrozenAmount() != null, MPointsWallet::getFrozenAmount, bo.getFrozenAmount());
        lqw.eq(bo.getTotalRecharge() != null, MPointsWallet::getTotalRecharge, bo.getTotalRecharge());
        lqw.eq(bo.getTotalConsume() != null, MPointsWallet::getTotalConsume, bo.getTotalConsume());
        lqw.eq(bo.getPointsBalance() != null, MPointsWallet::getPointsBalance, bo.getPointsBalance());
        lqw.eq(bo.getTotalPoints() != null, MPointsWallet::getTotalPoints, bo.getTotalPoints());
        lqw.eq(bo.getUsedPoints() != null, MPointsWallet::getUsedPoints, bo.getUsedPoints());
        lqw.eq(StringUtils.isNotBlank(bo.getPayPassword()), MPointsWallet::getPayPassword, bo.getPayPassword());
        lqw.eq(StringUtils.isNotBlank(bo.getIsPayPwdSet()), MPointsWallet::getIsPayPwdSet, bo.getIsPayPwdSet());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MPointsWallet::getStatus, bo.getStatus());
        lqw.eq(bo.getLastTradeTime() != null, MPointsWallet::getLastTradeTime, bo.getLastTradeTime());
        return lqw;
    }

    /**
     * 新增用户钱包
     *
     * @param bo 用户钱包
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MPointsWalletBo bo) {
        MPointsWallet add = MapstructUtils.convert(bo, MPointsWallet.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户钱包
     *
     * @param bo 用户钱包
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MPointsWalletBo bo) {
        MPointsWallet update = MapstructUtils.convert(bo, MPointsWallet.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MPointsWallet entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户钱包信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
