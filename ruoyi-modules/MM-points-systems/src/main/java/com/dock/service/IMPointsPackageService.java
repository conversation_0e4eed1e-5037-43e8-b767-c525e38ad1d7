package com.dock.service;

import com.dock.domain.vo.MPointsPackageVo;
import com.dock.domain.bo.MPointsPackageBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 积分套餐Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IMPointsPackageService {

    /**
     * 查询积分套餐
     *
     * @param id 主键
     * @return 积分套餐
     */
    MPointsPackageVo queryById(Long id);

    /**
     * 分页查询积分套餐列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 积分套餐分页列表
     */
    TableDataInfo<MPointsPackageVo> queryPageList(MPointsPackageBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的积分套餐列表
     *
     * @param bo 查询条件
     * @return 积分套餐列表
     */
    List<MPointsPackageVo> queryList(MPointsPackageBo bo);

    /**
     * 新增积分套餐
     *
     * @param bo 积分套餐
     * @return 是否新增成功
     */
    Boolean insertByBo(MPointsPackageBo bo);

    /**
     * 修改积分套餐
     *
     * @param bo 积分套餐
     * @return 是否修改成功
     */
    Boolean updateByBo(MPointsPackageBo bo);

    /**
     * 校验并批量删除积分套餐信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
