package com.dock.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MPointsPackageBo;
import com.dock.domain.vo.MPointsPackageVo;
import com.dock.domain.MPointsPackage;
import com.dock.mapper.MPointsPackageMapper;
import com.dock.service.IMPointsPackageService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 积分套餐Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MPointsPackageServiceImpl implements IMPointsPackageService {

    private final MPointsPackageMapper baseMapper;

    /**
     * 查询积分套餐
     *
     * @param id 主键
     * @return 积分套餐
     */
    @Override
    public MPointsPackageVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询积分套餐列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 积分套餐分页列表
     */
    @Override
    public TableDataInfo<MPointsPackageVo> queryPageList(MPointsPackageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MPointsPackage> lqw = buildQueryWrapper(bo);
        Page<MPointsPackageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的积分套餐列表
     *
     * @param bo 查询条件
     * @return 积分套餐列表
     */
    @Override
    public List<MPointsPackageVo> queryList(MPointsPackageBo bo) {
        LambdaQueryWrapper<MPointsPackage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MPointsPackage> buildQueryWrapper(MPointsPackageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MPointsPackage> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MPointsPackage::getId);
        lqw.like(StringUtils.isNotBlank(bo.getPackageName()), MPointsPackage::getPackageName, bo.getPackageName());
        lqw.eq(StringUtils.isNotBlank(bo.getPackageDesc()), MPointsPackage::getPackageDesc, bo.getPackageDesc());
        lqw.eq(bo.getPointsAmount() != null, MPointsPackage::getPointsAmount, bo.getPointsAmount());
        lqw.eq(bo.getOriginalPrice() != null, MPointsPackage::getOriginalPrice, bo.getOriginalPrice());
        lqw.eq(bo.getSalePrice() != null, MPointsPackage::getSalePrice, bo.getSalePrice());
        lqw.eq(bo.getDiscountRate() != null, MPointsPackage::getDiscountRate, bo.getDiscountRate());
        lqw.eq(bo.getBonusPoints() != null, MPointsPackage::getBonusPoints, bo.getBonusPoints());
        lqw.eq(StringUtils.isNotBlank(bo.getPackageType()), MPointsPackage::getPackageType, bo.getPackageType());
        lqw.eq(StringUtils.isNotBlank(bo.getIsHot()), MPointsPackage::getIsHot, bo.getIsHot());
        lqw.eq(bo.getSortOrder() != null, MPointsPackage::getSortOrder, bo.getSortOrder());
        lqw.eq(bo.getStartTime() != null, MPointsPackage::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, MPointsPackage::getEndTime, bo.getEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MPointsPackage::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增积分套餐
     *
     * @param bo 积分套餐
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MPointsPackageBo bo) {
        MPointsPackage add = MapstructUtils.convert(bo, MPointsPackage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改积分套餐
     *
     * @param bo 积分套餐
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MPointsPackageBo bo) {
        MPointsPackage update = MapstructUtils.convert(bo, MPointsPackage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MPointsPackage entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除积分套餐信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
