package com.dock.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MPointsPointsBo;
import com.dock.domain.vo.MPointsPointsVo;
import com.dock.domain.MPointsPoints;
import com.dock.mapper.MPointsPointsMapper;
import com.dock.service.IMPointsPointsService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 积分变动记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MPointsPointsServiceImpl implements IMPointsPointsService {

    private final MPointsPointsMapper baseMapper;

    /**
     * 查询积分变动记录
     *
     * @param id 主键
     * @return 积分变动记录
     */
    @Override
    public MPointsPointsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询积分变动记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 积分变动记录分页列表
     */
    @Override
    public TableDataInfo<MPointsPointsVo> queryPageList(MPointsPointsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MPointsPoints> lqw = buildQueryWrapper(bo);
        Page<MPointsPointsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的积分变动记录列表
     *
     * @param bo 查询条件
     * @return 积分变动记录列表
     */
    @Override
    public List<MPointsPointsVo> queryList(MPointsPointsBo bo) {
        LambdaQueryWrapper<MPointsPoints> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MPointsPoints> buildQueryWrapper(MPointsPointsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MPointsPoints> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MPointsPoints::getId);
        lqw.eq(bo.getUserId() != null, MPointsPoints::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), MPointsPoints::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeReason()), MPointsPoints::getChangeReason, bo.getChangeReason());
        lqw.eq(bo.getPointsAmount() != null, MPointsPoints::getPointsAmount, bo.getPointsAmount());
        lqw.eq(bo.getBalanceBefore() != null, MPointsPoints::getBalanceBefore, bo.getBalanceBefore());
        lqw.eq(bo.getBalanceAfter() != null, MPointsPoints::getBalanceAfter, bo.getBalanceAfter());
        lqw.eq(bo.getRelatedId() != null, MPointsPoints::getRelatedId, bo.getRelatedId());
        lqw.eq(StringUtils.isNotBlank(bo.getRelatedType()), MPointsPoints::getRelatedType, bo.getRelatedType());
        lqw.eq(bo.getExpireTime() != null, MPointsPoints::getExpireTime, bo.getExpireTime());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), MPointsPoints::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增积分变动记录
     *
     * @param bo 积分变动记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MPointsPointsBo bo) {
        MPointsPoints add = MapstructUtils.convert(bo, MPointsPoints.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改积分变动记录
     *
     * @param bo 积分变动记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MPointsPointsBo bo) {
        MPointsPoints update = MapstructUtils.convert(bo, MPointsPoints.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MPointsPoints entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除积分变动记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
