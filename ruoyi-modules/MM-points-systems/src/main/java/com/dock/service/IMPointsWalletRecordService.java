package com.dock.service;

import com.dock.domain.vo.MPointsWalletRecordVo;
import com.dock.domain.bo.MPointsWalletRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 钱包流水记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IMPointsWalletRecordService {

    /**
     * 查询钱包流水记录
     *
     * @param id 主键
     * @return 钱包流水记录
     */
    MPointsWalletRecordVo queryById(Long id);

    /**
     * 分页查询钱包流水记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 钱包流水记录分页列表
     */
    TableDataInfo<MPointsWalletRecordVo> queryPageList(MPointsWalletRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的钱包流水记录列表
     *
     * @param bo 查询条件
     * @return 钱包流水记录列表
     */
    List<MPointsWalletRecordVo> queryList(MPointsWalletRecordBo bo);

    /**
     * 新增钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MPointsWalletRecordBo bo);

    /**
     * 修改钱包流水记录
     *
     * @param bo 钱包流水记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MPointsWalletRecordBo bo);

    /**
     * 校验并批量删除钱包流水记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
