package com.dock.service;

import com.dock.domain.vo.MPointsPointsVo;
import com.dock.domain.bo.MPointsPointsBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 积分变动记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IMPointsPointsService {

    /**
     * 查询积分变动记录
     *
     * @param id 主键
     * @return 积分变动记录
     */
    MPointsPointsVo queryById(Long id);

    /**
     * 分页查询积分变动记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 积分变动记录分页列表
     */
    TableDataInfo<MPointsPointsVo> queryPageList(MPointsPointsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的积分变动记录列表
     *
     * @param bo 查询条件
     * @return 积分变动记录列表
     */
    List<MPointsPointsVo> queryList(MPointsPointsBo bo);

    /**
     * 新增积分变动记录
     *
     * @param bo 积分变动记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MPointsPointsBo bo);

    /**
     * 修改积分变动记录
     *
     * @param bo 积分变动记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MPointsPointsBo bo);

    /**
     * 校验并批量删除积分变动记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
