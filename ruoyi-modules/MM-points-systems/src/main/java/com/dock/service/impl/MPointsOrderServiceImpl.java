package com.dock.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MPointsOrderBo;
import com.dock.domain.vo.MPointsOrderVo;
import com.dock.domain.MPointsOrder;
import com.dock.mapper.MPointsOrderMapper;
import com.dock.service.IMPointsOrderService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 积分订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MPointsOrderServiceImpl implements IMPointsOrderService {

    private final MPointsOrderMapper baseMapper;

    /**
     * 查询积分订单
     *
     * @param id 主键
     * @return 积分订单
     */
    @Override
    public MPointsOrderVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询积分订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 积分订单分页列表
     */
    @Override
    public TableDataInfo<MPointsOrderVo> queryPageList(MPointsOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MPointsOrder> lqw = buildQueryWrapper(bo);
        Page<MPointsOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的积分订单列表
     *
     * @param bo 查询条件
     * @return 积分订单列表
     */
    @Override
    public List<MPointsOrderVo> queryList(MPointsOrderBo bo) {
        LambdaQueryWrapper<MPointsOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MPointsOrder> buildQueryWrapper(MPointsOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MPointsOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MPointsOrder::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), MPointsOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getUserId() != null, MPointsOrder::getUserId, bo.getUserId());
        lqw.eq(bo.getPackageId() != null, MPointsOrder::getPackageId, bo.getPackageId());
        lqw.like(StringUtils.isNotBlank(bo.getPackageName()), MPointsOrder::getPackageName, bo.getPackageName());
        lqw.eq(bo.getPointsAmount() != null, MPointsOrder::getPointsAmount, bo.getPointsAmount());
        lqw.eq(bo.getBonusPoints() != null, MPointsOrder::getBonusPoints, bo.getBonusPoints());
        lqw.eq(bo.getTotalPoints() != null, MPointsOrder::getTotalPoints, bo.getTotalPoints());
        lqw.eq(bo.getOriginalPrice() != null, MPointsOrder::getOriginalPrice, bo.getOriginalPrice());
        lqw.eq(bo.getPayAmount() != null, MPointsOrder::getPayAmount, bo.getPayAmount());
        lqw.eq(bo.getDiscountAmount() != null, MPointsOrder::getDiscountAmount, bo.getDiscountAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), MPointsOrder::getPaymentMethod, bo.getPaymentMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentNo()), MPointsOrder::getPaymentNo, bo.getPaymentNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderStatus()), MPointsOrder::getOrderStatus, bo.getOrderStatus());
        lqw.eq(bo.getPayTime() != null, MPointsOrder::getPayTime, bo.getPayTime());
        lqw.eq(bo.getCancelTime() != null, MPointsOrder::getCancelTime, bo.getCancelTime());
        lqw.eq(bo.getRefundTime() != null, MPointsOrder::getRefundTime, bo.getRefundTime());
        lqw.eq(bo.getExpireTime() != null, MPointsOrder::getExpireTime, bo.getExpireTime());
        lqw.eq(StringUtils.isNotBlank(bo.getClientIp()), MPointsOrder::getClientIp, bo.getClientIp());
        lqw.eq(StringUtils.isNotBlank(bo.getUserAgent()), MPointsOrder::getUserAgent, bo.getUserAgent());
        return lqw;
    }

    /**
     * 新增积分订单
     *
     * @param bo 积分订单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MPointsOrderBo bo) {
        MPointsOrder add = MapstructUtils.convert(bo, MPointsOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改积分订单
     *
     * @param bo 积分订单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MPointsOrderBo bo) {
        MPointsOrder update = MapstructUtils.convert(bo, MPointsOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MPointsOrder entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除积分订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
