package com.dock.service;

import com.dock.domain.vo.MPointsWalletVo;
import com.dock.domain.bo.MPointsWalletBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 用户钱包Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IMPointsWalletService {

    /**
     * 查询用户钱包
     *
     * @param id 主键
     * @return 用户钱包
     */
    MPointsWalletVo queryById(Long id);

    /**
     * 分页查询用户钱包列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户钱包分页列表
     */
    TableDataInfo<MPointsWalletVo> queryPageList(MPointsWalletBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户钱包列表
     *
     * @param bo 查询条件
     * @return 用户钱包列表
     */
    List<MPointsWalletVo> queryList(MPointsWalletBo bo);

    /**
     * 新增用户钱包
     *
     * @param bo 用户钱包
     * @return 是否新增成功
     */
    Boolean insertByBo(MPointsWalletBo bo);

    /**
     * 修改用户钱包
     *
     * @param bo 用户钱包
     * @return 是否修改成功
     */
    Boolean updateByBo(MPointsWalletBo bo);

    /**
     * 校验并批量删除用户钱包信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
