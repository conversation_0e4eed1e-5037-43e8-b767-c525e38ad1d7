package com.dock.service;

import com.dock.domain.vo.MPointsOrderVo;
import com.dock.domain.bo.MPointsOrderBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 积分订单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IMPointsOrderService {

    /**
     * 查询积分订单
     *
     * @param id 主键
     * @return 积分订单
     */
    MPointsOrderVo queryById(Long id);

    /**
     * 分页查询积分订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 积分订单分页列表
     */
    TableDataInfo<MPointsOrderVo> queryPageList(MPointsOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的积分订单列表
     *
     * @param bo 查询条件
     * @return 积分订单列表
     */
    List<MPointsOrderVo> queryList(MPointsOrderBo bo);

    /**
     * 新增积分订单
     *
     * @param bo 积分订单
     * @return 是否新增成功
     */
    Boolean insertByBo(MPointsOrderBo bo);

    /**
     * 修改积分订单
     *
     * @param bo 积分订单
     * @return 是否修改成功
     */
    Boolean updateByBo(MPointsOrderBo bo);

    /**
     * 校验并批量删除积分订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
