package com.dock.config;


import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.dock.domain.MMerchant;
import com.dock.domain.MStore;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * 自定义ID生成器
 * 对特定实体使用数据库自增，其他实体使用雪花算法
 *
 * <AUTHOR>
 */
@Component
@Primary
public class CustomIdGenerator implements IdentifierGenerator {

    private final DefaultIdentifierGenerator defaultGenerator = new DefaultIdentifierGenerator();

    @Override
    public Number nextId(Object entity) {
        if (entity instanceof MMerchant || entity instanceof MStore) {
            return null; // 返回null让数据库自增
        }

        // 其他实体使用雪花算法
        return defaultGenerator.nextId(entity);
    }

    @Override
    public String nextUUID(Object entity) {
        return defaultGenerator.nextUUID(entity);
    }
}
