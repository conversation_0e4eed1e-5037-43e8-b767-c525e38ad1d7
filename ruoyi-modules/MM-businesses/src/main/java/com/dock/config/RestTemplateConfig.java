package com.dock.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Value("${proxy.host}")
    private String proxyHost;

    @Value("${proxy.port}")
    private int proxyPort;

//    @Bean
//    public RestTemplate restTemplate() {
//        // 设置代理
//        HttpHost proxy = new HttpHost(proxyHost, proxyPort);
//
//        HttpClient httpClient = HttpClientBuilder.create()
//                .setProxy(proxy)
//                .build();
//
//        HttpComponentsClientHttpRequestFactory factory =
//                new HttpComponentsClientHttpRequestFactory(httpClient);
//
//        return new RestTemplate(factory);
//    }

    // prod环境使用，服务器上不需要配置代理(海外服务器)
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
