package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 商户对象 m_merchant
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_merchant")
public class MMerchant extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户名称
     */
    private String name;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 地址
     */
    private String address;

    /**
     * 删除标记(0-正常,1-已删除)，逻辑删除字段
     */
    @TableLogic
    private Long delFlag;



}
