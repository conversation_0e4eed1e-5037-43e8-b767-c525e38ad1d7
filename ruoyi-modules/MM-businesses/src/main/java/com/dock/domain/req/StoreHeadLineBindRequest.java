package com.dock.domain.req;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 商铺绑定文案请求体
 */
@Data
public class StoreHeadLineBindRequest {

    @NotNull(message = "商铺ID不能为空")
    private Long storeId;

    @NotEmpty(message = "文案ID不能为空")
    private Long headLineId;

}