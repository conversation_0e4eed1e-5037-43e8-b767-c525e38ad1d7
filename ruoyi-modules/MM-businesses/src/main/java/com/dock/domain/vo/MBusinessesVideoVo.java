package com.dock.domain.vo;

import com.dock.domain.MBusinessesVideo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 视频素材管理视图对象 m_businesses_video
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MBusinessesVideo.class)
public class MBusinessesVideoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，视频素材唯一标识符
     */
    @ExcelProperty(value = "主键ID，视频素材唯一标识符")
    private Long id;

    /**
     * 视频素材标题或名称
     */
    @ExcelProperty(value = "视频素材标题或名称")
    private String title;

    /**
     * 视频素材详细描述
     */
    @ExcelProperty(value = "视频素材详细描述")
    private String description;

    /**
     * 视频文件存储的URL或路径，支持OSS/CDN
     */
    @ExcelProperty(value = "视频文件存储的URL或路径，支持OSS/CDN")
    private String videoUrl;

    /**
     * 视频素材分类，例如：教学、宣传、娱乐
     */
    @ExcelProperty(value = "视频素材分类，例如：教学、宣传、娱乐")
    private String category;

    /**
     * 素材状态
     */
    @ExcelProperty(value = "素材状态")
    private String status;

    /**
     * 视频大小
     */
    private Long fileSize;


}
