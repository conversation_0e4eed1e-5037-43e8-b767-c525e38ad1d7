package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 店铺对象 m_store
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_store")
public class MStore extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属商户ID（无外键约束）
     */
    private Long merchantId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺电话
     */
    private String storePhone;

    /**
     * 店铺地址
     */
    private String storeAddress;

    /**
     * 删除标记(0-正常,1-已删除)，逻辑删除字段
     */
    @TableLogic
    private Long delFlag;

    private String videoUrl;

    private String title;

    @TableField("headline")
    private String headLine;

}
