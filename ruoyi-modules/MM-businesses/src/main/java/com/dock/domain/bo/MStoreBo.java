package com.dock.domain.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dock.domain.MStore;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 店铺业务对象 m_store
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MStore.class, reverseConvertGenerate = false)
public class MStoreBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 所属商户ID（无外键约束）
     */
    private Long merchantId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺电话
     */
//    @NotBlank(message = "店铺电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String storePhone;

    /**
     * 店铺地址
     */
//    @NotBlank(message = "店铺地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String storeAddress;

    private String videoUrl;

    private String title;

    private String headLine;


}
