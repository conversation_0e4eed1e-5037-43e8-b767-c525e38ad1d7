package com.dock.domain.bo;

import com.dock.domain.MBusinessesHeadline;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 商家文案业务对象 m_businesses_headline
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MBusinessesHeadline.class, reverseConvertGenerate = false)
public class MBusinessesHeadlineBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文案标题
     */
    private String title;

    /**
     * 文案内容
     */
//    @NotBlank(message = "文案内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 状态（0-草稿，1-发布，2-下架）
     */
//    @NotNull(message = "状态（0-草稿，1-发布，2-下架）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;


    /**
     * 生成条数
     */
    private int count;



}
