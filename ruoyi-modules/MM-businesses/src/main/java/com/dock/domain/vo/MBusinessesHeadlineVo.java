package com.dock.domain.vo;

import com.dock.domain.MBusinessesHeadline;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 商家文案视图对象 m_businesses_headline
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MBusinessesHeadline.class)
public class MBusinessesHeadlineVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 文案标题
     */
    @ExcelProperty(value = "文案标题")
    private String title;

    /**
     * 文案内容
     */
    @ExcelProperty(value = "文案内容")
    private String content;

    /**
     * 状态（0-草稿，1-发布，2-下架）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-草稿，1-发布，2-下架")
    private Long status;


}
