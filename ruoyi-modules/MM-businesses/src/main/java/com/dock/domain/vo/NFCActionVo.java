package com.dock.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.dock.domain.MStore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.mybatis.core.domain.BaseEntity;


@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MStore.class)
public class NFCActionVo extends BaseEntity {


    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 关联任务ID
     */
    @ExcelProperty(value = "关联任务ID")
    private Long taskId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 视频URL
     */
    @ExcelProperty(value = "视频URL")
    private String videoUrl;

    /**
     * 状态（0-正常 1-异常）
     */
    @ExcelProperty(value = "状态")
    private String status;


}
