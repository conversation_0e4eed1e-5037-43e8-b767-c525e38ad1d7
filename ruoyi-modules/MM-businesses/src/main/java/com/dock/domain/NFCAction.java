package com.dock.domain;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("media_product")
public class NFCAction  extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    @TableId(value = "product_id", type = IdType.AUTO) // 或 AUTO，如果数据库为自增
    private Long productId;

    /**
     * 关联任务ID
     */
    private Long taskId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 状态（0-正常 1-异常）
     */
    private String status;


    /**
     * 删除标记(0-正常,1-已删除)，逻辑删除字段
     */
    @TableField(fill = FieldFill.INSERT)
    private String delFlag;


}
