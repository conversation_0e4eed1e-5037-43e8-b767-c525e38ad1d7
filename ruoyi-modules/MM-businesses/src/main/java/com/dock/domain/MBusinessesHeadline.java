package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 商家文案对象 m_businesses_headline
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_businesses_headline")
public class MBusinessesHeadline extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id",type = IdType.AUTO )
    private Long id;

    /**
     * 文案标题
     */
    private String title;

    /**
     * 文案内容
     */
    private String content;

    /**
     * 状态（0-草稿，1-发布，2-下架）
     */
    private Long status;

    /**
     * 删除标记(0-正常,1-已删除)，逻辑删除字段
     */
    @TableLogic
    private Long delFlag;


}
