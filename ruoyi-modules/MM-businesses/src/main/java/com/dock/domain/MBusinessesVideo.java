package com.dock.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 视频素材管理对象 m_businesses_video
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("m_businesses_video")
public class MBusinessesVideo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，视频素材唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 视频素材标题或名称
     */
    private String title;

    /**
     * 视频素材详细描述
     */
    private String description;

    /**
     * 视频文件存储的URL或路径，支持OSS/CDN
     */
    private String videoUrl;

    /**
     * 视频素材分类，例如：教学、宣传、娱乐
     */
    private String category;

    /**
     * 素材状态
     */
    private String status;

    /**
     * 视频大小
     */
    private Long fileSize;

    /**
     * 删除标记(0-正常,1-已删除)，逻辑删除字段
     */
    @TableLogic
    private Long delFlag;


}
