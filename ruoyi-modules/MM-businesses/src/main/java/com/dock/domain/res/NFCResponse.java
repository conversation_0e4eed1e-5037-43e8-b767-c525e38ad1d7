package com.dock.domain.res;

import lombok.Data;

import java.util.List;

@Data
public class NFCResponse {

//    client_key：你的抖音开放平台应用的 client_key
//    nonce_str、timestamp、signature：签名相关，需和 schema 里一致
//            share_to_publish=1：直接进入发布页
//    state：自定义参数（如分享ID等）
//    hashtag_list：话题列表，需用 JSON 字符串
//    title：文案
//    video_path：视频链接

    // H5分享类型
    private String share_type;

    // 抖音开放平台应用的 client_key
    private String client_key;

    // 签名相关，需和 schema 里一致
    private String nonce_str;

    // 签名相关，需和 schema 里一致
    private String timestamp;

    // 签名相关，需和 schema 里一致
    private String signature;

    // share_to_publish=1：直接进入发布页
    private int share_to_publish;

    // 自定义参数（如分享ID等）
    private String state;

    // 话题列表，需用 JSON 字符串
    private List<String> hashtag_list;

    // 文案
    private String title;

    // 视频链接
    private List<VideoList> video_list;


    private int count;

    @Data
    public static class VideoList{
        private List<String> self;
        private List<String> other;
    }

}
