package com.dock.domain.bo;

import com.dock.domain.MBusinessesVideo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 视频素材管理业务对象 m_businesses_video
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MBusinessesVideo.class, reverseConvertGenerate = false)
public class MBusinessesVideoBo extends BaseEntity {

    /**
     * 主键ID，视频素材唯一标识符
     */
    private Long id;

    /**
     * 视频素材标题或名称
     */
    private String title;

    /**
     * 视频素材详细描述
     */
//    @NotBlank(message = "视频素材详细描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 视频文件存储的URL或路径，支持OSS/CDN
     */
    private String videoUrl;

    /**
     * 视频素材分类，例如：教学、宣传、娱乐
     */
//    @NotBlank(message = "视频素材分类，例如：教学、宣传、娱乐不能为空", groups = { AddGroup.class, EditGroup.class })
    private String category;

    /**
     * 素材状态
     */
    private String status;

    /**
     * 视频大小
     */
    private Long fileSize;


}
