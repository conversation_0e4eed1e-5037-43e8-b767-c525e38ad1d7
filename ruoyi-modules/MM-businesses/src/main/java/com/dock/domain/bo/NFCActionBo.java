package com.dock.domain.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dock.domain.MStore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;


@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MStore.class, reverseConvertGenerate = false)
public class NFCActionBo extends BaseEntity {


    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 关联任务ID
     */
    private Long taskId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 状态（0-正常 1-异常）
     */
    private String status;


}
