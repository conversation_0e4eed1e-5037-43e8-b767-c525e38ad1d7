package com.dock.controller;

import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.system.domain.vo.SysOssUploadVo;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MStoreVo;
import com.dock.domain.bo.MStoreBo;
import com.dock.service.IMStoreService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 店铺
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/businesses/store")
public class MStoreController extends BaseController {

    private final IMStoreService mStoreService;


    /**
     * 查询店铺列表
     */
    @SaCheckPermission("businesses:store:list")
    @GetMapping("/list")
    public TableDataInfo<MStoreVo> list(MStoreBo bo, PageQuery pageQuery) {
        return mStoreService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出店铺列表
     */
    @SaCheckPermission("businesses:store:export")
    @Log(title = "店铺", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MStoreBo bo, HttpServletResponse response) {
        List<MStoreVo> list = mStoreService.queryList(bo);
        ExcelUtil.exportExcel(list, "店铺", MStoreVo.class, response);
    }

    /**
     * 获取店铺详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("businesses:store:query")
    @GetMapping("/{id}")
    public R<MStoreVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mStoreService.queryById(id));
    }

    /**
     * 新增店铺
     */
    @SaCheckPermission("businesses:store:add")
    @Log(title = "店铺", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MStoreBo bo) {
        return toAjax(mStoreService.insertByBo(bo));
    }

    /**
     * 修改店铺
     */
    @SaCheckPermission("businesses:store:edit")
    @Log(title = "店铺", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MStoreBo bo) {
        return toAjax(mStoreService.updateByBo(bo));
    }

    /**
     * 删除店铺
     *
     * @param ids 主键串
     */
    @SaCheckPermission("businesses:store:remove")
    @Log(title = "店铺", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mStoreService.deleteWithValidByIds(List.of(ids), true));
    }





}
