package com.dock.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dock.domain.req.NCFRequest;
import com.dock.domain.NFCAction;
import com.dock.domain.req.StoreVideoBindRequest;
import com.dock.domain.res.NFCResponse;
import com.dock.mapper.NFCActionMapper;
import com.dock.service.INFCActionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/businesses/NFCAction")
@Slf4j
public class NFCActionController {

    private final NFCActionMapper nfcActionMapper;
    private final INFCActionService infcActionService;


    @PostMapping("info")
    public R<List<NFCResponse>> info(@RequestBody NCFRequest request) {
        List<NFCResponse> list = infcActionService.info(request);
        return R.ok(list);
    }


    @PostMapping("recover")
    public R<Boolean> recover(@RequestParam String videoUrl) {
        // 判断video是否为空
        if (videoUrl == null || videoUrl.isEmpty()) {
            throw new ServiceException("视频地址不能为空");
        }
        LambdaQueryWrapper<NFCAction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NFCAction::getVideoUrl, videoUrl);
        List<NFCAction> list = nfcActionMapper.selectList(queryWrapper);
        if (list.isEmpty()) {
            log.warn("未找到与视频URL '{}' 匹配的NFCAction记录。", videoUrl);
            throw new ServiceException("未找到与该视频URL匹配的记录");
        }
        LambdaUpdateWrapper<NFCAction> updateWrapper = new LambdaUpdateWrapper<>();

        // 设置更新条件
        // 只有当当前状态不等于新状态时才更新，防止重复更新
        // UPDATE nfc_action SET status = 1 WHERE video_url = ? AND status != 1
        updateWrapper.eq(NFCAction::getVideoUrl, videoUrl).ne(NFCAction::getStatus, 2);
        updateWrapper.set(NFCAction::getStatus, 2);
        int update = nfcActionMapper.update(null, updateWrapper);
        if (update > 0) {
            return R.ok(true);
        }
        return R.fail(false);
    }


    @PostMapping("/bindAll")
    public R<Void> bindAll(@RequestBody StoreVideoBindRequest request) {
        boolean success = infcActionService.bindVideosToStore(request.getStoreId(), request.getSelfVideoIds(), request.getOtherVideoIds());
        return success ? R.ok() : R.fail();
    }


}