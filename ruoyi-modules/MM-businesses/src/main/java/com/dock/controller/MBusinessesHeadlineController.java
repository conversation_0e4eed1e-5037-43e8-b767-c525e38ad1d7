package com.dock.controller;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dock.domain.MBusinessesHeadline;
import com.dock.domain.MStore;
import com.dock.domain.bo.MStoreBo;
import com.dock.domain.req.HeadLineRequest;
import com.dock.domain.req.StoreHeadLineBindRequest;
import com.dock.domain.res.HeadlineResponse;
import com.dock.domain.vo.MStoreVo;
import com.dock.service.IMStoreService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MBusinessesHeadlineVo;
import com.dock.domain.bo.MBusinessesHeadlineBo;
import com.dock.service.IMBusinessesHeadlineService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 商家文案
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/businesses/businessesHeadline")
@Slf4j
public class MBusinessesHeadlineController extends BaseController {

    private final IMBusinessesHeadlineService mBusinessesHeadlineService;
    private final IMStoreService mStoreService;

    /**
     * 查询商家文案列表
     */
    @SaCheckPermission("businesses:businessesHeadline:list")
    @GetMapping("/list")
    public TableDataInfo<MBusinessesHeadlineVo> list(MBusinessesHeadlineBo bo, PageQuery pageQuery) {
        return mBusinessesHeadlineService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商家文案列表
     */
    @SaCheckPermission("businesses:businessesHeadline:export")
    @Log(title = "商家文案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MBusinessesHeadlineBo bo, HttpServletResponse response) {
        List<MBusinessesHeadlineVo> list = mBusinessesHeadlineService.queryList(bo);
        ExcelUtil.exportExcel(list, "商家文案", MBusinessesHeadlineVo.class, response);
    }

    /**
     * 获取商家文案详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("businesses:businessesHeadline:query")
    @GetMapping("/{id}")
    public R<MBusinessesHeadlineVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mBusinessesHeadlineService.queryById(id));
    }

    /**
     * 新增商家文案
     */
    @SaCheckPermission("businesses:businessesHeadline:add")
    @Log(title = "商家文案", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MBusinessesHeadlineBo bo) {
        return toAjax(mBusinessesHeadlineService.insertByBo(bo));
    }

    /**
     * 修改商家文案
     */
    @SaCheckPermission("businesses:businessesHeadline:edit")
    @Log(title = "商家文案", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MBusinessesHeadlineBo bo) {
        return toAjax(mBusinessesHeadlineService.updateByBo(bo));
    }

    /**
     * 删除商家文案
     *
     * @param ids 主键串
     */
    @SaCheckPermission("businesses:businessesHeadline:remove")
    @Log(title = "商家文案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mBusinessesHeadlineService.deleteWithValidByIds(List.of(ids), true));
    }


    @PostMapping("request")
    public R<HeadlineResponse> request(@RequestBody HeadLineRequest request) {
        int count = request.getCount();
        String title = request.getTitle();

        List<String> comments = mBusinessesHeadlineService.generateComments(title, count);
        log.info("comments----{}", comments);

        // 为每条文案创建一条数据库记录
        for (String comment : comments) {
            MBusinessesHeadlineBo bo = new MBusinessesHeadlineBo();
            bo.setTitle(title);
            bo.setContent(comment); // 每条文案单独保存
            bo.setStatus(0L); // 默认状态为草稿
            mBusinessesHeadlineService.insertByBo(bo);
        }

        HeadlineResponse response = new HeadlineResponse();
        response.setComments(comments);
        response.setTotalCount(comments.size());

        return R.ok(response);
    }


    @PostMapping("bind")
    public R<Void> bing(@RequestBody StoreHeadLineBindRequest request) {
        Long storeId = request.getStoreId();
        Long headLineId = request.getHeadLineId();

        MBusinessesHeadlineVo mBusinessesHeadlineVo = mBusinessesHeadlineService.queryById(headLineId);
        log.info("mBusinessesHeadlineVo----{}", mBusinessesHeadlineVo);
        MStoreVo mStoreVo = mStoreService.queryById(storeId);
        log.info("mStoreVo----{}",mStoreVo);
        String title = mBusinessesHeadlineVo.getTitle();
        String content = mBusinessesHeadlineVo.getContent();
        Long status = mBusinessesHeadlineVo.getStatus();
        if (status != 1L) {
            throw new ServiceException("请先发布改文案信息");
        }

        MStoreBo mStoreBo = new MStoreBo();
        mStoreBo.setId(storeId);
        mStoreBo.setTitle(title);
        mStoreBo.setHeadLine(content);
//        LambdaUpdateWrapper<MStore> updateWrapper = new LambdaUpdateWrapper<>();
//        updateWrapper.eq(MStore::getId, storeId)
//                .set(MStore::getTitle, title)
//                .set(MStore::getHeadLine, content);
        Boolean b = mStoreService.updateByBo(mStoreBo);
        return  b ? R.ok() : R.fail("绑定失败");    }



}
