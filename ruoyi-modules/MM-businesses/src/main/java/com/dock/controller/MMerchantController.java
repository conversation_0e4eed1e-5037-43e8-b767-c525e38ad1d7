package com.dock.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import com.dock.domain.vo.MMerchantVo;
import com.dock.domain.bo.MMerchantBo;
import com.dock.service.IMMerchantService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 商户
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/businesses/merchant")
public class MMerchantController extends BaseController {

    private final IMMerchantService mMerchantService;

    /**
     * 查询商户列表
     */
    @SaCheckPermission("businesses:merchant:list")
    @GetMapping("/list")
    public TableDataInfo<MMerchantVo> list(MMerchantBo bo, PageQuery pageQuery) {
        return mMerchantService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商户列表
     */
    @SaCheckPermission("businesses:merchant:export")
    @Log(title = "商户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MMerchantBo bo, HttpServletResponse response) {
        List<MMerchantVo> list = mMerchantService.queryList(bo);
        ExcelUtil.exportExcel(list, "商户", MMerchantVo.class, response);
    }

    /**
     * 获取商户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("businesses:merchant:query")
    @GetMapping("/{id}")
    public R<MMerchantVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mMerchantService.queryById(id));
    }

    /**
     * 新增商户
     */
    @SaCheckPermission("businesses:merchant:add")
    @Log(title = "商户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MMerchantBo bo) {
        return toAjax(mMerchantService.insertByBo(bo));
    }

    /**
     * 修改商户
     */
    @SaCheckPermission("businesses:merchant:edit")
    @Log(title = "商户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MMerchantBo bo) {
        return toAjax(mMerchantService.updateByBo(bo));
    }

    /**
     * 删除商户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("businesses:merchant:remove")
    @Log(title = "商户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mMerchantService.deleteWithValidByIds(List.of(ids), true));
    }
}
