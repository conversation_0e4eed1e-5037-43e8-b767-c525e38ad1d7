package com.dock.service;

import com.dock.domain.vo.MBusinessesVideoVo;
import com.dock.domain.bo.MBusinessesVideoBo;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 视频素材管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IMBusinessesVideoService {

    /**
     * 查询视频素材管理
     *
     * @param id 主键
     * @return 视频素材管理
     */
    MBusinessesVideoVo queryById(Long id);

    /**
     * 分页查询视频素材管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频素材管理分页列表
     */
    TableDataInfo<MBusinessesVideoVo> queryPageList(MBusinessesVideoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的视频素材管理列表
     *
     * @param bo 查询条件
     * @return 视频素材管理列表
     */
    List<MBusinessesVideoVo> queryList(MBusinessesVideoBo bo);

    /**
     * 新增视频素材管理
     *
     * @param bo 视频素材管理
     * @return 是否新增成功
     */
    Boolean insertByBo(MBusinessesVideoBo bo);

    /**
     * 修改视频素材管理
     *
     * @param bo 视频素材管理
     * @return 是否修改成功
     */
    Boolean updateByBo(MBusinessesVideoBo bo);

    /**
     * 校验并批量删除视频素材管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}
