package com.dock.service;

import com.dock.domain.req.NCFRequest;
import com.dock.domain.res.NFCResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 商户碰一碰数据回显接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface INFCActionService {

    List<NFCResponse> info(NCFRequest request) ;

    boolean bindVideosToStore(Long storeId, List<Long> ownVideoIds, List<Long> otherVideoIds);
}
