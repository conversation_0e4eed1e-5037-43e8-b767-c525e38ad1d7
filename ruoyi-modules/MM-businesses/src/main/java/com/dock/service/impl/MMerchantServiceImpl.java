package com.dock.service.impl;

import org.apache.ibatis.jdbc.Null;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MMerchantBo;
import com.dock.domain.vo.MMerchantVo;
import com.dock.domain.MMerchant;
import com.dock.mapper.MMerchantMapper;
import com.dock.service.IMMerchantService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 商户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MMerchantServiceImpl implements IMMerchantService {

    private final MMerchantMapper baseMapper;

    /**
     * 查询商户
     *
     * @param id 主键
     * @return 商户
     */
    @Override
    public MMerchantVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询商户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户分页列表
     */
    @Override
    public TableDataInfo<MMerchantVo> queryPageList(MMerchantBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MMerchant> lqw = buildQueryWrapper(bo);
        Page<MMerchantVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商户列表
     *
     * @param bo 查询条件
     * @return 商户列表
     */
    @Override
    public List<MMerchantVo> queryList(MMerchantBo bo) {
        LambdaQueryWrapper<MMerchant> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MMerchant> buildQueryWrapper(MMerchantBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MMerchant> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), MMerchant::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), MMerchant::getContactPhone, bo.getContactPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), MMerchant::getAddress, bo.getAddress());
        return lqw;
    }

    /**
     * 新增商户
     *
     * @param bo 商户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MMerchantBo bo) {
        // 获取当前登录用户的tenantId作为商户ID
        String tenantId = LoginHelper.getTenantId();
        log.info("tenantId：{}", tenantId);
        Long merchantId = Long.valueOf(tenantId);
        log.info("merchantId：{}", merchantId);
        // 设置商户ID为当前租户ID
        bo.setId(merchantId);
        MMerchant add = MapstructUtils.convert(bo, MMerchant.class);
        validEntityBeforeSave(add);
        bo.setId(null);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商户
     *
     * @param bo 商户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MMerchantBo bo) {
        MMerchant update = MapstructUtils.convert(bo, MMerchant.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MMerchant entity){

        //TODO 做一些数据校验,如唯一约束
        // 校验商户名称唯一性
        LambdaQueryWrapper<MMerchant> lqw = Wrappers.lambdaQuery();
        lqw.eq(MMerchant::getName, entity.getName());
        if (entity.getId() != null) {
            lqw.ne(MMerchant::getId, entity.getId());
        }
        if (baseMapper.selectCount(lqw) > 0) {
            throw new ServiceException("商户名称已存在");
        }

    }

    /**
     * 校验并批量删除商户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
