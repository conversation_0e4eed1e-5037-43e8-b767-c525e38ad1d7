package com.dock.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dock.domain.bo.MStoreBo;
import com.dock.domain.req.NCFRequest;
import com.dock.domain.res.NFCResponse;
import com.dock.mapper.MBusinessesVideoMapper;
import com.dock.service.IMBusinessesVideoService;
import com.dock.service.IMStoreService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.dock.domain.*;
import com.dock.domain.res.NFCResponse.VideoList;
import com.dock.mapper.MStoreMapper;
import com.dock.mapper.NFCActionMapper;
import com.dock.service.INFCActionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class NFCActionServiceImpl implements INFCActionService {


    private final String SHARE_TYPE = "H5";
    private final Converter converter;
    @Value("${NFC.douyin.client_key}")
    private String CLIENT_KEY;
    private final String NONCE_STR = UUID.randomUUID().toString().replace("-", "");
    private final String TIMESTAMP = String.valueOf(System.currentTimeMillis() / 1000);
    private final String SIGNATURE = generateSignature(NONCE_STR, TIMESTAMP);
    private final int SHARE_TO_PUBLISH = 1;
    private final String STATE = UUID.randomUUID().toString().replace("-", "");


    private final ObjectMapper objectMapper = new ObjectMapper(); // <--- 新增 ObjectMapper 实例
    private final IMStoreService imStoreService;
    private final MStoreMapper mStoreMapper;
    private final NFCActionMapper nfcActionMapper;
    private final MBusinessesVideoMapper mBusinessesVideoMapper;

    @Override
    public List<NFCResponse> info(NCFRequest request) {
//        LambdaQueryWrapper<MStore> mStoreLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        mStoreLambdaQueryWrapper.eq(MStore::getId, request.getStore_id())
//                .eq(MStore::getMerchantId, request.getMerchant_id());
//        MStore mStore = mStoreMapper.selectOne(mStoreLambdaQueryWrapper);

        MStore mStore = mStoreMapper.selectMStoreByIdAndMerchantId(request.getStore_id(), request.getMerchant_id());
        if (mStore == null) {
            throw new ServiceException("商户门店不存在");
        }

        // 计算selfVideo
        String selfVideoJsonFromDb = mStore.getVideoUrl();
        String title = mStore.getTitle();
        String headLine = mStore.getHeadLine();

        List<String> selfVideos = new ArrayList<>();
        if (selfVideoJsonFromDb != null && !selfVideoJsonFromDb.trim().isEmpty()) {
            try {
                // 使用 ObjectMapper 将 JSON 字符串反序列化为 List<String>
                selfVideos = objectMapper.readValue(selfVideoJsonFromDb, new TypeReference<List<String>>() {
                });
            } catch (JsonProcessingException e) {
                throw new ServiceException("数据库视频URL格式错误");
            }
        }

        // 解析 hashtag_list
        List<String> hashtagList = new ArrayList<>();
        if (headLine != null && !headLine.trim().isEmpty()) {
            try {
                // 首先尝试使用 ObjectMapper 解析标准 JSON 格式
                hashtagList = objectMapper.readValue(headLine, new TypeReference<List<String>>() {
                });
            } catch (JsonProcessingException e) {
                // 解析 List.toString() 格式的字符串
                hashtagList = parseListToStringFormat(headLine);
            }
        }


        // 计算otherVideo
//        LambdaQueryWrapper<NFCAction> actionLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        actionLambdaQueryWrapper.eq(NFCAction::getTenantId, mStore.getTenantId());
//        List<NFCAction> actionList = nfcActionMapper.selectList(actionLambdaQueryWrapper);
        List<NFCAction> actionList = nfcActionMapper.selectNFCActionByTenantId(mStore.getTenantId()); // 注意这里，NFCAction::getTenantId 是 String 类型

        List<String> otherVideos = actionList.stream()
                .map(NFCAction::getVideoUrl)
                .filter(Objects::nonNull)
                .toList();


        // 返回 otherVideo + selfVideo
        List<VideoList> videoLists = new ArrayList<>();
        VideoList videoItem = new VideoList();
        videoItem.setSelf(selfVideos);
        videoItem.setOther(otherVideos);
        videoLists.add(videoItem);


        // 计算count
        int count = 0;
        for (VideoList item : videoLists) {
            if (item.getSelf() != null) {
                count += item.getSelf().size();
            }
            if (item.getOther() != null) {
                count += item.getOther().size();
            }
        }

        // 构建 NFCResponse 对象
        NFCResponse response = new NFCResponse();

        response.setShare_type(SHARE_TYPE);
        response.setClient_key(CLIENT_KEY);
        response.setNonce_str(NONCE_STR);
        response.setTimestamp(TIMESTAMP);
        // ticket 可能含有这个参数 如果有就添加一个set
        response.setSignature(SIGNATURE);
        response.setShare_to_publish(SHARE_TO_PUBLISH);
        response.setState(STATE);
        response.setHashtag_list(hashtagList);
        response.setTitle(title);
        response.setVideo_list(videoLists);
        response.setCount(count);

        // 封装为集合
        List<NFCResponse> result = new ArrayList<>();
        result.add(response);

        return result;
    }

    @Override
    public boolean bindVideosToStore(Long storeId, List<Long> ownVideoIds, List<Long> otherVideoIds) {
        List<String> allVideoUrls = new ArrayList<>();

        // 查询自己的视频
        if (ownVideoIds != null && !ownVideoIds.isEmpty()) {
            List<MBusinessesVideo> ownVideos = mBusinessesVideoMapper.selectList(
                    new LambdaQueryWrapper<MBusinessesVideo>().in(MBusinessesVideo::getId, ownVideoIds)
            );
            if (ownVideos == null) {
                throw new ServiceException("未找到用户上传的视频素材");
            }
            List<String> ownUrls = ownVideos.stream()
                    .map(MBusinessesVideo::getVideoUrl)
                    .filter(Objects::nonNull)
                    .filter(url -> !url.isEmpty())
                    .toList();
            allVideoUrls.addAll(ownUrls);
        }

        // 查询他人的视频
        if (otherVideoIds != null && !otherVideoIds.isEmpty()) {
            List<NFCAction> otherVideos = nfcActionMapper.selectList(
                    new LambdaQueryWrapper<NFCAction>().in(NFCAction::getProductId, otherVideoIds)
            );
            if (otherVideos == null) {
                throw new ServiceException("未找到他人视频素材");
            }
            List<String> otherUrls = otherVideos.stream()
                    .map(NFCAction::getVideoUrl)
                    .filter(Objects::nonNull)
                    .filter(url -> !url.isEmpty())
                    .toList();
            allVideoUrls.addAll(otherUrls);
        }

        if (allVideoUrls.isEmpty()) {
            throw new ServiceException("无可绑定的视频 URL");
        }

        // 序列化为 JSON 字符串
        String combinedVideoUrls;
        try {
            combinedVideoUrls = objectMapper.writeValueAsString(allVideoUrls);
        } catch (JsonProcessingException e) {
            log.error("视频URL序列化失败", e);
            throw new ServiceException("视频URL处理失败");
        }

        log.info("最终绑定的视频URL：{}", combinedVideoUrls);
        MStoreBo storeBo = new MStoreBo();
        storeBo.setId(storeId);
        storeBo.setVideoUrl(combinedVideoUrls);

        Boolean result = imStoreService.updateByBo(storeBo);
        return result != null && result;
    }


    /**
     * 生成签名，只使用 nonce_str 和 timestamp
     */
    private String generateSignature(String nonceStr, String timestamp) {
        Map<String, String> params = new TreeMap<>();
        params.put("nonce_str", nonceStr);
        params.put("timestamp", timestamp);

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        sb.deleteCharAt(sb.length() - 1); // 删除最后一个 '&'

        return md5(sb.toString()).toUpperCase();
    }

    /**
     * MD5 加密
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexStr = new StringBuilder();
            for (byte b : bytes) {
                hexStr.append(String.format("%02x", b));
            }
            return hexStr.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成 MD5 签名失败", e);
        }
    }

    /**
     * 解析 List.toString() 格式的字符串
     * 处理类似 "[item1, item2, item3]" 的格式
     */
    private List<String> parseListToStringFormat(String input) {
        List<String> result = new ArrayList<>();

        try {
            String trimmed = input.trim();
            // 检查是否以 [ 开头并以 ] 结尾
            if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
                // 去掉首尾的方括号
                String content = trimmed.substring(1, trimmed.length() - 1).trim();

                if (!content.isEmpty()) {
                    // 简单按逗号分割（适用于不包含逗号的标签）
                    // 注意：如果标签本身包含逗号，这种简单分割会出问题
                    String[] items = content.split(",(?=([^\"]*\"[^\"]*\")*[^\"]*$)"); // 支持引号内的逗号

                    // 如果正则表达式太复杂，可以使用简单版本
                    // String[] items = content.split(", ");

                    for (String item : items) {
                        String cleanedItem = item.trim();
                        // 去除首尾可能的引号
                        if (cleanedItem.startsWith("\"") && cleanedItem.endsWith("\"") && cleanedItem.length() > 1) {
                            cleanedItem = cleanedItem.substring(1, cleanedItem.length() - 1);
                        }
                        if (!cleanedItem.isEmpty()) {
                            result.add(cleanedItem);
                        }
                    }
                }
            } else {
                // 如果不是数组格式，将整个字符串作为单个标签
                result.add(input);
            }
        } catch (Exception e) {
            // 如果解析失败，将整个字符串作为单个标签
            result.add(input);
        }

        return result;
    }


}

