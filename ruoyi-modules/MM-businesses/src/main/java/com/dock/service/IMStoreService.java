package com.dock.service;

import com.dock.domain.vo.MStoreVo;
import com.dock.domain.bo.MStoreBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 店铺Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface IMStoreService {

    /**
     * 查询店铺
     *
     * @param id 主键
     * @return 店铺
     */
    MStoreVo queryById(Long id);

    /**
     * 分页查询店铺列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 店铺分页列表
     */
    TableDataInfo<MStoreVo> queryPageList(MStoreBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的店铺列表
     *
     * @param bo 查询条件
     * @return 店铺列表
     */
    List<MStoreVo> queryList(MStoreBo bo);

    /**
     * 新增店铺
     *
     * @param bo 店铺
     * @return 是否新增成功
     */
    Boolean insertByBo(MStoreBo bo);

    /**
     * 修改店铺
     *
     * @param bo 店铺
     * @return 是否修改成功
     */
    Boolean updateByBo(MStoreBo bo);

    /**
     * 校验并批量删除店铺信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
