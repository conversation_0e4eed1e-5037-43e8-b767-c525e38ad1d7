package com.dock.service;

import com.dock.domain.vo.MBusinessesHeadlineVo;
import com.dock.domain.bo.MBusinessesHeadlineBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 商家文案Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IMBusinessesHeadlineService {

    /**
     * 查询商家文案
     *
     * @param id 主键
     * @return 商家文案
     */
    MBusinessesHeadlineVo queryById(Long id);

    /**
     * 分页查询商家文案列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商家文案分页列表
     */
    TableDataInfo<MBusinessesHeadlineVo> queryPageList(MBusinessesHeadlineBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商家文案列表
     *
     * @param bo 查询条件
     * @return 商家文案列表
     */
    List<MBusinessesHeadlineVo> queryList(MBusinessesHeadlineBo bo);

    /**
     * 新增商家文案
     *
     * @param bo 商家文案
     * @return 是否新增成功
     */
    Boolean insertByBo(MBusinessesHeadlineBo bo);

    /**
     * 修改商家文案
     *
     * @param bo 商家文案
     * @return 是否修改成功
     */
    Boolean updateByBo(MBusinessesHeadlineBo bo);

    /**
     * 校验并批量删除商家文案信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<String> generateComments(String title, int count);
}
