package com.dock.service;

import com.dock.domain.vo.MMerchantVo;
import com.dock.domain.bo.MMerchantBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 商户Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface IMMerchantService {

    /**
     * 查询商户
     *
     * @param id 主键
     * @return 商户
     */
    MMerchantVo queryById(Long id);

    /**
     * 分页查询商户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户分页列表
     */
    TableDataInfo<MMerchantVo> queryPageList(MMerchantBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商户列表
     *
     * @param bo 查询条件
     * @return 商户列表
     */
    List<MMerchantVo> queryList(MMerchantBo bo);

    /**
     * 新增商户
     *
     * @param bo 商户
     * @return 是否新增成功
     */
    Boolean insertByBo(MMerchantBo bo);

    /**
     * 修改商户
     *
     * @param bo 商户
     * @return 是否修改成功
     */
    Boolean updateByBo(MMerchantBo bo);

    /**
     * 校验并批量删除商户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
