package com.dock.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import com.dock.domain.bo.MBusinessesHeadlineBo;
import com.dock.domain.vo.MBusinessesHeadlineVo;
import com.dock.domain.MBusinessesHeadline;
import com.dock.mapper.MBusinessesHeadlineMapper;
import com.dock.service.IMBusinessesHeadlineService;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 商家文案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MBusinessesHeadlineServiceImpl implements IMBusinessesHeadlineService {


    @Value("${gemini.api.url}")
    private String apiUrl;

    @Value("${gemini.api.key}")
    private String apiKey;

    @Value("${gemini.api.model}")
    private String apiModel;

    @Value("${gemini.prompt.headline}")
    private String promptHeadLine;


    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final MBusinessesHeadlineMapper baseMapper;
    private final RestTemplate restTemplate;


    /**
     * 查询商家文案
     *
     * @param id 主键
     * @return 商家文案
     */
    @Override
    public MBusinessesHeadlineVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询商家文案列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商家文案分页列表
     */
    @Override
    public TableDataInfo<MBusinessesHeadlineVo> queryPageList(MBusinessesHeadlineBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MBusinessesHeadline> lqw = buildQueryWrapper(bo);
        Page<MBusinessesHeadlineVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商家文案列表
     *
     * @param bo 查询条件
     * @return 商家文案列表
     */
    @Override
    public List<MBusinessesHeadlineVo> queryList(MBusinessesHeadlineBo bo) {
        LambdaQueryWrapper<MBusinessesHeadline> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MBusinessesHeadline> buildQueryWrapper(MBusinessesHeadlineBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MBusinessesHeadline> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MBusinessesHeadline::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), MBusinessesHeadline::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), MBusinessesHeadline::getContent, bo.getContent());
        lqw.eq(bo.getStatus() != null, MBusinessesHeadline::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增商家文案
     *
     * @param bo 商家文案
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MBusinessesHeadlineBo bo) {
        MBusinessesHeadline add = MapstructUtils.convert(bo, MBusinessesHeadline.class);
        validEntityBeforeSave(add);
        bo.setId(null);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商家文案
     *
     * @param bo 商家文案
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MBusinessesHeadlineBo bo) {
        MBusinessesHeadline update = MapstructUtils.convert(bo, MBusinessesHeadline.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MBusinessesHeadline entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除商家文案信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }



    @Override
    public List<String> generateComments(String title, int count) {

        if (count <= 0) {
            count = 1;
        }

        if (title == null || title.isEmpty()) {
            throw new ServiceException("标题不能为空");
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        Set<String> uniqueComments = new LinkedHashSet<>();

        while (uniqueComments.size() < count) {
            int remain = count - uniqueComments.size();

            String prompt = promptHeadLine + title + "，请生成" + remain + "条短评，格式为JSON数组，每条一句话，不要有多余的文字。";

            Map<String, Object> requestBody = Map.of(
                    "model", apiModel,
                    "messages", List.of(
                            Map.of("role", "user", "content", prompt)
                    ),
                    "temperature", 0.7
            );

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            try {
                ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, requestEntity, Map.class);
                if (response.getStatusCode() == HttpStatus.OK) {
                    Map<?, ?> body = response.getBody();
                    List<?> choices = (List<?>) body.get("choices");
                    if (choices != null && !choices.isEmpty()) {
                        Map<?, ?> firstChoice = (Map<?, ?>) choices.get(0);
                        Map<?, ?> message = (Map<?, ?>) firstChoice.get("message");
                        String content = (String) message.get("content");

                        // 去掉markdown代码块格式
                        content = content.replaceAll("(?s)```json\\s*", "").replaceAll("(?s)```", "").trim();

                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            List<String> result = objectMapper.readValue(content, new TypeReference<List<String>>() {});
                            uniqueComments.addAll(result);
                        } catch (Exception ex) {
                            log.error("解析返回内容失败: {}", content, ex);
                            // 防止死循环，加入原始字符串
                            uniqueComments.add(content);
                        }
                    }
                } else {
                    log.error("调用Gemini失败，状态码：{}", response.getStatusCode());
                    break;
                }
            } catch (Exception e) {
                log.error("调用Gemini接口异常", e);
                break;
            }
        }

        List<String> allComments = new ArrayList<>(uniqueComments);
        return allComments.subList(0, Math.min(allComments.size(), count));
    }


}
