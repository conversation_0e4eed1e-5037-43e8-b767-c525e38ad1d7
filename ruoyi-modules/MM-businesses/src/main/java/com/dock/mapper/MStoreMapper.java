package com.dock.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.dock.domain.MStore;
import com.dock.domain.vo.MStoreVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 店铺Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Mapper
public interface MStoreMapper extends BaseMapperPlus<MStore, MStoreVo> {

    /**
     * 根据门店ID和商户ID查询门店信息
     * @param storeId 门店ID
     * @param merchantId 商户ID
     * @return 匹配的门店实体
     */
    @InterceptorIgnore(tenantLine = "true", dataPermission = "false")
    @Select("SELECT id, merchant_id, store_name, store_phone, store_address, video_url, tenant_id, del_flag,title,headline FROM m_store " +
            "WHERE id = #{storeId} AND merchant_id = #{merchantId} AND del_flag = 0")
    MStore selectMStoreByIdAndMerchantId(@Param("storeId") Long storeId, @Param("merchantId") Long merchantId);

}
