package com.dock.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dock.domain.NFCAction;
import com.dock.domain.vo.NFCActionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

@Mapper
public interface NFCActionMapper extends BaseMapperPlus<NFCAction, NFCActionVo> {



    /**
     * 根据租户ID查询NFCAction列表
     * @param tenantId 租户ID
     * @return 匹配的NFCAction实体列表
     */
    @Select("SELECT tenant_id, video_url FROM media_product WHERE tenant_id = #{tenantId} AND del_flag = 0 AND status = 0")
    List<NFCAction> selectNFCActionByTenantId(@Param("tenantId") String tenantId);
}
