import { request } from '@/service/request';

/** 获取商户列表 */
export function fetchGetMerchantList(params?: Api.Businesses.MerchantSearchParams) {
  return request<Api.Businesses.MerchantList>({
    url: '/businesses/merchant/list',
    method: 'get',
    params
  });
}

/** 新增商户 */
export function fetchCreateMerchant(data: Api.Businesses.MerchantOperateParams) {
  return request<boolean>({
    url: '/businesses/merchant',
    method: 'post',
    data
  });
}

/** 修改商户 */
export function fetchUpdateMerchant(data: Api.Businesses.MerchantOperateParams) {
  return request<boolean>({
    url: '/businesses/merchant',
    method: 'put',
    data
  });
}

/** 批量删除商户 */
export function fetchBatchDeleteMerchant(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/businesses/merchant/${ids.join(',')}`,
    method: 'delete'
  });
}
