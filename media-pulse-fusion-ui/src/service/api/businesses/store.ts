import { request } from '@/service/request';

/** 获取店铺列表 */
export function fetchGetStoreList(params?: Api.Businesses.StoreSearchParams) {
  return request<Api.Businesses.StoreList>({
    url: '/businesses/store/list',
    method: 'get',
    params
  });
}

/** 新增店铺 */
export function fetchCreateStore(data: Api.Businesses.StoreOperateParams) {
  return request<boolean>({
    url: '/businesses/store',
    method: 'post',
    data
  });
}

/** 修改店铺 */
export function fetchUpdateStore(data: Api.Businesses.StoreOperateParams) {
  return request<boolean>({
    url: '/businesses/store',
    method: 'put',
    data
  });
}

/** 批量删除店铺 */
export function fetchBatchDeleteStore(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/businesses/store/${ids.join(',')}`,
    method: 'delete'
  });
}
