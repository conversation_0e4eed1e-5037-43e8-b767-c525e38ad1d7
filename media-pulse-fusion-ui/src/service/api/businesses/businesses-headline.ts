import { request } from '@/service/request';

/** 获取商家文案列表 */
export function fetchGetBusinessesHeadlineList(params?: Api.Businesses.BusinessesHeadlineSearchParams) {
  return request<Api.Businesses.BusinessesHeadlineList>({
    url: '/businesses/businessesHeadline/list',
    method: 'get',
    params
  });
}

/** 新增商家文案 */
export function fetchCreateBusinessesHeadline(data: Api.Businesses.BusinessesHeadlineOperateParams) {
  return request<boolean>({
    url: '/businesses/businessesHeadline',
    method: 'post',
    data
  });
}

/** 修改商家文案 */
export function fetchUpdateBusinessesHeadline(data: Api.Businesses.BusinessesHeadlineOperateParams) {
  return request<boolean>({
    url: '/businesses/businessesHeadline',
    method: 'put',
    data
  });
}

/** 批量删除商家文案 */
export function fetchBatchDeleteBusinessesHeadline(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/businesses/businessesHeadline/${ids.join(',')}`,
    method: 'delete'
  });
}

/** 根据标题生成商家文案 */
export function fetchRequestBusinessesHeadline(data: Api.Businesses.HeadLineRequest) {
  return request<Api.Businesses.HeadlineResponse>({
    url: '/businesses/businessesHeadline/request',
    method: 'post',
    data,
    timeout: 3000000
  });
}

/** 绑定商家文案到店铺 */
export function fetchBindBusinessesHeadline(data: Api.Businesses.StoreHeadLineBindRequest) {
  return request<boolean>({
    url: '/businesses/businessesHeadline/bind',
    method: 'post',
    data
  });
}
