import { request } from '@/service/request';

/** 获取积分变动记录列表 */
export function fetchGetPointsPointsList(params?: Api.Points.PointsPointsSearchParams) {
  return request<Api.Points.PointsPointsList>({
    url: '/points/pointsPoints/list',
    method: 'get',
    params
  });
}

/** 新增积分变动记录 */
export function fetchCreatePointsPoints(data: Api.Points.PointsPointsOperateParams) {
  return request<boolean>({
    url: '/points/pointsPoints',
    method: 'post',
    data
  });
}

/** 修改积分变动记录 */
export function fetchUpdatePointsPoints(data: Api.Points.PointsPointsOperateParams) {
  return request<boolean>({
    url: '/points/pointsPoints',
    method: 'put',
    data
  });
}

/** 批量删除积分变动记录 */
export function fetchBatchDeletePointsPoints(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/points/pointsPoints/${ids.join(',')}`,
    method: 'delete'
  });
}


/** 查询用户当前积分 */
export function fetchGetUserPoints() {
  return request<number>({
    url: '/points/pointsPoints/self/points',
    method: 'get'
  });
}
