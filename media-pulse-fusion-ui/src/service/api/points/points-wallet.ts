import { request } from '@/service/request';

/** 获取用户钱包列表 */
export function fetchGetPointsWalletList(params?: Api.Points.PointsWalletSearchParams) {
  return request<Api.Points.PointsWalletList>({
    url: '/points/pointsWallet/list',
    method: 'get',
    params
  });
}

/** 新增用户钱包 */
export function fetchCreatePointsWallet(data: Api.Points.PointsWalletOperateParams) {
  return request<boolean>({
    url: '/points/pointsWallet',
    method: 'post',
    data
  });
}

/** 修改用户钱包 */
export function fetchUpdatePointsWallet(data: Api.Points.PointsWalletOperateParams) {
  return request<boolean>({
    url: '/points/pointsWallet',
    method: 'put',
    data
  });
}

/** 批量删除用户钱包 */
export function fetchBatchDeletePointsWallet(ids: CommonType.IdType[]) {
  return request<boolean>({
    url: `/points/pointsWallet/${ids.join(',')}`,
    method: 'delete'
  });
}
