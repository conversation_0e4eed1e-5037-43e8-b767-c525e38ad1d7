import { request } from '@/service/request';

/** 获取积分订单列表 */
export function fetchGetPointsOrderList (params?: Api.Points.PointsOrderSearchParams) {
    return request<Api.Points.PointsOrderList>({
        url: '/points/pointsOrder/list',
        method: 'get',
        params
    });
}

/** 新增积分订单 */
export function fetchCreatePointsOrder (data: Api.Points.PointsOrderOperateParams) {
    return request<boolean>({
        url: '/points/pointsOrder',
        method: 'post',
        data
    });
}

/** 修改积分订单 */
export function fetchUpdatePointsOrder (data: Api.Points.PointsOrderOperateParams) {
    return request<boolean>({
        url: '/points/pointsOrder',
        method: 'put',
        data
    });
}

/** 批量删除积分订单 */
export function fetchBatchDeletePointsOrder (ids: CommonType.IdType[]) {
    return request<boolean>({
        url: `/points/pointsOrder/${ids.join(',')}`,
        method: 'delete'
    });
}
