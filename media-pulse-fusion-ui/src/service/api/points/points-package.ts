import { request } from '@/service/request';

/** 获取积分套餐列表 */
export function fetchGetPointsPackageList (params?: Api.Points.PointsPackageSearchParams) {
    return request<Api.Points.PointsPackageList>({
        url: '/points/pointsPackage/list',
        method: 'get',
        params
    });
}

/** 新增积分套餐 */
export function fetchCreatePointsPackage (data: Api.Points.PointsPackageOperateParams) {
    return request<boolean>({
        url: '/points/pointsPackage',
        method: 'post',
        data
    });
}

/** 修改积分套餐 */
export function fetchUpdatePointsPackage (data: Api.Points.PointsPackageOperateParams) {
    return request<boolean>({
        url: '/points/pointsPackage',
        method: 'put',
        data
    });
}

/** 批量删除积分套餐 */
export function fetchBatchDeletePointsPackage (ids: CommonType.IdType[]) {
    return request<boolean>({
        url: `/points/pointsPackage/${ids.join(',')}`,
        method: 'delete'
    });
}
