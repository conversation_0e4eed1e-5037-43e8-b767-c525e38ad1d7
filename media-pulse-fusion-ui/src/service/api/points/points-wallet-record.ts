import { request } from '@/service/request';

/** 获取钱包流水记录列表 */
export function fetchGetPointsWalletRecordList (params?: Api.Points.PointsWalletRecordSearchParams) {
    return request<Api.Points.PointsWalletRecordList>({
        url: '/points/pointsWalletRecord/list',
        method: 'get',
        params
    });
}

/** 新增钱包流水记录 */
export function fetchCreatePointsWalletRecord (data: Api.Points.PointsWalletRecordOperateParams) {
    return request<boolean>({
        url: '/points/pointsWalletRecord',
        method: 'post',
        data
    });
}

/** 修改钱包流水记录 */
export function fetchUpdatePointsWalletRecord (data: Api.Points.PointsWalletRecordOperateParams) {
    return request<boolean>({
        url: '/points/pointsWalletRecord',
        method: 'put',
        data
    });
}

/** 批量删除钱包流水记录 */
export function fetchBatchDeletePointsWalletRecord (ids: CommonType.IdType[]) {
    return request<boolean>({
        url: `/points/pointsWalletRecord/${ids.join(',')}`,
        method: 'delete'
    });
}
