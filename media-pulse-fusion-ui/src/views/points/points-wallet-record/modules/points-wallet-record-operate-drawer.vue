<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreatePointsWalletRecord, fetchUpdatePointsWalletRecord } from '@/service/api/points/points-wallet-record';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsWalletRecordOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Points.PointsWalletRecord | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增钱包流水记录',
    edit: '编辑钱包流水记录'
  };
  return titles[props.operateType];
});

type Model = Api.Points.PointsWalletRecordOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      userId: undefined,
      walletId: undefined,
      tradeNo: '',
      tradeType: '',
      tradeReason: '',
      amount: undefined,
      balanceBefore: undefined,
      balanceAfter: undefined,
      relatedId: undefined,
      relatedType: '',
      paymentMethod: '',
      paymentNo: '',
      description: '',
      clientIp: '',
      remark: ''
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'tenantId'
  | 'relatedId'
  | 'relatedType'
  | 'paymentMethod'
  | 'paymentNo'
  | 'description'
  | 'clientIp'
  | 'delFlag'
  | 'createDept'
  | 'createBy'
  | 'createTime'
  | 'updateBy'
  | 'updateTime'
  | 'remark'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  tenantId: createRequiredRule('租户编号不能为空'),
  relatedId: createRequiredRule('关联ID不能为空'),
  relatedType: createRequiredRule('关联类型不能为空'),
  paymentMethod: createRequiredRule('支付方式不能为空'),
  paymentNo: createRequiredRule('支付流水号不能为空'),
  description: createRequiredRule('描述说明不能为空'),
  clientIp: createRequiredRule('客户端IP不能为空'),
  delFlag: createRequiredRule('删除标志（0代表存在 1代表删除）不能为空'),
  createDept: createRequiredRule('创建部门不能为空'),
  createBy: createRequiredRule('创建者不能为空'),
  createTime: createRequiredRule('创建时间不能为空'),
  updateBy: createRequiredRule('更新者不能为空'),
  updateTime: createRequiredRule('更新时间不能为空'),
  remark: createRequiredRule('备注不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, userId, walletId, tradeNo, tradeType, tradeReason, amount, balanceBefore, balanceAfter, relatedId, relatedType, paymentMethod, paymentNo, description, clientIp, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreatePointsWalletRecord({ userId, walletId, tradeNo, tradeType, tradeReason, amount, balanceBefore, balanceAfter, relatedId, relatedType, paymentMethod, paymentNo, description, clientIp, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdatePointsWalletRecord({ id, userId, walletId, tradeNo, tradeType, tradeReason, amount, balanceBefore, balanceAfter, relatedId, relatedType, paymentMethod, paymentNo, description, clientIp, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="用户ID" path="userId">
          <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
        </NFormItem>
        <NFormItem label="钱包ID" path="walletId">
          <NInput v-model:value="model.walletId" placeholder="请输入钱包ID" />
        </NFormItem>
        <NFormItem label="交易流水号" path="tradeNo">
          <NInput v-model:value="model.tradeNo" placeholder="请输入交易流水号" />
        </NFormItem>
        <NFormItem label="交易类型（1收入 2支出）" path="tradeType">
          <NInput v-model:value="model.tradeType" placeholder="请输入交易类型（1收入 2支出）" />
        </NFormItem>
        <NFormItem label="交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）" path="tradeReason">
          <NInput v-model:value="model.tradeReason" placeholder="请输入交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）" />
        </NFormItem>
        <NFormItem label="交易金额" path="amount">
          <NInput v-model:value="model.amount" placeholder="请输入交易金额" />
        </NFormItem>
        <NFormItem label="交易前余额" path="balanceBefore">
          <NInput v-model:value="model.balanceBefore" placeholder="请输入交易前余额" />
        </NFormItem>
        <NFormItem label="交易后余额" path="balanceAfter">
          <NInput v-model:value="model.balanceAfter" placeholder="请输入交易后余额" />
        </NFormItem>
        <NFormItem label="关联ID" path="relatedId">
          <NInput v-model:value="model.relatedId" placeholder="请输入关联ID" />
        </NFormItem>
        <NFormItem label="关联类型" path="relatedType">
          <NInput v-model:value="model.relatedType" placeholder="请输入关联类型" />
        </NFormItem>
        <NFormItem label="支付方式" path="paymentMethod">
          <NInput v-model:value="model.paymentMethod" placeholder="请输入支付方式" />
        </NFormItem>
        <NFormItem label="支付流水号" path="paymentNo">
          <NInput v-model:value="model.paymentNo" placeholder="请输入支付流水号" />
        </NFormItem>
        <NFormItem label="描述说明" path="description">
          <NInput v-model:value="model.description" placeholder="请输入描述说明" />
        </NFormItem>
        <NFormItem label="客户端IP" path="clientIp">
          <NInput v-model:value="model.clientIp" placeholder="请输入客户端IP" />
        </NFormItem>
        <NFormItem label="备注" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入备注"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
