<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsWalletRecordSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Points.PointsWalletRecordSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="用户ID" path="userId" class="pr-24px">
              <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="钱包ID" path="walletId" class="pr-24px">
              <NInput v-model:value="model.walletId" placeholder="请输入钱包ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="交易流水号" path="tradeNo" class="pr-24px">
              <NInput v-model:value="model.tradeNo" placeholder="请输入交易流水号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="交易类型（1收入 2支出）" path="tradeType" class="pr-24px">
              <NSelect
                v-model:value="model.tradeType"
                placeholder="请选择交易类型（1收入 2支出）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）" path="tradeReason" class="pr-24px">
              <NInput v-model:value="model.tradeReason" placeholder="请输入交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="交易金额" path="amount" class="pr-24px">
              <NInput v-model:value="model.amount" placeholder="请输入交易金额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="交易前余额" path="balanceBefore" class="pr-24px">
              <NInput v-model:value="model.balanceBefore" placeholder="请输入交易前余额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="交易后余额" path="balanceAfter" class="pr-24px">
              <NInput v-model:value="model.balanceAfter" placeholder="请输入交易后余额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="关联ID" path="relatedId" class="pr-24px">
              <NInput v-model:value="model.relatedId" placeholder="请输入关联ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="关联类型" path="relatedType" class="pr-24px">
              <NSelect
                v-model:value="model.relatedType"
                placeholder="请选择关联类型"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="支付方式" path="paymentMethod" class="pr-24px">
              <NInput v-model:value="model.paymentMethod" placeholder="请输入支付方式" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="支付流水号" path="paymentNo" class="pr-24px">
              <NInput v-model:value="model.paymentNo" placeholder="请输入支付流水号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="描述说明" path="description" class="pr-24px">
              <NInput v-model:value="model.description" placeholder="请输入描述说明" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="客户端IP" path="clientIp" class="pr-24px">
              <NInput v-model:value="model.clientIp" placeholder="请输入客户端IP" />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
