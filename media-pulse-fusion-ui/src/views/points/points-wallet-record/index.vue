<script setup lang="tsx">
import { NDivider } from 'naive-ui';
import { fetchBatchDeletePointsWalletRecord, fetchGetPointsWalletRecordList } from '@/service/api/points/points-wallet-record';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import PointsWalletRecordOperateDrawer from './modules/points-wallet-record-operate-drawer.vue';
import PointsWalletRecordSearch from './modules/points-wallet-record-search.vue';

defineOptions({
  name: 'PointsWalletRecordList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetPointsWalletRecordList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    userId: null,
    walletId: null,
    tradeNo: null,
    tradeType: null,
    tradeReason: null,
    amount: null,
    balanceBefore: null,
    balanceAfter: null,
    relatedId: null,
    relatedType: null,
    paymentMethod: null,
    paymentNo: null,
    description: null,
    clientIp: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'userId',
      title: '用户ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'walletId',
      title: '钱包ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'tradeNo',
      title: '交易流水号',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'tradeType',
      title: '交易类型（1收入 2支出）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'tradeReason',
      title: '交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'amount',
      title: '交易金额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'balanceBefore',
      title: '交易前余额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'balanceAfter',
      title: '交易后余额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'relatedId',
      title: '关联ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'relatedType',
      title: '关联类型',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'paymentMethod',
      title: '支付方式',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'paymentNo',
      title: '支付流水号',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'description',
      title: '描述说明',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'clientIp',
      title: '客户端IP',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'remark',
      title: '备注',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('points:pointsWalletRecord:edit') || !hasAuth('points:pointsWalletRecord:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('points:pointsWalletRecord:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('points:pointsWalletRecord:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeletePointsWalletRecord(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeletePointsWalletRecord([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/points/pointsWalletRecord/export', searchParams, `钱包流水记录_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <PointsWalletRecordSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="钱包流水记录列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('points:pointsWalletRecord:add')"
          :show-delete="hasAuth('points:pointsWalletRecord:remove')"
          :show-export="hasAuth('points:pointsWalletRecord:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <PointsWalletRecordOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
