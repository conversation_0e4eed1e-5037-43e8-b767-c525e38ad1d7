<script setup lang="tsx">
import { ref, onMounted } from 'vue';
import { NDivider, NStatistic, NCard, NSpace, NButton } from 'naive-ui';
import { fetchBatchDeletePointsPoints, fetchGetPointsPointsList, fetchGetUserPoints } from '@/service/api/points/points-points';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import PointsPointsOperateDrawer from './modules/points-points-operate-drawer.vue';
import PointsPointsSearch from './modules/points-points-search.vue';

defineOptions({
  name: 'PointsPointsList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();


const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetPointsPointsList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    userId: null,
    changeType: null,
    changeReason: null,
    pointsAmount: null,
    balanceBefore: null,
    balanceAfter: null,
    relatedId: null,
    relatedType: null,
    expireTime: null,
    description: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'userId',
      title: '用户ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'changeType',
      title: '变动类型（1收入 2支出）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'changeReason',
      title: '变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'pointsAmount',
      title: '积分数量',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'balanceBefore',
      title: '变动前余额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'balanceAfter',
      title: '变动后余额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'relatedId',
      title: '关联ID（订单ID、消费记录ID等）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'relatedType',
      title: '关联类型（order订单 consume消费 gift赠送）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'expireTime',
      title: '积分过期时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'description',
      title: '描述说明',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'remark',
      title: '备注',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('points:pointsPoints:edit') || !hasAuth('points:pointsPoints:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('points:pointsPoints:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('points:pointsPoints:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeletePointsPoints(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeletePointsPoints([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/points/pointsPoints/export', searchParams, `积分变动记录_${new Date().getTime()}.xlsx`);
}

</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">


    <!-- 搜索组件 -->
    <PointsPointsSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="积分变动记录列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('points:pointsPoints:add')"
          :show-delete="hasAuth('points:pointsPoints:remove')"
          :show-export="hasAuth('points:pointsPoints:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <PointsPointsOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
