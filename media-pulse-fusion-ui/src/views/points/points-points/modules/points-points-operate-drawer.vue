<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreatePointsPoints, fetchUpdatePointsPoints } from '@/service/api/points/points-points';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsPointsOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Points.PointsPoints | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增积分变动记录',
    edit: '编辑积分变动记录'
  };
  return titles[props.operateType];
});

type Model = Api.Points.PointsPointsOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      userId: undefined,
      changeType: '',
      changeReason: '',
      pointsAmount: undefined,
      balanceBefore: undefined,
      balanceAfter: undefined,
      relatedId: undefined,
      relatedType: '',
      expireTime: undefined,
      description: '',
      remark: ''
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'tenantId'
  | 'relatedId'
  | 'relatedType'
  | 'expireTime'
  | 'description'
  | 'delFlag'
  | 'createDept'
  | 'createBy'
  | 'createTime'
  | 'updateBy'
  | 'updateTime'
  | 'remark'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  tenantId: createRequiredRule('租户编号不能为空'),
  relatedId: createRequiredRule('关联ID（订单ID、消费记录ID等）不能为空'),
  relatedType: createRequiredRule('关联类型（order订单 consume消费 gift赠送）不能为空'),
  expireTime: createRequiredRule('积分过期时间不能为空'),
  description: createRequiredRule('描述说明不能为空'),
  delFlag: createRequiredRule('删除标志（0代表存在 1代表删除）不能为空'),
  createDept: createRequiredRule('创建部门不能为空'),
  createBy: createRequiredRule('创建者不能为空'),
  createTime: createRequiredRule('创建时间不能为空'),
  updateBy: createRequiredRule('更新者不能为空'),
  updateTime: createRequiredRule('更新时间不能为空'),
  remark: createRequiredRule('备注不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, userId, changeType, changeReason, pointsAmount, balanceBefore, balanceAfter, relatedId, relatedType, expireTime, description, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreatePointsPoints({ userId, changeType, changeReason, pointsAmount, balanceBefore, balanceAfter, relatedId, relatedType, expireTime, description, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdatePointsPoints({ id, userId, changeType, changeReason, pointsAmount, balanceBefore, balanceAfter, relatedId, relatedType, expireTime, description, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="用户ID" path="userId">
          <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
        </NFormItem>
        <NFormItem label="变动类型（1收入 2支出）" path="changeType">
          <NInput v-model:value="model.changeType" placeholder="请输入变动类型（1收入 2支出）" />
        </NFormItem>
        <NFormItem label="变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）" path="changeReason">
          <NInput v-model:value="model.changeReason" placeholder="请输入变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）" />
        </NFormItem>
        <NFormItem label="积分数量" path="pointsAmount">
          <NInput v-model:value="model.pointsAmount" placeholder="请输入积分数量" />
        </NFormItem>
        <NFormItem label="变动前余额" path="balanceBefore">
          <NInput v-model:value="model.balanceBefore" placeholder="请输入变动前余额" />
        </NFormItem>
        <NFormItem label="变动后余额" path="balanceAfter">
          <NInput v-model:value="model.balanceAfter" placeholder="请输入变动后余额" />
        </NFormItem>
        <NFormItem label="关联ID（订单ID、消费记录ID等）" path="relatedId">
          <NInput v-model:value="model.relatedId" placeholder="请输入关联ID（订单ID、消费记录ID等）" />
        </NFormItem>
        <NFormItem label="关联类型（order订单 consume消费 gift赠送）" path="relatedType">
          <NInput v-model:value="model.relatedType" placeholder="请输入关联类型（order订单 consume消费 gift赠送）" />
        </NFormItem>
        <NFormItem label="积分过期时间" path="expireTime">
          <NDatePicker
            v-model:formatted-value="model.expireTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="描述说明" path="description">
          <NInput v-model:value="model.description" placeholder="请输入描述说明" />
        </NFormItem>
        <NFormItem label="备注" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入备注"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
