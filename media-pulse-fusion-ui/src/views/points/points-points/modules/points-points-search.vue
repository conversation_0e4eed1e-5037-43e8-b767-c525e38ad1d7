<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsPointsSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Points.PointsPointsSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="用户ID" path="userId" class="pr-24px">
              <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="变动类型（1收入 2支出）" path="changeType" class="pr-24px">
              <NSelect
                v-model:value="model.changeType"
                placeholder="请选择变动类型（1收入 2支出）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）" path="changeReason" class="pr-24px">
              <NInput v-model:value="model.changeReason" placeholder="请输入变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="积分数量" path="pointsAmount" class="pr-24px">
              <NInput v-model:value="model.pointsAmount" placeholder="请输入积分数量" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="变动前余额" path="balanceBefore" class="pr-24px">
              <NInput v-model:value="model.balanceBefore" placeholder="请输入变动前余额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="变动后余额" path="balanceAfter" class="pr-24px">
              <NInput v-model:value="model.balanceAfter" placeholder="请输入变动后余额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="关联ID（订单ID、消费记录ID等）" path="relatedId" class="pr-24px">
              <NInput v-model:value="model.relatedId" placeholder="请输入关联ID（订单ID、消费记录ID等）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="关联类型（order订单 consume消费 gift赠送）" path="relatedType" class="pr-24px">
              <NSelect
                v-model:value="model.relatedType"
                placeholder="请选择关联类型（order订单 consume消费 gift赠送）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="积分过期时间" path="expireTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.expireTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="描述说明" path="description" class="pr-24px">
              <NInput v-model:value="model.description" placeholder="请输入描述说明" />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
