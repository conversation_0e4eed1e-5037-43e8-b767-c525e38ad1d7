<script setup lang="tsx">
import { NDivider } from 'naive-ui';
import { fetchBatchDeletePointsWallet, fetchGetPointsWalletList } from '@/service/api/points/points-wallet';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import PointsWalletOperateDrawer from './modules/points-wallet-operate-drawer.vue';
import PointsWalletSearch from './modules/points-wallet-search.vue';

defineOptions({
  name: 'PointsWalletList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetPointsWalletList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    userId: null,
    balance: null,
    frozenAmount: null,
    totalRecharge: null,
    totalConsume: null,
    pointsBalance: null,
    totalPoints: null,
    usedPoints: null,
    payPassword: null,
    isPayPwdSet: null,
    status: null,
    lastTradeTime: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'userId',
      title: '用户ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'balance',
      title: '余额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'frozenAmount',
      title: '冻结金额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'totalRecharge',
      title: '累计充值',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'totalConsume',
      title: '累计消费',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'pointsBalance',
      title: '积分余额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'totalPoints',
      title: '累计获得积分',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'usedPoints',
      title: '累计使用积分',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'payPassword',
      title: '支付密码',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'isPayPwdSet',
      title: '是否设置支付密码（0否 1是）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: '钱包状态（0正常 1冻结 2注销）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'lastTradeTime',
      title: '最后交易时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'remark',
      title: '备注',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('points:pointsWallet:edit') || !hasAuth('points:pointsWallet:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('points:pointsWallet:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('points:pointsWallet:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeletePointsWallet(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeletePointsWallet([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/points/pointsWallet/export', searchParams, `用户钱包_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <PointsWalletSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="用户钱包列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('points:pointsWallet:add')"
          :show-delete="hasAuth('points:pointsWallet:remove')"
          :show-export="hasAuth('points:pointsWallet:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <PointsWalletOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
