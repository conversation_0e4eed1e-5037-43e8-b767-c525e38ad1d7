<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreatePointsWallet, fetchUpdatePointsWallet } from '@/service/api/points/points-wallet';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsWalletOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Points.PointsWallet | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增用户钱包',
    edit: '编辑用户钱包'
  };
  return titles[props.operateType];
});

type Model = Api.Points.PointsWalletOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      userId: undefined,
      balance: undefined,
      frozenAmount: undefined,
      totalRecharge: undefined,
      totalConsume: undefined,
      pointsBalance: undefined,
      totalPoints: undefined,
      usedPoints: undefined,
      payPassword: '',
      isPayPwdSet: '',
      status: '',
      lastTradeTime: undefined,
      remark: ''
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'tenantId'
  | 'balance'
  | 'frozenAmount'
  | 'totalRecharge'
  | 'totalConsume'
  | 'pointsBalance'
  | 'totalPoints'
  | 'usedPoints'
  | 'payPassword'
  | 'isPayPwdSet'
  | 'status'
  | 'lastTradeTime'
  | 'version'
  | 'delFlag'
  | 'createDept'
  | 'createBy'
  | 'createTime'
  | 'updateBy'
  | 'updateTime'
  | 'remark'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  tenantId: createRequiredRule('租户编号不能为空'),
  balance: createRequiredRule('余额不能为空'),
  frozenAmount: createRequiredRule('冻结金额不能为空'),
  totalRecharge: createRequiredRule('累计充值不能为空'),
  totalConsume: createRequiredRule('累计消费不能为空'),
  pointsBalance: createRequiredRule('积分余额不能为空'),
  totalPoints: createRequiredRule('累计获得积分不能为空'),
  usedPoints: createRequiredRule('累计使用积分不能为空'),
  payPassword: createRequiredRule('支付密码不能为空'),
  isPayPwdSet: createRequiredRule('是否设置支付密码（0否 1是）不能为空'),
  status: createRequiredRule('钱包状态（0正常 1冻结 2注销）不能为空'),
  lastTradeTime: createRequiredRule('最后交易时间不能为空'),
  version: createRequiredRule('版本号（乐观锁）不能为空'),
  delFlag: createRequiredRule('删除标志（0代表存在 1代表删除）不能为空'),
  createDept: createRequiredRule('创建部门不能为空'),
  createBy: createRequiredRule('创建者不能为空'),
  createTime: createRequiredRule('创建时间不能为空'),
  updateBy: createRequiredRule('更新者不能为空'),
  updateTime: createRequiredRule('更新时间不能为空'),
  remark: createRequiredRule('备注不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, userId, balance, frozenAmount, totalRecharge, totalConsume, pointsBalance, totalPoints, usedPoints, payPassword, isPayPwdSet, status, lastTradeTime, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreatePointsWallet({ userId, balance, frozenAmount, totalRecharge, totalConsume, pointsBalance, totalPoints, usedPoints, payPassword, isPayPwdSet, status, lastTradeTime, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdatePointsWallet({ id, userId, balance, frozenAmount, totalRecharge, totalConsume, pointsBalance, totalPoints, usedPoints, payPassword, isPayPwdSet, status, lastTradeTime, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="用户ID" path="userId">
          <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
        </NFormItem>
        <NFormItem label="余额" path="balance">
          <NInput v-model:value="model.balance" placeholder="请输入余额" />
        </NFormItem>
        <NFormItem label="冻结金额" path="frozenAmount">
          <NInput v-model:value="model.frozenAmount" placeholder="请输入冻结金额" />
        </NFormItem>
        <NFormItem label="累计充值" path="totalRecharge">
          <NInput v-model:value="model.totalRecharge" placeholder="请输入累计充值" />
        </NFormItem>
        <NFormItem label="累计消费" path="totalConsume">
          <NInput v-model:value="model.totalConsume" placeholder="请输入累计消费" />
        </NFormItem>
        <NFormItem label="积分余额" path="pointsBalance">
          <NInput v-model:value="model.pointsBalance" placeholder="请输入积分余额" />
        </NFormItem>
        <NFormItem label="累计获得积分" path="totalPoints">
          <NInput v-model:value="model.totalPoints" placeholder="请输入累计获得积分" />
        </NFormItem>
        <NFormItem label="累计使用积分" path="usedPoints">
          <NInput v-model:value="model.usedPoints" placeholder="请输入累计使用积分" />
        </NFormItem>
        <NFormItem label="支付密码" path="payPassword">
          <NInput v-model:value="model.payPassword" placeholder="请输入支付密码" />
        </NFormItem>
        <NFormItem label="是否设置支付密码（0否 1是）" path="isPayPwdSet">
          <NInput v-model:value="model.isPayPwdSet" placeholder="请输入是否设置支付密码（0否 1是）" />
        </NFormItem>
        <NFormItem label="钱包状态（0正常 1冻结 2注销）" path="status">
          <NInput v-model:value="model.status" placeholder="请输入钱包状态（0正常 1冻结 2注销）" />
        </NFormItem>
        <NFormItem label="最后交易时间" path="lastTradeTime">
          <NDatePicker
            v-model:formatted-value="model.lastTradeTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="备注" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入备注"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
