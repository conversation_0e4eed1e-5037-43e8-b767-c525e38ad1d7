<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsWalletSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Points.PointsWalletSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="用户ID" path="userId" class="pr-24px">
              <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="余额" path="balance" class="pr-24px">
              <NInput v-model:value="model.balance" placeholder="请输入余额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="冻结金额" path="frozenAmount" class="pr-24px">
              <NInput v-model:value="model.frozenAmount" placeholder="请输入冻结金额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="累计充值" path="totalRecharge" class="pr-24px">
              <NInput v-model:value="model.totalRecharge" placeholder="请输入累计充值" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="累计消费" path="totalConsume" class="pr-24px">
              <NInput v-model:value="model.totalConsume" placeholder="请输入累计消费" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="积分余额" path="pointsBalance" class="pr-24px">
              <NInput v-model:value="model.pointsBalance" placeholder="请输入积分余额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="累计获得积分" path="totalPoints" class="pr-24px">
              <NInput v-model:value="model.totalPoints" placeholder="请输入累计获得积分" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="累计使用积分" path="usedPoints" class="pr-24px">
              <NInput v-model:value="model.usedPoints" placeholder="请输入累计使用积分" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="支付密码" path="payPassword" class="pr-24px">
              <NInput v-model:value="model.payPassword" placeholder="请输入支付密码" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="是否设置支付密码（0否 1是）" path="isPayPwdSet" class="pr-24px">
              <NInput v-model:value="model.isPayPwdSet" placeholder="请输入是否设置支付密码（0否 1是）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="钱包状态（0正常 1冻结 2注销）" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择钱包状态（0正常 1冻结 2注销）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="最后交易时间" path="lastTradeTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.lastTradeTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
