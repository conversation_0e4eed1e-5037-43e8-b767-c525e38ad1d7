<script setup lang="tsx">
import { NDivider } from 'naive-ui';
import { fetchBatchDeletePointsOrder, fetchGetPointsOrderList } from '@/service/api/points/points-order';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import PointsOrderOperateDrawer from './modules/points-order-operate-drawer.vue';
import PointsOrderSearch from './modules/points-order-search.vue';

defineOptions({
  name: 'PointsOrderList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetPointsOrderList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    orderNo: null,
    userId: null,
    packageId: null,
    packageName: null,
    pointsAmount: null,
    bonusPoints: null,
    totalPoints: null,
    originalPrice: null,
    payAmount: null,
    discountAmount: null,
    paymentMethod: null,
    paymentNo: null,
    orderStatus: null,
    payTime: null,
    cancelTime: null,
    refundTime: null,
    expireTime: null,
    clientIp: null,
    userAgent: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'orderNo',
      title: '订单号',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'userId',
      title: '用户ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'packageId',
      title: '套餐ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'packageName',
      title: '套餐名称',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'pointsAmount',
      title: '购买积分数量',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'bonusPoints',
      title: '赠送积分数量',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'totalPoints',
      title: '总积分数量',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'originalPrice',
      title: '原价',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'payAmount',
      title: '实付金额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'discountAmount',
      title: '优惠金额',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'paymentMethod',
      title: '支付方式（alipay支付宝 wechat微信 balance余额）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'paymentNo',
      title: '支付流水号',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'orderStatus',
      title: '订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'payTime',
      title: '支付时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'cancelTime',
      title: '取消时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'refundTime',
      title: '退款时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'expireTime',
      title: '订单过期时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'clientIp',
      title: '客户端IP',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'userAgent',
      title: '用户代理',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'remark',
      title: '备注',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('points:pointsOrder:edit') || !hasAuth('points:pointsOrder:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('points:pointsOrder:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('points:pointsOrder:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeletePointsOrder(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeletePointsOrder([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/points/pointsOrder/export', searchParams, `积分订单_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <PointsOrderSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="积分订单列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('points:pointsOrder:add')"
          :show-delete="hasAuth('points:pointsOrder:remove')"
          :show-export="hasAuth('points:pointsOrder:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <PointsOrderOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
