<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsOrderSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Points.PointsOrderSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="订单号" path="orderNo" class="pr-24px">
              <NInput v-model:value="model.orderNo" placeholder="请输入订单号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="用户ID" path="userId" class="pr-24px">
              <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="套餐ID" path="packageId" class="pr-24px">
              <NInput v-model:value="model.packageId" placeholder="请输入套餐ID" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="套餐名称" path="packageName" class="pr-24px">
              <NInput v-model:value="model.packageName" placeholder="请输入套餐名称" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="购买积分数量" path="pointsAmount" class="pr-24px">
              <NInput v-model:value="model.pointsAmount" placeholder="请输入购买积分数量" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="赠送积分数量" path="bonusPoints" class="pr-24px">
              <NInput v-model:value="model.bonusPoints" placeholder="请输入赠送积分数量" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="总积分数量" path="totalPoints" class="pr-24px">
              <NInput v-model:value="model.totalPoints" placeholder="请输入总积分数量" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="原价" path="originalPrice" class="pr-24px">
              <NInput v-model:value="model.originalPrice" placeholder="请输入原价" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="实付金额" path="payAmount" class="pr-24px">
              <NInput v-model:value="model.payAmount" placeholder="请输入实付金额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="优惠金额" path="discountAmount" class="pr-24px">
              <NInput v-model:value="model.discountAmount" placeholder="请输入优惠金额" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="支付方式（alipay支付宝 wechat微信 balance余额）" path="paymentMethod" class="pr-24px">
              <NInput v-model:value="model.paymentMethod" placeholder="请输入支付方式（alipay支付宝 wechat微信 balance余额）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="支付流水号" path="paymentNo" class="pr-24px">
              <NInput v-model:value="model.paymentNo" placeholder="请输入支付流水号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）" path="orderStatus" class="pr-24px">
              <NSelect
                v-model:value="model.orderStatus"
                placeholder="请选择订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="支付时间" path="payTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.payTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="取消时间" path="cancelTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.cancelTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="退款时间" path="refundTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.refundTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="订单过期时间" path="expireTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.expireTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="客户端IP" path="clientIp" class="pr-24px">
              <NInput v-model:value="model.clientIp" placeholder="请输入客户端IP" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="用户代理" path="userAgent" class="pr-24px">
              <NInput v-model:value="model.userAgent" placeholder="请输入用户代理" />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
