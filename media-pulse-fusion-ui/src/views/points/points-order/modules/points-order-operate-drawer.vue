<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreatePointsOrder, fetchUpdatePointsOrder } from '@/service/api/points/points-order';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsOrderOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Points.PointsOrder | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增积分订单',
    edit: '编辑积分订单'
  };
  return titles[props.operateType];
});

type Model = Api.Points.PointsOrderOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      orderNo: '',
      userId: undefined,
      packageId: undefined,
      packageName: '',
      pointsAmount: undefined,
      bonusPoints: undefined,
      totalPoints: undefined,
      originalPrice: undefined,
      payAmount: undefined,
      discountAmount: undefined,
      paymentMethod: '',
      paymentNo: '',
      orderStatus: '',
      payTime: undefined,
      cancelTime: undefined,
      refundTime: undefined,
      expireTime: undefined,
      clientIp: '',
      userAgent: '',
      remark: ''
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'tenantId'
  | 'bonusPoints'
  | 'discountAmount'
  | 'paymentMethod'
  | 'paymentNo'
  | 'orderStatus'
  | 'payTime'
  | 'cancelTime'
  | 'refundTime'
  | 'expireTime'
  | 'clientIp'
  | 'userAgent'
  | 'delFlag'
  | 'createDept'
  | 'createBy'
  | 'createTime'
  | 'updateBy'
  | 'updateTime'
  | 'remark'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  tenantId: createRequiredRule('租户编号不能为空'),
  bonusPoints: createRequiredRule('赠送积分数量不能为空'),
  discountAmount: createRequiredRule('优惠金额不能为空'),
  paymentMethod: createRequiredRule('支付方式（alipay支付宝 wechat微信 balance余额）不能为空'),
  paymentNo: createRequiredRule('支付流水号不能为空'),
  orderStatus: createRequiredRule('订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）不能为空'),
  payTime: createRequiredRule('支付时间不能为空'),
  cancelTime: createRequiredRule('取消时间不能为空'),
  refundTime: createRequiredRule('退款时间不能为空'),
  expireTime: createRequiredRule('订单过期时间不能为空'),
  clientIp: createRequiredRule('客户端IP不能为空'),
  userAgent: createRequiredRule('用户代理不能为空'),
  delFlag: createRequiredRule('删除标志（0代表存在 1代表删除）不能为空'),
  createDept: createRequiredRule('创建部门不能为空'),
  createBy: createRequiredRule('创建者不能为空'),
  createTime: createRequiredRule('创建时间不能为空'),
  updateBy: createRequiredRule('更新者不能为空'),
  updateTime: createRequiredRule('更新时间不能为空'),
  remark: createRequiredRule('备注不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, orderNo, userId, packageId, packageName, pointsAmount, bonusPoints, totalPoints, originalPrice, payAmount, discountAmount, paymentMethod, paymentNo, orderStatus, payTime, cancelTime, refundTime, expireTime, clientIp, userAgent, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreatePointsOrder({ orderNo, userId, packageId, packageName, pointsAmount, bonusPoints, totalPoints, originalPrice, payAmount, discountAmount, paymentMethod, paymentNo, orderStatus, payTime, cancelTime, refundTime, expireTime, clientIp, userAgent, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdatePointsOrder({ id, orderNo, userId, packageId, packageName, pointsAmount, bonusPoints, totalPoints, originalPrice, payAmount, discountAmount, paymentMethod, paymentNo, orderStatus, payTime, cancelTime, refundTime, expireTime, clientIp, userAgent, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="订单号" path="orderNo">
          <NInput v-model:value="model.orderNo" placeholder="请输入订单号" />
        </NFormItem>
        <NFormItem label="用户ID" path="userId">
          <NInput v-model:value="model.userId" placeholder="请输入用户ID" />
        </NFormItem>
        <NFormItem label="套餐ID" path="packageId">
          <NInput v-model:value="model.packageId" placeholder="请输入套餐ID" />
        </NFormItem>
        <NFormItem label="套餐名称" path="packageName">
          <NInput v-model:value="model.packageName" placeholder="请输入套餐名称" />
        </NFormItem>
        <NFormItem label="购买积分数量" path="pointsAmount">
          <NInput v-model:value="model.pointsAmount" placeholder="请输入购买积分数量" />
        </NFormItem>
        <NFormItem label="赠送积分数量" path="bonusPoints">
          <NInput v-model:value="model.bonusPoints" placeholder="请输入赠送积分数量" />
        </NFormItem>
        <NFormItem label="总积分数量" path="totalPoints">
          <NInput v-model:value="model.totalPoints" placeholder="请输入总积分数量" />
        </NFormItem>
        <NFormItem label="原价" path="originalPrice">
          <NInput v-model:value="model.originalPrice" placeholder="请输入原价" />
        </NFormItem>
        <NFormItem label="实付金额" path="payAmount">
          <NInput v-model:value="model.payAmount" placeholder="请输入实付金额" />
        </NFormItem>
        <NFormItem label="优惠金额" path="discountAmount">
          <NInput v-model:value="model.discountAmount" placeholder="请输入优惠金额" />
        </NFormItem>
        <NFormItem label="支付方式（alipay支付宝 wechat微信 balance余额）" path="paymentMethod">
          <NInput v-model:value="model.paymentMethod" placeholder="请输入支付方式（alipay支付宝 wechat微信 balance余额）" />
        </NFormItem>
        <NFormItem label="支付流水号" path="paymentNo">
          <NInput v-model:value="model.paymentNo" placeholder="请输入支付流水号" />
        </NFormItem>
        <NFormItem label="订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）" path="orderStatus">
          <NInput v-model:value="model.orderStatus" placeholder="请输入订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败）" />
        </NFormItem>
        <NFormItem label="支付时间" path="payTime">
          <NDatePicker
            v-model:formatted-value="model.payTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="取消时间" path="cancelTime">
          <NDatePicker
            v-model:formatted-value="model.cancelTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="退款时间" path="refundTime">
          <NDatePicker
            v-model:formatted-value="model.refundTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="订单过期时间" path="expireTime">
          <NDatePicker
            v-model:formatted-value="model.expireTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="客户端IP" path="clientIp">
          <NInput v-model:value="model.clientIp" placeholder="请输入客户端IP" />
        </NFormItem>
        <NFormItem label="用户代理" path="userAgent">
          <NInput
            v-model:value="model.userAgent"
            :rows="3"
            type="textarea"
            placeholder="请输入用户代理"
          />
        </NFormItem>
        <NFormItem label="备注" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入备注"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
