<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NCard, NButton, NTag, NSpace, NGrid, NGridItem, NSpin, NEmpty, NModal, NForm, NFormItem, NInput, NInputNumber, NSelect, NDatePicker, NSwitch, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList, fetchCreatePointsPackage, fetchUpdatePointsPackage, fetchBatchDeletePointsPackage } from '@/service/api/points/points-package';
import { fetchIsSuperAdmin } from '@/service/api/points/point-common';
import { useAuth } from '@/hooks/business/auth';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsPackageShow'
});

const message = useMessage();
const { hasAuth } = useAuth();
const authStore = useAuthStore();

// 数据状态
const loading = ref(false);
const packages = ref<Api.Points.PointsPackage[]>([]);

// 计算是否为超级管理员
const isSuperAdmin = computed(() => {
  // 检查是否有超级管理员权限标识
  return authStore.userInfo.permissions.includes('*:*:*') ||
         authStore.userInfo.roles.includes('superadmin') ||
         authStore.userInfo.roles.includes('admin') ||
         authStore.userInfo.user?.userId === 1;
});

// 弹窗状态
const showModal = ref(false);
const modalType = ref<'add' | 'edit'>('add');
const editingPackage = ref<Api.Points.PointsPackage | null>(null);

// 表单数据
const formData = ref<Api.Points.PointsPackageOperateParams>({
  id: null,
  packageName: '',
  packageDesc: '',
  pointsAmount: null,
  originalPrice: null,
  salePrice: null,
  discountRate: null,
  bonusPoints: null,
  packageType: '1',
  isHot: '0',
  sortOrder: null,
  startTime: null,
  endTime: null,
  status: '0',
  remark: ''
});

// 套餐类型选项
const packageTypeOptions = [
  { label: '普通套餐', value: '1' },
  { label: '限时优惠', value: '2' },
  { label: '新用户专享', value: '3' }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

// 计算属性
const sortedPackages = computed(() => {
  return packages.value
    .filter(pkg => pkg.status === '0') // 只显示正常状态的套餐
    .sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999));
});

// 获取套餐类型标签
const getPackageTypeTag = (type: string) => {
  const typeMap = {
    '1': { label: '普通套餐', type: 'default' as const },
    '2': { label: '限时优惠', type: 'warning' as const },
    '3': { label: '新用户专享', type: 'success' as const }
  };
  return typeMap[type] || { label: '未知', type: 'default' as const };
};

// 格式化价格
const formatPrice = (price: number) => {
  return (price / 100).toFixed(2);
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleDateString();
};

// 检查是否为超级管理员
const checkSuperAdmin = async () => {
  try {
    const { data, error } = await fetchIsSuperAdmin();
    if (!error) {
      isSuperAdmin.value = data;
    }
  } catch (err) {
    console.error('检查超级管理员权限失败:', err);
  }
};

// 获取套餐列表
const getPackageList = async () => {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100, // 获取所有套餐
      status: null // 获取所有状态的套餐，在前端过滤
    });
    if (!error && data) {
      packages.value = data.rows || [];
    }
  } catch (err) {
    console.error('获取套餐列表失败:', err);
    message.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
};

// 打开新增弹窗
const openAddModal = () => {
  modalType.value = 'add';
  editingPackage.value = null;
  resetForm();
  showModal.value = true;
};

// 打开编辑弹窗
const openEditModal = (pkg: Api.Points.PointsPackage) => {
  modalType.value = 'edit';
  editingPackage.value = pkg;
  formData.value = {
    id: pkg.id,
    packageName: pkg.packageName,
    packageDesc: pkg.packageDesc,
    pointsAmount: pkg.pointsAmount,
    originalPrice: pkg.originalPrice,
    salePrice: pkg.salePrice,
    discountRate: pkg.discountRate,
    bonusPoints: pkg.bonusPoints,
    packageType: pkg.packageType,
    isHot: pkg.isHot,
    sortOrder: pkg.sortOrder,
    startTime: pkg.startTime,
    endTime: pkg.endTime,
    status: pkg.status,
    remark: pkg.remark
  };
  showModal.value = true;
};

// 重置表单
const resetForm = () => {
  formData.value = {
    id: null,
    packageName: '',
    packageDesc: '',
    pointsAmount: null,
    originalPrice: null,
    salePrice: null,
    discountRate: null,
    bonusPoints: null,
    packageType: '1',
    isHot: '0',
    sortOrder: null,
    startTime: null,
    endTime: null,
    status: '0',
    remark: ''
  };
};

// 保存套餐
const savePackage = async () => {
  try {
    const apiCall = modalType.value === 'add' ? fetchCreatePointsPackage : fetchUpdatePointsPackage;
    const { error } = await apiCall(formData.value);

    if (!error) {
      message.success(modalType.value === 'add' ? '新增成功' : '更新成功');
      showModal.value = false;
      await getPackageList();
    } else {
      message.error(modalType.value === 'add' ? '新增失败' : '更新失败');
    }
  } catch (err) {
    console.error('保存套餐失败:', err);
    message.error('操作失败');
  }
};

// 删除套餐
const deletePackage = async (pkg: Api.Points.PointsPackage) => {
  try {
    const { error } = await fetchBatchDeletePointsPackage([pkg.id]);
    if (!error) {
      message.success('删除成功');
      await getPackageList();
    } else {
      message.error('删除失败');
    }
  } catch (err) {
    console.error('删除套餐失败:', err);
    message.error('删除失败');
  }
};

// 初始化
onMounted(async () => {
  await checkSuperAdmin();
  await getPackageList();
});
</script>

<template>
  <div class="points-package-container">
    <!-- 头部操作区 -->
    <div class="header-section" v-if="isSuperAdmin">
      <NCard :bordered="false" size="small">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold">积分套餐管理</h2>
          <NButton type="primary" @click="openAddModal">
            <template #icon>
              <i class="i-material-symbols:add" />
            </template>
            新增套餐
          </NButton>
        </div>
      </NCard>
    </div>

    <!-- 套餐展示区 -->
    <div class="packages-section">
      <NSpin :show="loading">
        <div v-if="sortedPackages.length === 0" class="empty-state">
          <NEmpty description="暂无套餐数据" />
        </div>
        <NGrid v-else :cols="1" :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGridItem v-for="pkg in sortedPackages" :key="pkg.id" span="1 s:1 m:1 l:1 xl:1 2xl:1">
            <NCard class="package-card" :data-type="pkg.packageType" :bordered="true" hoverable>
              <!-- 套餐头部 -->
              <div class="package-header">
                <div class="package-title-section">
                  <h3 class="package-title">{{ pkg.packageName }}</h3>
                  <div class="package-tags">
                    <NTag :type="getPackageTypeTag(pkg.packageType).type" size="small">
                      {{ getPackageTypeTag(pkg.packageType).label }}
                    </NTag>
                    <NTag v-if="pkg.isHot === '1'" type="error" size="small">
                      🔥 热门
                    </NTag>
                  </div>
                </div>

                <!-- 超级管理员操作按钮 -->
                <div v-if="isSuperAdmin" class="package-actions">
                  <NButton size="small" type="primary" text @click="openEditModal(pkg)">
                    <template #icon>
                      <i class="i-material-symbols:edit" />
                    </template>
                    编辑
                  </NButton>
                  <NButton size="small" type="error" text @click="deletePackage(pkg)">
                    <template #icon>
                      <i class="i-material-symbols:delete" />
                    </template>
                    删除
                  </NButton>
                </div>
              </div>

              <!-- 套餐内容 -->
              <div class="package-content">
                <div class="package-description">
                  <p>{{ pkg.packageDesc || '暂无描述' }}</p>
                </div>

                <!-- 积分信息 -->
                <div class="points-info">
                  <div class="main-points">
                    <span class="points-amount">{{ pkg.pointsAmount }}</span>
                    <span class="points-label">积分</span>
                  </div>
                  <div v-if="pkg.bonusPoints && pkg.bonusPoints > 0" class="bonus-points">
                    <span class="bonus-text">+ {{ pkg.bonusPoints }} 赠送积分</span>
                  </div>
                </div>

                <!-- 价格信息 -->
                <div class="price-info">
                  <div class="current-price">
                    <span class="currency">¥</span>
                    <span class="price">{{ formatPrice(pkg.salePrice) }}</span>
                  </div>
                  <div v-if="pkg.originalPrice > pkg.salePrice" class="original-price">
                    <span class="original">原价 ¥{{ formatPrice(pkg.originalPrice) }}</span>
                    <span class="discount">{{ pkg.discountRate }}折</span>
                  </div>
                </div>

                <!-- 有效期信息 -->
                <div v-if="pkg.startTime || pkg.endTime" class="validity-info">
                  <div class="validity-item">
                    <span class="label">有效期：</span>
                    <span class="value">
                      {{ formatDate(pkg.startTime) }} - {{ formatDate(pkg.endTime) }}
                    </span>
                  </div>
                </div>

                <!-- 购买按钮 -->
                <div class="purchase-section">
                  <NButton type="primary" size="large" block>
                    立即购买
                  </NButton>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </NSpin>
    </div>

    <!-- 新增/编辑弹窗 -->
    <NModal v-model:show="showModal" preset="dialog" :title="modalType === 'add' ? '新增套餐' : '编辑套餐'" style="width: 600px;">
      <NForm :model="formData" label-placement="left" label-width="100px">
        <NFormItem label="套餐名称" required>
          <NInput v-model:value="formData.packageName" placeholder="请输入套餐名称" />
        </NFormItem>

        <NFormItem label="套餐描述">
          <NInput v-model:value="formData.packageDesc" type="textarea" placeholder="请输入套餐描述" />
        </NFormItem>

        <NFormItem label="积分数量" required>
          <NInputNumber v-model:value="formData.pointsAmount" placeholder="请输入积分数量" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="原价(分)" required>
          <NInputNumber v-model:value="formData.originalPrice" placeholder="请输入原价(分)" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="售价(分)" required>
          <NInputNumber v-model:value="formData.salePrice" placeholder="请输入售价(分)" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="折扣率(%)">
          <NInputNumber v-model:value="formData.discountRate" placeholder="请输入折扣率" :min="0" :max="100" style="width: 100%" />
        </NFormItem>

        <NFormItem label="赠送积分">
          <NInputNumber v-model:value="formData.bonusPoints" placeholder="请输入赠送积分" :min="0" style="width: 100%" />
        </NFormItem>

        <NFormItem label="套餐类型" required>
          <NSelect v-model:value="formData.packageType" :options="packageTypeOptions" placeholder="请选择套餐类型" />
        </NFormItem>

        <NFormItem label="是否热门">
          <NSwitch v-model:value="formData.isHot" checked-value="1" unchecked-value="0" />
        </NFormItem>

        <NFormItem label="显示顺序">
          <NInputNumber v-model:value="formData.sortOrder" placeholder="请输入显示顺序" :min="0" style="width: 100%" />
        </NFormItem>

        <NFormItem label="生效时间">
          <NDatePicker v-model:value="formData.startTime" type="datetime" placeholder="请选择生效时间" style="width: 100%" />
        </NFormItem>

        <NFormItem label="失效时间">
          <NDatePicker v-model:value="formData.endTime" type="datetime" placeholder="请选择失效时间" style="width: 100%" />
        </NFormItem>

        <NFormItem label="状态" required>
          <NSelect v-model:value="formData.status" :options="statusOptions" placeholder="请选择状态" />
        </NFormItem>

        <NFormItem label="备注">
          <NInput v-model:value="formData.remark" type="textarea" placeholder="请输入备注" />
        </NFormItem>
      </NForm>

      <template #action>
        <NSpace>
          <NButton @click="showModal = false">取消</NButton>
          <NButton type="primary" @click="savePackage">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.points-package-container {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  margin-bottom: 16px;
}

.packages-section {
  min-height: 400px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.package-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.package-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.package-card > * {
  position: relative;
  z-index: 2;
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.package-title-section {
  flex: 1;
}

.package-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.package-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.package-actions {
  display: flex;
  gap: 8px;
}

.package-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.package-description p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.5;
}

.points-info {
  text-align: center;
  padding: 16px 0;
}

.main-points {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.points-amount {
  font-size: 48px;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.points-label {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.bonus-points {
  margin-top: 8px;
}

.bonus-text {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.price-info {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.current-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
  margin-bottom: 8px;
}

.currency {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.price {
  font-size: 36px;
  font-weight: 700;
  color: white;
}

.original-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.original {
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.discount {
  background: #ff4757;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.validity-info {
  padding: 12px 0;
}

.validity-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.validity-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.validity-item .value {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.purchase-section {
  padding-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-package-container {
    padding: 8px;
  }

  .package-header {
    flex-direction: column;
    gap: 12px;
  }

  .package-actions {
    align-self: flex-end;
  }

  .package-title {
    font-size: 20px;
  }

  .points-amount {
    font-size: 36px;
  }

  .price {
    font-size: 28px;
  }
}

/* 不同套餐类型的渐变色 */
.package-card[data-type="1"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.package-card[data-type="2"] {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.package-card[data-type="3"] {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
</style>
