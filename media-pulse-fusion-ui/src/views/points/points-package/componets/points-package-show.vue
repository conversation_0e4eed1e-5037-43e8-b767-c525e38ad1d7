<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NCard, NButton, NTag, NSpace, NGrid, NGridItem, NSpin, NEmpty, NModal, NForm, NFormItem, NInput, NInputNumber, NSelect, NDatePicker, NSwitch, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList, fetchCreatePointsPackage, fetchUpdatePointsPackage, fetchBatchDeletePointsPackage } from '@/service/api/points/points-package';
import { fetchIsSuperAdmin } from '@/service/api/points/point-common';
import { useAuth } from '@/hooks/business/auth';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsPackageShow'
});

const message = useMessage();
const { hasAuth } = useAuth();
const authStore = useAuthStore();

// 数据状态
const loading = ref(false);
const packages = ref<Api.Points.PointsPackage[]>([]);

// 计算是否为超级管理员
const isSuperAdmin = computed(() => {
  // 检查是否有超级管理员权限标识
  return authStore.userInfo.permissions.includes('*:*:*') ||
         authStore.userInfo.roles.includes('superadmin') ||
         authStore.userInfo.roles.includes('admin') ||
         authStore.userInfo.user?.userId === 1;
});

// 弹窗状态
const showModal = ref(false);
const modalType = ref<'add' | 'edit'>('add');
const editingPackage = ref<Api.Points.PointsPackage | null>(null);

// 表单数据
const formData = ref<Api.Points.PointsPackageOperateParams>({
  id: null,
  packageName: '',
  packageDesc: '',
  pointsAmount: null,
  originalPrice: null,
  salePrice: null,
  discountRate: null,
  bonusPoints: null,
  packageType: '1',
  isHot: '0',
  sortOrder: null,
  startTime: null,
  endTime: null,
  status: '0',
  remark: ''
});

// 套餐类型选项
const packageTypeOptions = [
  { label: '普通套餐', value: '1' },
  { label: '限时优惠', value: '2' },
  { label: '新用户专享', value: '3' }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

// 计算属性
const sortedPackages = computed(() => {
  return packages.value
    .filter(pkg => pkg.status === '0') // 只显示正常状态的套餐
    .sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999));
});

// 获取套餐类型标签
const getPackageTypeTag = (type: string) => {
  const typeMap = {
    '1': { label: '普通套餐', type: 'default' as const },
    '2': { label: '限时优惠', type: 'warning' as const },
    '3': { label: '新用户专享', type: 'success' as const }
  };
  return typeMap[type] || { label: '未知', type: 'default' as const };
};

// 格式化价格
const formatPrice = (price: number) => {
  return (price / 100).toFixed(2);
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleDateString();
};

// 检查是否为超级管理员
const checkSuperAdmin = async () => {
  try {
    // 方法1：使用API检查
    const { data, error } = await fetchIsSuperAdmin();
    if (!error) {
      isSuperAdmin.value = data;
      return;
    }
  } catch (err) {
    console.error('API检查超级管理员权限失败:', err);
  }

  // 方法2：使用权限系统检查（备用方案）
  try {
    const { hasAuth } = useAuth();
    isSuperAdmin.value = hasAuth('*:*:*');
  } catch (err) {
    console.error('权限检查失败:', err);
    isSuperAdmin.value = false;
  }
};

// 获取套餐列表
const getPackageList = async () => {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100, // 获取所有套餐
      status: null // 获取所有状态的套餐，在前端过滤
    });
    if (!error && data) {
      packages.value = data.rows || [];
    }
  } catch (err) {
    console.error('获取套餐列表失败:', err);
    message.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
};

// 打开新增弹窗
const openAddModal = () => {
  modalType.value = 'add';
  editingPackage.value = null;
  resetForm();
  showModal.value = true;
};

// 打开编辑弹窗
const openEditModal = (pkg: Api.Points.PointsPackage) => {
  modalType.value = 'edit';
  editingPackage.value = pkg;
  formData.value = {
    id: pkg.id,
    packageName: pkg.packageName,
    packageDesc: pkg.packageDesc,
    pointsAmount: pkg.pointsAmount,
    originalPrice: pkg.originalPrice,
    salePrice: pkg.salePrice,
    discountRate: pkg.discountRate,
    bonusPoints: pkg.bonusPoints,
    packageType: pkg.packageType,
    isHot: pkg.isHot,
    sortOrder: pkg.sortOrder,
    startTime: pkg.startTime,
    endTime: pkg.endTime,
    status: pkg.status,
    remark: pkg.remark
  };
  showModal.value = true;
};

// 重置表单
const resetForm = () => {
  formData.value = {
    id: null,
    packageName: '',
    packageDesc: '',
    pointsAmount: null,
    originalPrice: null,
    salePrice: null,
    discountRate: null,
    bonusPoints: null,
    packageType: '1',
    isHot: '0',
    sortOrder: null,
    startTime: null,
    endTime: null,
    status: '0',
    remark: ''
  };
};

// 保存套餐
const savePackage = async () => {
  try {
    const apiCall = modalType.value === 'add' ? fetchCreatePointsPackage : fetchUpdatePointsPackage;
    const { error } = await apiCall(formData.value);

    if (!error) {
      message.success(modalType.value === 'add' ? '新增成功' : '更新成功');
      showModal.value = false;
      await getPackageList();
    } else {
      message.error(modalType.value === 'add' ? '新增失败' : '更新失败');
    }
  } catch (err) {
    console.error('保存套餐失败:', err);
    message.error('操作失败');
  }
};

// 删除套餐
const deletePackage = async (pkg: Api.Points.PointsPackage) => {
  try {
    const { error } = await fetchBatchDeletePointsPackage([pkg.id]);
    if (!error) {
      message.success('删除成功');
      await getPackageList();
    } else {
      message.error('删除失败');
    }
  } catch (err) {
    console.error('删除套餐失败:', err);
    message.error('删除失败');
  }
};

// 选择套餐
const selectPackage = (pkg: Api.Points.PointsPackage) => {
  console.log('选择套餐:', pkg);
  // 这里可以添加选择套餐的逻辑，比如跳转到购买页面
};

// 初始化
onMounted(async () => {
  await checkSuperAdmin();
  await getPackageList();
});
</script>

<template>
  <div class="points-package-container">
    <!-- 头部操作区 -->
    <div class="header-section" v-if="isSuperAdmin">
      <NCard :bordered="false" size="small">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold">积分套餐管理</h2>
          <NButton type="primary" @click="openAddModal">
            <template #icon>
              <i class="i-material-symbols:add" />
            </template>
            新增套餐
          </NButton>
        </div>
      </NCard>
    </div>

    <!-- 套餐展示区 -->
    <div class="packages-section">
      <NSpin :show="loading">
        <div v-if="sortedPackages.length === 0" class="empty-state">
          <NEmpty description="暂无套餐数据" />
        </div>
        <!-- 创新的瀑布流布局 -->
        <div class="packages-showcase">
          <!-- 主推套餐 -->
          <div v-if="sortedPackages.length > 0" class="hero-package" @click="selectPackage(sortedPackages[0])">
            <div class="hero-content">
              <div class="hero-badge" v-if="sortedPackages[0].isHot === '1'">🔥 最受欢迎</div>
              <div v-if="isSuperAdmin" class="hero-admin">
                <button @click.stop="openEditModal(sortedPackages[0])" class="admin-btn">✏️</button>
                <button @click.stop="deletePackage(sortedPackages[0])" class="admin-btn">🗑️</button>
              </div>
              <h2 class="hero-title">{{ sortedPackages[0].packageName }}</h2>
              <p class="hero-desc">{{ sortedPackages[0].packageDesc || '最受欢迎的积分套餐' }}</p>
              <div class="hero-points">
                <span class="hero-points-num">{{ sortedPackages[0].pointsAmount.toLocaleString() }}</span>
                <span class="hero-points-unit">积分</span>
              </div>
              <div class="hero-price">
                <span class="hero-price-current">¥{{ formatPrice(sortedPackages[0].salePrice) }}</span>
                <span v-if="sortedPackages[0].originalPrice > sortedPackages[0].salePrice" class="hero-price-original">
                  ¥{{ formatPrice(sortedPackages[0].originalPrice) }}
                </span>
              </div>
              <button class="hero-buy-btn">立即购买</button>
            </div>
            <div class="hero-visual">
              <div class="floating-coins">
                <div class="coin coin-1">💰</div>
                <div class="coin coin-2">🪙</div>
                <div class="coin coin-3">💎</div>
              </div>
            </div>
          </div>

          <!-- 其他套餐 - 横向滚动 -->
          <div class="packages-scroll-section">
            <h3 class="section-title">更多选择</h3>
            <div class="packages-horizontal-scroll">
              <div v-for="(pkg, index) in sortedPackages.slice(1)" :key="pkg.id"
                   class="package-tile"
                   :class="`tile-${(index % 4) + 1}`"
                   @click="selectPackage(pkg)">
                <div v-if="isSuperAdmin" class="tile-admin">
                  <button @click.stop="openEditModal(pkg)" class="tile-admin-btn">✏️</button>
                  <button @click.stop="deletePackage(pkg)" class="tile-admin-btn">🗑️</button>
                </div>
                <div v-if="pkg.isHot === '1'" class="tile-hot">HOT</div>
                <div class="tile-type">{{ getPackageTypeTag(pkg.packageType).label }}</div>
                <div class="tile-name">{{ pkg.packageName }}</div>
                <div class="tile-points">
                  <span class="tile-points-num">{{ pkg.pointsAmount.toLocaleString() }}</span>
                  <span class="tile-points-text">积分</span>
                </div>
                <div class="tile-price">¥{{ formatPrice(pkg.salePrice) }}</div>
                <div v-if="pkg.originalPrice > pkg.salePrice" class="tile-discount">
                  {{ pkg.discountRate }}折
                </div>
              </div>
            </div>
          </div>

          <!-- 套餐对比区域 -->
          <div class="comparison-section" v-if="sortedPackages.length > 2">
            <h3 class="section-title">套餐对比</h3>
            <div class="comparison-grid">
              <div v-for="pkg in sortedPackages.slice(0, 3)" :key="`compare-${pkg.id}`" class="comparison-item">
                <div class="comparison-header">
                  <div class="comparison-name">{{ pkg.packageName }}</div>
                  <div class="comparison-type">{{ getPackageTypeTag(pkg.packageType).label }}</div>
                </div>
                <div class="comparison-points">{{ pkg.pointsAmount.toLocaleString() }}积分</div>
                <div class="comparison-price">¥{{ formatPrice(pkg.salePrice) }}</div>
                <div class="comparison-features">
                  <div class="feature">✓ 永久有效</div>
                  <div class="feature" v-if="pkg.bonusPoints">✓ 赠送{{ pkg.bonusPoints }}积分</div>
                  <div class="feature">✓ 24小时客服</div>
                </div>
                <button class="comparison-btn">选择此套餐</button>
              </div>
            </div>
          </div>
        </div>
      </NSpin>
    </div>

    <!-- 新增/编辑弹窗 -->
    <NModal v-model:show="showModal" preset="dialog" :title="modalType === 'add' ? '新增套餐' : '编辑套餐'" style="width: 600px;">
      <NForm :model="formData" label-placement="left" label-width="100px">
        <NFormItem label="套餐名称" required>
          <NInput v-model:value="formData.packageName" placeholder="请输入套餐名称" />
        </NFormItem>

        <NFormItem label="套餐描述">
          <NInput v-model:value="formData.packageDesc" type="textarea" placeholder="请输入套餐描述" />
        </NFormItem>

        <NFormItem label="积分数量" required>
          <NInputNumber v-model:value="formData.pointsAmount" placeholder="请输入积分数量" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="原价(分)" required>
          <NInputNumber v-model:value="formData.originalPrice" placeholder="请输入原价(分)" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="售价(分)" required>
          <NInputNumber v-model:value="formData.salePrice" placeholder="请输入售价(分)" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="折扣率(%)">
          <NInputNumber v-model:value="formData.discountRate" placeholder="请输入折扣率" :min="0" :max="100" style="width: 100%" />
        </NFormItem>

        <NFormItem label="赠送积分">
          <NInputNumber v-model:value="formData.bonusPoints" placeholder="请输入赠送积分" :min="0" style="width: 100%" />
        </NFormItem>

        <NFormItem label="套餐类型" required>
          <NSelect v-model:value="formData.packageType" :options="packageTypeOptions" placeholder="请选择套餐类型" />
        </NFormItem>

        <NFormItem label="是否热门">
          <NSwitch v-model:value="formData.isHot" checked-value="1" unchecked-value="0" />
        </NFormItem>

        <NFormItem label="显示顺序">
          <NInputNumber v-model:value="formData.sortOrder" placeholder="请输入显示顺序" :min="0" style="width: 100%" />
        </NFormItem>

        <NFormItem label="生效时间">
          <NDatePicker v-model:value="formData.startTime" type="datetime" placeholder="请选择生效时间" style="width: 100%" />
        </NFormItem>

        <NFormItem label="失效时间">
          <NDatePicker v-model:value="formData.endTime" type="datetime" placeholder="请选择失效时间" style="width: 100%" />
        </NFormItem>

        <NFormItem label="状态" required>
          <NSelect v-model:value="formData.status" :options="statusOptions" placeholder="请选择状态" />
        </NFormItem>

        <NFormItem label="备注">
          <NInput v-model:value="formData.remark" type="textarea" placeholder="请输入备注" />
        </NFormItem>
      </NForm>

      <template #action>
        <NSpace>
          <NButton @click="showModal = false">取消</NButton>
          <NButton type="primary" @click="savePackage">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.points-package-container {
  padding: 16px;
  background: #f8fafc;
  min-height: calc(100vh - 120px);
  overflow-y: auto;
}

.header-section {
  margin-bottom: 20px;
}

.header-section .n-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.packages-section {
  min-height: 200px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 创新展示区域 */
.packages-showcase {
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding: 20px 0;
}

/* 主推套餐 - 大屏展示 */
.hero-package {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 0;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s ease;
  min-height: 300px;
  display: flex;
  align-items: center;
}

.hero-package:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.hero-content {
  flex: 1;
  padding: 40px;
  color: white;
  z-index: 2;
}

.hero-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 20px;
}

.hero-admin {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
}

.admin-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.admin-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 主推套餐内容样式 */
.hero-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.hero-desc {
  font-size: 18px;
  opacity: 0.9;
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.hero-points {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 24px;
}

.hero-points-num {
  font-size: 48px;
  font-weight: 900;
  line-height: 1;
}

.hero-points-unit {
  font-size: 20px;
  opacity: 0.8;
}

.hero-price {
  display: flex;
  align-items: baseline;
  gap: 16px;
  margin-bottom: 32px;
}

.hero-price-current {
  font-size: 32px;
  font-weight: 800;
}

.hero-price-original {
  font-size: 20px;
  opacity: 0.6;
  text-decoration: line-through;
}

.hero-buy-btn {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero-buy-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* 主推套餐视觉效果 */
.hero-visual {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40%;
  overflow: hidden;
  z-index: 1;
}

.floating-coins {
  position: relative;
  width: 100%;
  height: 100%;
}

.coin {
  position: absolute;
  font-size: 48px;
  animation: float 6s ease-in-out infinite;
  opacity: 0.3;
}

.coin-1 {
  top: 20%;
  right: 20%;
  animation-delay: 0s;
}

.coin-2 {
  top: 50%;
  right: 40%;
  animation-delay: 2s;
}

.coin-3 {
  top: 70%;
  right: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 横向滚动区域 */
.packages-scroll-section {
  margin-top: 20px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
  text-align: center;
}

.packages-horizontal-scroll {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding: 20px 0;
  scroll-behavior: smooth;
}

.packages-horizontal-scroll::-webkit-scrollbar {
  height: 8px;
}

.packages-horizontal-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.packages-horizontal-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.packages-horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.bonus-text {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.price-info {
  text-align: center;
  padding: 16px 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.current-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
  margin-bottom: 8px;
}

.currency {
  font-size: 18px;
  color: #64748b;
  font-weight: 600;
}

.price {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
}

.original-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.original {
  text-decoration: line-through;
  color: #94a3b8;
  font-size: 16px;
  font-weight: 500;
}

.discount {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
}

.validity-info {
  padding: 16px 0;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.validity-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.validity-item .label {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.validity-item .value {
  color: #1e293b;
  font-size: 14px;
  font-weight: 600;
}

.purchase-section {
  padding-top: 16px;
  margin-top: auto;
}

.purchase-section .n-button {
  height: 44px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.package-card[data-type="2"] .purchase-section .n-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.package-card[data-type="3"] .purchase-section .n-button {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.purchase-section .n-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-package-container {
    padding: 12px;
  }

  .package-header {
    flex-direction: column;
    gap: 12px;
    padding: 12px 12px 0 12px;
  }

  .package-actions {
    align-self: flex-end;
    opacity: 1;
  }

  .package-title {
    font-size: 18px;
  }

  .points-amount {
    font-size: 28px;
  }

  .price {
    font-size: 24px;
  }

  .package-card {
    height: auto;
    min-height: 360px;
  }

  .package-content {
    padding: 0 12px 12px 12px;
  }
}

/* 加载动画 */
.packages-section .n-spin {
  min-height: 200px;
}

/* 新增样式 - 价格展示区域 */
.price-display {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin: 0 16px 16px 16px;
  padding: 16px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.price-symbol {
  font-size: 18px;
  color: #1e293b;
  font-weight: 600;
}

.price-value {
  font-size: 28px;
  font-weight: 800;
  color: #1e293b;
  line-height: 1;
}

.original-value {
  font-size: 14px;
  color: #94a3b8;
  text-decoration: line-through;
}

.discount-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 购买按钮 */
.purchase-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 14px 24px;
  margin: 0 16px 16px 16px;
  border-radius: 16px;
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.purchase-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

/* 套餐瓦片样式 */
.package-tile {
  flex: 0 0 240px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.package-tile:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.tile-1 { border-color: #667eea; }
.tile-1:hover { border-color: #667eea; box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2); }

.tile-2 { border-color: #f093fb; }
.tile-2:hover { border-color: #f093fb; box-shadow: 0 20px 40px rgba(240, 147, 251, 0.2); }

.tile-3 { border-color: #4facfe; }
.tile-3:hover { border-color: #4facfe; box-shadow: 0 20px 40px rgba(79, 172, 254, 0.2); }

.tile-4 { border-color: #43e97b; }
.tile-4:hover { border-color: #43e97b; box-shadow: 0 20px 40px rgba(67, 233, 123, 0.2); }

.tile-admin {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.package-tile:hover .tile-admin {
  opacity: 1;
}

.tile-admin-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.tile-admin-btn:hover {
  background: #e2e8f0;
  transform: scale(1.1);
}

.tile-hot {
  position: absolute;
  top: 0;
  right: 0;
  background: #ef4444;
  color: white;
  padding: 4px 8px;
  border-radius: 0 16px 0 12px;
  font-size: 10px;
  font-weight: 600;
}

.tile-type {
  color: #64748b;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tile-name {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16px;
  line-height: 1.3;
}

.tile-points {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin-bottom: 12px;
}

.tile-points-num {
  font-size: 24px;
  font-weight: 800;
  color: #1e293b;
}

.tile-points-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 600;
}

.tile-price {
  font-size: 20px;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 8px;
}

.tile-discount {
  display: inline-block;
  background: #fef3c7;
  color: #d97706;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 对比区域样式 */
.comparison-section {
  margin-top: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 24px;
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.comparison-item {
  background: white;
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.comparison-item:hover {
  border-color: #3b82f6;
  transform: translateY(-4px);
  box-shadow: 0 16px 32px rgba(59, 130, 246, 0.15);
}

.comparison-header {
  margin-bottom: 24px;
}

.comparison-name {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}

.comparison-type {
  color: #64748b;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.comparison-points {
  font-size: 32px;
  font-weight: 800;
  color: #3b82f6;
  margin-bottom: 16px;
}

.comparison-price {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
}

.comparison-features {
  text-align: left;
  margin-bottom: 32px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #64748b;
  font-size: 14px;
}

.comparison-btn {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.comparison-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-package {
    flex-direction: column;
    min-height: auto;
  }

  .hero-content {
    padding: 24px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-points-num {
    font-size: 36px;
  }

  .hero-price-current {
    font-size: 24px;
  }

  .hero-visual {
    position: relative;
    width: 100%;
    height: 120px;
  }

  .packages-horizontal-scroll {
    padding: 10px 0;
  }

  .package-tile {
    flex: 0 0 200px;
    padding: 16px;
  }

  .comparison-section {
    padding: 24px;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .comparison-item {
    padding: 24px;
  }
}
</style>
