<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NButton, NCard, NEmpty, NGrid, NGridItem, NSpin, NTag, NTooltip, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList, fetchCreatePointsPackage, fetchUpdatePointsPackage, fetchBatchDeletePointsPackage } from '@/service/api/points/points-package';
import { fetchIsSuperAdmin } from '@/service/api/points/point-common';
import { useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import PointsPackageOperateDrawer from '../modules/points-package-operate-drawer.vue';

defineOptions({
  name: 'PointsPackageShow'
});

const message = useMessage();

// 数据状态
const loading = ref(false);
const packages = ref<Api.Points.PointsPackage[]>([]);
const isSuperAdmin = ref(false);

// 操作相关
const { drawerVisible, operateType, editingData, handleAdd, handleEdit } = useTableOperate(packages, loadPackages);

// 加载套餐数据
async function loadPackages() {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100, // 获取所有套餐
      status: '0' // 只获取正常状态的套餐
    });

    if (!error && data) {
      packages.value = data.rows || [];
    }
  } catch (err) {
    console.error('加载套餐失败:', err);
    message.error('加载套餐失败');
  } finally {
    loading.value = false;
  }
}

// 检查超级管理员权限
async function checkSuperAdmin() {
  try {
    const { data, error } = await fetchIsSuperAdmin();
    if (!error) {
      isSuperAdmin.value = data || false;
    }
  } catch (err) {
    console.error('检查权限失败:', err);
  }
}

// 删除套餐
async function handleDelete(id: CommonType.IdType) {
  try {
    const { error } = await fetchBatchDeletePointsPackage([id]);
    if (!error) {
      message.success('删除成功');
      await loadPackages();
    }
  } catch (err) {
    console.error('删除失败:', err);
    message.error('删除失败');
  }
}

// 编辑套餐
function editPackage(pkg: Api.Points.PointsPackage) {
  handleEdit('id', pkg.id!);
}

// 格式化价格
function formatPrice(price: number | undefined) {
  if (price === undefined || price === null) return '0';
  return (price / 100).toFixed(2);
}

// 获取套餐类型标签
function getPackageTypeTag(type: string) {
  const typeMap = {
    '1': { label: '普通套餐', color: 'default' },
    '2': { label: '限时优惠', color: 'warning' },
    '3': { label: '新用户专享', color: 'success' }
  };
  return typeMap[type] || { label: '未知', color: 'default' };
}

// 计算折扣信息
function getDiscountInfo(originalPrice: number, salePrice: number) {
  if (!originalPrice || !salePrice) return null;
  const discount = Math.round((1 - salePrice / originalPrice) * 100);
  return discount > 0 ? discount : null;
}

// 排序后的套餐列表
const sortedPackages = computed(() => {
  return [...packages.value].sort((a, b) => {
    // 先按热门排序，再按显示顺序
    if (a.isHot !== b.isHot) {
      return b.isHot === '1' ? 1 : -1;
    }
    return (a.sortOrder || 0) - (b.sortOrder || 0);
  });
});

// 组件挂载时加载数据
onMounted(() => {
  loadPackages();
  checkSuperAdmin();
});
</script>

<template>
  <div class="points-package-show">
    <!-- 头部操作区 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200">积分套餐</h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">选择适合您的积分套餐</p>
      </div>

      <!-- 超级管理员操作按钮 -->
      <div v-if="isSuperAdmin" class="flex gap-3">
        <NButton type="primary" @click="handleAdd">
          <template #icon>
            <icon-material-symbols:add />
          </template>
          新增套餐
        </NButton>
        <NButton @click="loadPackages">
          <template #icon>
            <icon-material-symbols:refresh />
          </template>
          刷新
        </NButton>
      </div>
    </div>

    <!-- 套餐展示区 -->
    <NSpin :show="loading">
      <div v-if="sortedPackages.length === 0 && !loading" class="text-center py-20">
        <NEmpty description="暂无套餐数据" />
      </div>

      <NGrid v-else :cols="1" :x-gap="24" :y-gap="24" responsive="screen" item-responsive>
        <NGridItem v-for="pkg in sortedPackages" :key="pkg.id" span="1 s:1 m:1 l:1 xl:1 2xl:1">
          <NCard
            class="package-card"
            :class="{ 'hot-package': pkg.isHot === '1' }"
            hoverable
          >
            <!-- 热门标签 -->
            <div v-if="pkg.isHot === '1'" class="hot-badge">
              <icon-material-symbols:local-fire-department class="text-orange-500" />
              <span>热门</span>
            </div>

            <div class="package-content">
              <!-- 左侧信息 -->
              <div class="package-info">
                <div class="package-header">
                  <h3 class="package-name">{{ pkg.packageName }}</h3>
                  <NTag
                    :type="getPackageTypeTag(pkg.packageType).color as any"
                    size="small"
                  >
                    {{ getPackageTypeTag(pkg.packageType).label }}
                  </NTag>
                </div>

                <p class="package-desc">{{ pkg.packageDesc }}</p>

                <div class="package-details">
                  <div class="detail-item">
                    <icon-material-symbols:stars class="text-yellow-500" />
                    <span>{{ pkg.pointsAmount }}积分</span>
                    <span v-if="pkg.bonusPoints && pkg.bonusPoints > 0" class="bonus">
                      +{{ pkg.bonusPoints }}赠送
                    </span>
                  </div>
                </div>
              </div>

              <!-- 右侧价格和操作 -->
              <div class="package-actions">
                <div class="price-section">
                  <div class="current-price">
                    ¥{{ formatPrice(pkg.salePrice) }}
                  </div>
                  <div v-if="pkg.originalPrice && pkg.originalPrice !== pkg.salePrice" class="original-price">
                    <span class="line-through">¥{{ formatPrice(pkg.originalPrice) }}</span>
                    <span v-if="getDiscountInfo(pkg.originalPrice, pkg.salePrice)" class="discount">
                      {{ getDiscountInfo(pkg.originalPrice, pkg.salePrice) }}折
                    </span>
                  </div>
                </div>

                <!-- 超级管理员操作按钮 -->
                <div v-if="isSuperAdmin" class="admin-actions">
                  <NTooltip trigger="hover">
                    <template #trigger>
                      <ButtonIcon
                        text
                        type="primary"
                        icon="material-symbols:edit"
                        @click="editPackage(pkg)"
                      />
                    </template>
                    编辑套餐
                  </NTooltip>

                  <NTooltip trigger="hover">
                    <template #trigger>
                      <ButtonIcon
                        text
                        type="error"
                        icon="material-symbols:delete"
                        popconfirm-content="确定要删除这个套餐吗？"
                        @positive-click="handleDelete(pkg.id!)"
                      />
                    </template>
                    删除套餐
                  </NTooltip>
                </div>

                <!-- 普通用户购买按钮 -->
                <div v-else class="purchase-section">
                  <NButton type="primary" size="large" class="purchase-btn">
                    立即购买
                  </NButton>
                </div>
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </NSpin>

    <!-- 操作抽屉 -->
    <PointsPackageOperateDrawer
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="loadPackages"
    />
  </div>
</template>

<style scoped>
.points-package-show {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.package-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.hot-package {
  border: 2px solid #ff6b35;
  box-shadow: 0 8px 32px rgba(255, 107, 53, 0.2);
}

.hot-package:hover {
  box-shadow: 0 12px 40px rgba(255, 107, 53, 0.3);
}

.hot-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.package-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  gap: 24px;
}

.package-info {
  flex: 1;
}

.package-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.package-name {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.package-desc {
  color: #718096;
  font-size: 16px;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #4a5568;
}

.bonus {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.package-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
}

.price-section {
  text-align: right;
}

.current-price {
  font-size: 32px;
  font-weight: 800;
  color: #e53e3e;
  line-height: 1;
}

.original-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.line-through {
  color: #a0aec0;
  font-size: 16px;
  text-decoration: line-through;
}

.discount {
  background: linear-gradient(135deg, #e53e3e, #c53030);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.admin-actions {
  display: flex;
  gap: 8px;
}

.purchase-section {
  width: 120px;
}

.purchase-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  transition: all 0.3s ease;
}

.purchase-btn:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-package-show {
    padding: 16px;
  }

  .package-content {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .package-actions {
    align-items: stretch;
  }

  .price-section {
    text-align: center;
  }

  .admin-actions {
    justify-content: center;
  }

  .purchase-section {
    width: 100%;
  }
}

/* 暗色主题适配 */
.dark .package-card {
  background: rgba(45, 55, 72, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .package-name {
  color: #f7fafc;
}

.dark .package-desc {
  color: #cbd5e0;
}

.dark .detail-item {
  color: #e2e8f0;
}
</style>
