<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NCard, NButton, NTag, NSpace, NGrid, NGridItem, NSpin, NEmpty, NModal, NForm, NFormItem, NInput, NInputNumber, NSelect, NDatePicker, NSwitch, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList, fetchCreatePointsPackage, fetchUpdatePointsPackage, fetchBatchDeletePointsPackage } from '@/service/api/points/points-package';
import { fetchIsSuperAdmin } from '@/service/api/points/point-common';
import { useAuth } from '@/hooks/business/auth';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsPackageShow'
});

const message = useMessage();
const { hasAuth } = useAuth();
const authStore = useAuthStore();

// 数据状态
const loading = ref(false);
const packages = ref<Api.Points.PointsPackage[]>([]);

// 计算是否为超级管理员
const isSuperAdmin = computed(() => {
  // 检查是否有超级管理员权限标识
  return authStore.userInfo.permissions.includes('*:*:*') ||
         authStore.userInfo.roles.includes('superadmin') ||
         authStore.userInfo.roles.includes('admin') ||
         authStore.userInfo.user?.userId === 1;
});

// 弹窗状态
const showModal = ref(false);
const modalType = ref<'add' | 'edit'>('add');
const editingPackage = ref<Api.Points.PointsPackage | null>(null);

// 表单数据
const formData = ref<Api.Points.PointsPackageOperateParams>({
  id: null,
  packageName: '',
  packageDesc: '',
  pointsAmount: null,
  originalPrice: null,
  salePrice: null,
  discountRate: null,
  bonusPoints: null,
  packageType: '1',
  isHot: '0',
  sortOrder: null,
  startTime: null,
  endTime: null,
  status: '0',
  remark: ''
});

// 套餐类型选项
const packageTypeOptions = [
  { label: '普通套餐', value: '1' },
  { label: '限时优惠', value: '2' },
  { label: '新用户专享', value: '3' }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

// 计算属性
const sortedPackages = computed(() => {
  return packages.value
    .filter(pkg => pkg.status === '0') // 只显示正常状态的套餐
    .sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999));
});

// 获取套餐类型标签
const getPackageTypeTag = (type: string) => {
  const typeMap = {
    '1': { label: '普通套餐', type: 'default' as const },
    '2': { label: '限时优惠', type: 'warning' as const },
    '3': { label: '新用户专享', type: 'success' as const }
  };
  return typeMap[type] || { label: '未知', type: 'default' as const };
};

// 格式化价格
const formatPrice = (price: number) => {
  return (price / 100).toFixed(2);
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleDateString();
};

// 检查是否为超级管理员
const checkSuperAdmin = async () => {
  try {
    const { data, error } = await fetchIsSuperAdmin();
    if (!error) {
      isSuperAdmin.value = data;
    }
  } catch (err) {
    console.error('检查超级管理员权限失败:', err);
  }
};

// 获取套餐列表
const getPackageList = async () => {
  loading.value = true;
  try {
    const { data, error } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100, // 获取所有套餐
      status: null // 获取所有状态的套餐，在前端过滤
    });
    if (!error && data) {
      packages.value = data.rows || [];
    }
  } catch (err) {
    console.error('获取套餐列表失败:', err);
    message.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
};

// 打开新增弹窗
const openAddModal = () => {
  modalType.value = 'add';
  editingPackage.value = null;
  resetForm();
  showModal.value = true;
};

// 打开编辑弹窗
const openEditModal = (pkg: Api.Points.PointsPackage) => {
  modalType.value = 'edit';
  editingPackage.value = pkg;
  formData.value = {
    id: pkg.id,
    packageName: pkg.packageName,
    packageDesc: pkg.packageDesc,
    pointsAmount: pkg.pointsAmount,
    originalPrice: pkg.originalPrice,
    salePrice: pkg.salePrice,
    discountRate: pkg.discountRate,
    bonusPoints: pkg.bonusPoints,
    packageType: pkg.packageType,
    isHot: pkg.isHot,
    sortOrder: pkg.sortOrder,
    startTime: pkg.startTime,
    endTime: pkg.endTime,
    status: pkg.status,
    remark: pkg.remark
  };
  showModal.value = true;
};

// 重置表单
const resetForm = () => {
  formData.value = {
    id: null,
    packageName: '',
    packageDesc: '',
    pointsAmount: null,
    originalPrice: null,
    salePrice: null,
    discountRate: null,
    bonusPoints: null,
    packageType: '1',
    isHot: '0',
    sortOrder: null,
    startTime: null,
    endTime: null,
    status: '0',
    remark: ''
  };
};

// 保存套餐
const savePackage = async () => {
  try {
    const apiCall = modalType.value === 'add' ? fetchCreatePointsPackage : fetchUpdatePointsPackage;
    const { error } = await apiCall(formData.value);

    if (!error) {
      message.success(modalType.value === 'add' ? '新增成功' : '更新成功');
      showModal.value = false;
      await getPackageList();
    } else {
      message.error(modalType.value === 'add' ? '新增失败' : '更新失败');
    }
  } catch (err) {
    console.error('保存套餐失败:', err);
    message.error('操作失败');
  }
};

// 删除套餐
const deletePackage = async (pkg: Api.Points.PointsPackage) => {
  try {
    const { error } = await fetchBatchDeletePointsPackage([pkg.id]);
    if (!error) {
      message.success('删除成功');
      await getPackageList();
    } else {
      message.error('删除失败');
    }
  } catch (err) {
    console.error('删除套餐失败:', err);
    message.error('删除失败');
  }
};

// 初始化
onMounted(async () => {
  await checkSuperAdmin();
  await getPackageList();
});
</script>

<template>
  <div class="points-package-container">
    <!-- 头部操作区 -->
    <div class="header-section" v-if="isSuperAdmin">
      <NCard :bordered="false" size="small">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold">积分套餐管理</h2>
          <NButton type="primary" @click="openAddModal">
            <template #icon>
              <i class="i-material-symbols:add" />
            </template>
            新增套餐
          </NButton>
        </div>
      </NCard>
    </div>

    <!-- 套餐展示区 -->
    <div class="packages-section">
      <NSpin :show="loading">
        <div v-if="sortedPackages.length === 0" class="empty-state">
          <NEmpty description="暂无套餐数据" />
        </div>
        <NGrid v-else :cols="1" :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGridItem v-for="pkg in sortedPackages" :key="pkg.id" span="1 s:1 m:1 l:1 xl:1 2xl:1">
            <NCard class="package-card" :data-type="pkg.packageType" :bordered="true" hoverable>
              <!-- 套餐头部 -->
              <div class="package-header">
                <div class="package-title-section">
                  <h3 class="package-title">{{ pkg.packageName }}</h3>
                  <div class="package-tags">
                    <NTag :type="getPackageTypeTag(pkg.packageType).type" size="small">
                      {{ getPackageTypeTag(pkg.packageType).label }}
                    </NTag>
                    <NTag v-if="pkg.isHot === '1'" type="error" size="small">
                      🔥 热门
                    </NTag>
                  </div>
                </div>

                <!-- 超级管理员操作按钮 -->
                <div v-if="isSuperAdmin" class="package-actions">
                  <NButton size="small" type="primary" text @click="openEditModal(pkg)">
                    <template #icon>
                      <i class="i-material-symbols:edit" />
                    </template>
                    编辑
                  </NButton>
                  <NButton size="small" type="error" text @click="deletePackage(pkg)">
                    <template #icon>
                      <i class="i-material-symbols:delete" />
                    </template>
                    删除
                  </NButton>
                </div>
              </div>

              <!-- 套餐内容 -->
              <div class="package-content">
                <div class="package-description">
                  <p>{{ pkg.packageDesc || '暂无描述' }}</p>
                </div>

                <!-- 积分信息 -->
                <div class="points-info">
                  <div class="main-points">
                    <span class="points-amount">{{ pkg.pointsAmount }}</span>
                    <span class="points-label">积分</span>
                  </div>
                  <div v-if="pkg.bonusPoints && pkg.bonusPoints > 0" class="bonus-points">
                    <span class="bonus-text">+ {{ pkg.bonusPoints }} 赠送积分</span>
                  </div>
                </div>

                <!-- 价格信息 -->
                <div class="price-info">
                  <div class="current-price">
                    <span class="currency">¥</span>
                    <span class="price">{{ formatPrice(pkg.salePrice) }}</span>
                  </div>
                  <div v-if="pkg.originalPrice > pkg.salePrice" class="original-price">
                    <span class="original">原价 ¥{{ formatPrice(pkg.originalPrice) }}</span>
                    <span class="discount">{{ pkg.discountRate }}折</span>
                  </div>
                </div>

                <!-- 有效期信息 -->
                <div v-if="pkg.startTime || pkg.endTime" class="validity-info">
                  <div class="validity-item">
                    <span class="label">有效期：</span>
                    <span class="value">
                      {{ formatDate(pkg.startTime) }} - {{ formatDate(pkg.endTime) }}
                    </span>
                  </div>
                </div>

                <!-- 购买按钮 -->
                <div class="purchase-section">
                  <NButton type="primary" size="large" block>
                    立即购买
                  </NButton>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </NSpin>
    </div>

    <!-- 新增/编辑弹窗 -->
    <NModal v-model:show="showModal" preset="dialog" :title="modalType === 'add' ? '新增套餐' : '编辑套餐'" style="width: 600px;">
      <NForm :model="formData" label-placement="left" label-width="100px">
        <NFormItem label="套餐名称" required>
          <NInput v-model:value="formData.packageName" placeholder="请输入套餐名称" />
        </NFormItem>

        <NFormItem label="套餐描述">
          <NInput v-model:value="formData.packageDesc" type="textarea" placeholder="请输入套餐描述" />
        </NFormItem>

        <NFormItem label="积分数量" required>
          <NInputNumber v-model:value="formData.pointsAmount" placeholder="请输入积分数量" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="原价(分)" required>
          <NInputNumber v-model:value="formData.originalPrice" placeholder="请输入原价(分)" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="售价(分)" required>
          <NInputNumber v-model:value="formData.salePrice" placeholder="请输入售价(分)" :min="1" style="width: 100%" />
        </NFormItem>

        <NFormItem label="折扣率(%)">
          <NInputNumber v-model:value="formData.discountRate" placeholder="请输入折扣率" :min="0" :max="100" style="width: 100%" />
        </NFormItem>

        <NFormItem label="赠送积分">
          <NInputNumber v-model:value="formData.bonusPoints" placeholder="请输入赠送积分" :min="0" style="width: 100%" />
        </NFormItem>

        <NFormItem label="套餐类型" required>
          <NSelect v-model:value="formData.packageType" :options="packageTypeOptions" placeholder="请选择套餐类型" />
        </NFormItem>

        <NFormItem label="是否热门">
          <NSwitch v-model:value="formData.isHot" checked-value="1" unchecked-value="0" />
        </NFormItem>

        <NFormItem label="显示顺序">
          <NInputNumber v-model:value="formData.sortOrder" placeholder="请输入显示顺序" :min="0" style="width: 100%" />
        </NFormItem>

        <NFormItem label="生效时间">
          <NDatePicker v-model:value="formData.startTime" type="datetime" placeholder="请选择生效时间" style="width: 100%" />
        </NFormItem>

        <NFormItem label="失效时间">
          <NDatePicker v-model:value="formData.endTime" type="datetime" placeholder="请选择失效时间" style="width: 100%" />
        </NFormItem>

        <NFormItem label="状态" required>
          <NSelect v-model:value="formData.status" :options="statusOptions" placeholder="请选择状态" />
        </NFormItem>

        <NFormItem label="备注">
          <NInput v-model:value="formData.remark" type="textarea" placeholder="请输入备注" />
        </NFormItem>
      </NForm>

      <template #action>
        <NSpace>
          <NButton @click="showModal = false">取消</NButton>
          <NButton type="primary" @click="savePackage">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.points-package-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.header-section {
  margin-bottom: 32px;
}

.header-section .n-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.packages-section {
  min-height: 400px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.package-card {
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  background: #ffffff;
  border: none !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
}

.package-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  z-index: 1;
}

.package-card:hover {
  transform: translateY(-12px) rotateX(5deg);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.package-card[data-type="1"]::before {
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.package-card[data-type="2"]::before {
  background: linear-gradient(90deg, #f093fb, #f5576c);
}

.package-card[data-type="3"]::before {
  background: linear-gradient(90deg, #4facfe, #00f2fe);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-top: 8px;
}

.package-title-section {
  flex: 1;
}

.package-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.package-card[data-type="2"] .package-title {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.package-card[data-type="3"] .package-title {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.package-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.package-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.package-card:hover .package-actions {
  opacity: 1;
}

.package-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.package-description p {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  text-align: center;
}

.points-info {
  text-align: center;
  padding: 32px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20px;
  margin: 0 -8px;
  position: relative;
}

.points-info::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 22px;
  z-index: -1;
  opacity: 0.1;
}

.main-points {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.points-amount {
  font-size: 64px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.package-card[data-type="2"] .points-amount {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.package-card[data-type="3"] .points-amount {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.points-label {
  font-size: 20px;
  color: #64748b;
  font-weight: 600;
}

.bonus-points {
  margin-top: 12px;
}

.bonus-text {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.price-info {
  text-align: center;
  padding: 24px 0;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.current-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-bottom: 12px;
}

.currency {
  font-size: 24px;
  color: #64748b;
  font-weight: 600;
}

.price {
  font-size: 48px;
  font-weight: 800;
  color: #1e293b;
}

.original-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.original {
  text-decoration: line-through;
  color: #94a3b8;
  font-size: 16px;
  font-weight: 500;
}

.discount {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
}

.validity-info {
  padding: 16px 0;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.validity-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.validity-item .label {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.validity-item .value {
  color: #1e293b;
  font-size: 14px;
  font-weight: 600;
}

.purchase-section {
  padding-top: 24px;
}

.purchase-section .n-button {
  height: 56px;
  font-size: 18px;
  font-weight: 700;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.package-card[data-type="2"] .purchase-section .n-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.package-card[data-type="3"] .purchase-section .n-button {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.purchase-section .n-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-package-container {
    padding: 16px;
  }

  .package-header {
    flex-direction: column;
    gap: 16px;
  }

  .package-actions {
    align-self: flex-end;
    opacity: 1;
  }

  .package-title {
    font-size: 24px;
  }

  .points-amount {
    font-size: 48px;
  }

  .price {
    font-size: 36px;
  }

  .package-card:hover {
    transform: translateY(-8px);
  }
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.package-card:nth-child(odd) {
  animation: float 6s ease-in-out infinite;
}

.package-card:nth-child(even) {
  animation: float 6s ease-in-out infinite reverse;
}

.package-card:hover {
  animation-play-state: paused;
}

/* 加载动画 */
.packages-section .n-spin {
  min-height: 400px;
}

/* 标签样式优化 */
.package-tags .n-tag {
  font-weight: 600;
  border-radius: 20px;
  padding: 4px 12px;
}
</style>
