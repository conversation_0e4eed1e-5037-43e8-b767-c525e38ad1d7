<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreatePointsPackage, fetchUpdatePointsPackage } from '@/service/api/points/points-package';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsPackageOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Points.PointsPackage | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增积分套餐',
    edit: '编辑积分套餐'
  };
  return titles[props.operateType];
});

type Model = Api.Points.PointsPackageOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      packageName: '',
      packageDesc: '',
      pointsAmount: undefined,
      originalPrice: undefined,
      salePrice: undefined,
      discountRate: undefined,
      bonusPoints: undefined,
      packageType: '',
      isHot: '',
      sortOrder: undefined,
      startTime: undefined,
      endTime: undefined,
      status: '',
      remark: ''
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'tenantId'
  | 'packageDesc'
  | 'discountRate'
  | 'bonusPoints'
  | 'packageType'
  | 'isHot'
  | 'sortOrder'
  | 'startTime'
  | 'endTime'
  | 'status'
  | 'delFlag'
  | 'createDept'
  | 'createBy'
  | 'createTime'
  | 'updateBy'
  | 'updateTime'
  | 'remark'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  tenantId: createRequiredRule('租户编号不能为空'),
  packageDesc: createRequiredRule('套餐描述不能为空'),
  discountRate: createRequiredRule('折扣率(%)不能为空'),
  bonusPoints: createRequiredRule('赠送积分不能为空'),
  packageType: createRequiredRule('套餐类型（1普通套餐 2限时优惠 3新用户专享）不能为空'),
  isHot: createRequiredRule('是否热门（0否 1是）不能为空'),
  sortOrder: createRequiredRule('显示顺序不能为空'),
  startTime: createRequiredRule('生效时间不能为空'),
  endTime: createRequiredRule('失效时间不能为空'),
  status: createRequiredRule('状态（0正常 1停用）不能为空'),
  delFlag: createRequiredRule('删除标志（0代表存在 1代表删除）不能为空'),
  createDept: createRequiredRule('创建部门不能为空'),
  createBy: createRequiredRule('创建者不能为空'),
  createTime: createRequiredRule('创建时间不能为空'),
  updateBy: createRequiredRule('更新者不能为空'),
  updateTime: createRequiredRule('更新时间不能为空'),
  remark: createRequiredRule('备注不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, packageName, packageDesc, pointsAmount, originalPrice, salePrice, discountRate, bonusPoints, packageType, isHot, sortOrder, startTime, endTime, status, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreatePointsPackage({ packageName, packageDesc, pointsAmount, originalPrice, salePrice, discountRate, bonusPoints, packageType, isHot, sortOrder, startTime, endTime, status, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdatePointsPackage({ id, packageName, packageDesc, pointsAmount, originalPrice, salePrice, discountRate, bonusPoints, packageType, isHot, sortOrder, startTime, endTime, status, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="套餐名称" path="packageName">
          <NInput v-model:value="model.packageName" placeholder="请输入套餐名称" />
        </NFormItem>
        <NFormItem label="套餐描述" path="packageDesc">
          <NInput
            v-model:value="model.packageDesc"
            :rows="3"
            type="textarea"
            placeholder="请输入套餐描述"
          />
        </NFormItem>
        <NFormItem label="积分数量" path="pointsAmount">
          <NInput v-model:value="model.pointsAmount" placeholder="请输入积分数量" />
        </NFormItem>
        <NFormItem label="原价" path="originalPrice">
          <NInput v-model:value="model.originalPrice" placeholder="请输入原价" />
        </NFormItem>
        <NFormItem label="售价" path="salePrice">
          <NInput v-model:value="model.salePrice" placeholder="请输入售价" />
        </NFormItem>
        <NFormItem label="折扣率(%)" path="discountRate">
          <NInput v-model:value="model.discountRate" placeholder="请输入折扣率(%)" />
        </NFormItem>
        <NFormItem label="赠送积分" path="bonusPoints">
          <NInput v-model:value="model.bonusPoints" placeholder="请输入赠送积分" />
        </NFormItem>
        <NFormItem label="套餐类型（1普通套餐 2限时优惠 3新用户专享）" path="packageType">
          <NInput v-model:value="model.packageType" placeholder="请输入套餐类型（1普通套餐 2限时优惠 3新用户专享）" />
        </NFormItem>
        <NFormItem label="是否热门（0否 1是）" path="isHot">
          <NInput v-model:value="model.isHot" placeholder="请输入是否热门（0否 1是）" />
        </NFormItem>
        <NFormItem label="显示顺序" path="sortOrder">
          <NInput v-model:value="model.sortOrder" placeholder="请输入显示顺序" />
        </NFormItem>
        <NFormItem label="生效时间" path="startTime">
          <NDatePicker
            v-model:formatted-value="model.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="失效时间" path="endTime">
          <NDatePicker
            v-model:formatted-value="model.endTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="状态（0正常 1停用）" path="status">
          <NInput v-model:value="model.status" placeholder="请输入状态（0正常 1停用）" />
        </NFormItem>
        <NFormItem label="备注" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入备注"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
