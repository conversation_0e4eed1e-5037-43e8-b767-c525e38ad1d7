<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'PointsPackageSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Points.PointsPackageSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="套餐名称" path="packageName" class="pr-24px">
              <NInput v-model:value="model.packageName" placeholder="请输入套餐名称" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="套餐描述" path="packageDesc" class="pr-24px">
              <NInput v-model:value="model.packageDesc" placeholder="请输入套餐描述" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="积分数量" path="pointsAmount" class="pr-24px">
              <NInput v-model:value="model.pointsAmount" placeholder="请输入积分数量" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="原价" path="originalPrice" class="pr-24px">
              <NInput v-model:value="model.originalPrice" placeholder="请输入原价" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="售价" path="salePrice" class="pr-24px">
              <NInput v-model:value="model.salePrice" placeholder="请输入售价" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="折扣率(%)" path="discountRate" class="pr-24px">
              <NInput v-model:value="model.discountRate" placeholder="请输入折扣率(%)" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="赠送积分" path="bonusPoints" class="pr-24px">
              <NInput v-model:value="model.bonusPoints" placeholder="请输入赠送积分" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="套餐类型（1普通套餐 2限时优惠 3新用户专享）" path="packageType" class="pr-24px">
              <NSelect
                v-model:value="model.packageType"
                placeholder="请选择套餐类型（1普通套餐 2限时优惠 3新用户专享）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="是否热门（0否 1是）" path="isHot" class="pr-24px">
              <NInput v-model:value="model.isHot" placeholder="请输入是否热门（0否 1是）" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="显示顺序" path="sortOrder" class="pr-24px">
              <NInput v-model:value="model.sortOrder" placeholder="请输入显示顺序" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="生效时间" path="startTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.startTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="失效时间" path="endTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.endTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="状态（0正常 1停用）" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择状态（0正常 1停用）"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
