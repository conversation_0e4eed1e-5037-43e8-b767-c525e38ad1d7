<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreateBusinessesVideo, fetchUpdateBusinessesVideo } from '@/service/api/businesses/businesses-video';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'BusinessesVideoOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Businesses.BusinessesVideo | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增视频素材管理',
    edit: '编辑视频素材管理'
  };
  return titles[props.operateType];
});

type Model = Api.Businesses.BusinessesVideoOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    title: '',
    description: '',
    videoUrl: '',
    category: '',
    status: ''
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'description'
  | 'category'
  | 'tenantId'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  description: createRequiredRule('视频素材详细描述不能为空'),
  category: createRequiredRule('视频素材分类，例如：教学、宣传、娱乐不能为空'),
  tenantId: createRequiredRule('租户ID，标记数据所属租户，可通过配置关闭不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, title, description, videoUrl, category, status } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateBusinessesVideo({ title, description, videoUrl, category, status });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateBusinessesVideo({ id, title, description, videoUrl, category, status });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="视频标题" path="title">
          <NInput v-model:value="model.title" placeholder="标题名称" />
        </NFormItem>
        <NFormItem label="视频素材详细描述" path="description">
          <NInput
            v-model:value="model.description"
            :rows="3"
            type="textarea"
            placeholder="请输入视频素材详细描述"
          />
        </NFormItem>
        <NFormItem label="视频文件存储的URL或路径，支持OSS/CDN" path="videoUrl">
          <NInput
            v-model:value="model.videoUrl"
            :rows="3"
            type="textarea"
            placeholder="请输入视频文件存储的URL或路径，支持OSS/CDN"
          />
        </NFormItem>
        <NFormItem label="视频素材分类，例如：教学、宣传、娱乐" path="category">
          <NInput v-model:value="model.category" placeholder="请输入视频素材分类，例如：教学、宣传、娱乐" />
        </NFormItem>
        <NFormItem label="素材状态" path="status">
          <NInput v-model:value="model.status" placeholder="请输入素材状态" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>



<style scoped></style>
