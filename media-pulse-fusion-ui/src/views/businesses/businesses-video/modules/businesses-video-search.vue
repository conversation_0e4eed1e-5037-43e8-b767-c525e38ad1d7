<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'BusinessesVideoSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Businesses.BusinessesVideoSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="视频素材标题或名称" path="title" class="pr-24px">
              <NInput v-model:value="model.title" placeholder="请输入视频素材标题或名称" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="视频素材详细描述" path="description" class="pr-24px">
              <NInput v-model:value="model.description" placeholder="请输入视频素材详细描述" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="视频文件存储的URL或路径，支持OSS/CDN" path="videoUrl" class="pr-24px">
              <NInput v-model:value="model.videoUrl" placeholder="请输入视频文件存储的URL或路径，支持OSS/CDN" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="视频素材分类，例如：教学、宣传、娱乐" path="category" class="pr-24px">
              <NInput v-model:value="model.category" placeholder="请输入视频素材分类，例如：教学、宣传、娱乐" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="素材状态" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择素材状态"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
