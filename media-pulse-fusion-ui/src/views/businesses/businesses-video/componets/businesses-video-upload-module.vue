<script setup lang="ts">
import { ref, computed } from 'vue';

defineOptions({
  name: 'BusinessesVideoUploadModule'
});

interface Props {
  /** 上传类型 */
  uploadType?: 'video' | 'audio' | 'clone-voice';
  /** 是否支持多文件上传 */
  multiple?: boolean;
  /** 最大上传数量 */
  maxCount?: number;
  /** 接受的文件格式 */
  accept?: string;
  /** 上传提示文本 */
  hintText?: string;
  /** 是否正在上传 */
  uploading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  uploadType: 'video',
  multiple: true,
  maxCount: 50,
  accept: '.mp4,.avi,.mov,.wmv,.flv,.mkv,.webm,.m4v,.3gp,.ts,.mts,.m2ts',
  hintText: '可上传多个文件',
  uploading: false
});

interface Emits {
  (e: 'upload', payload: { file: File; onFinish: () => void; onError: () => void }): void;
  (e: 'preview', file: any): void;
  (e: 'remove', file: any): void;
  (e: 'showPreview'): void;
  (e: 'update:fileList', value: any[]): void;
}

const emit = defineEmits<Emits>();

// 文件列表
const fileList = defineModel<any[]>('fileList', { default: () => [] });

// 上传状态
const isUploading = ref(false);

// 根据上传类型计算图标
const uploadIcon = computed(() => {
  switch (props.uploadType) {
    case 'video':
      return {
        svg: `<polygon points="23 7 16 12 23 17 23 7"></polygon><rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>`,
        title: '上传视频',
        formats: '支持 MP4、AVI、MOV 等格式'
      };
    case 'audio':
      return {
        svg: `<path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="20" cy="16" r="3"></circle>`,
        title: '上传音频',
        formats: '支持 MP3、WAV、AAC 等格式'
      };
    case 'clone-voice':
      return {
        svg: `<path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path><path d="M19 10v2a7 7 0 0 1-14 0v-2"></path><line x1="12" y1="19" x2="12" y2="23"></line><line x1="8" y1="23" x2="16" y2="23"></line>`,
        title: '上传克隆音色文件',
        formats: '支持mp3/wav/ogg等格式，仅限1个文件'
      };
    default:
      return {
        svg: `<polygon points="23 7 16 12 23 17 23 7"></polygon><rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>`,
        title: '上传文件',
        formats: '支持多种格式'
      };
  }
});

// 处理文件上传
function handleUpload(options: any) {
  isUploading.value = true;

  // 检查 file 对象结构
  const actualFile = options.file?.file || options.file;

  emit('upload', {
    file: actualFile,
    onFinish: (response?: any) => {
      isUploading.value = false;
      options.onFinish(response);
    },
    onError: () => {
      isUploading.value = false;
      options.onError();
    }
  });
}

// 处理文件预览
function handlePreview(file: any) {
  emit('preview', file);
}

// 处理文件删除
function handleRemove(file: any) {
  emit('remove', file);
}

// 获取文件数量
function getFileCount(): number {
  if (!fileList.value) return 0;
  if (Array.isArray(fileList.value)) {
    return fileList.value.length;
  }
  return 0;
}
</script>

<template>
  <div class="enhanced-upload-container">
    <NUpload
      :file-list="fileList"
      :custom-request="handleUpload"
      :accept="accept"
      :multiple="multiple"
      :max="maxCount"
      :show-file-list="false"
      @preview="handlePreview"
      @remove="handleRemove"
      @update:file-list="(value) => emit('update:fileList', value)"
    >
      <div
        class="enhanced-upload-trigger"
        :class="[`${uploadType}-upload`, { uploading: isUploading || uploading }]"
      >
        <!-- 上传状态遮罩 -->
        <div v-if="isUploading || uploading" class="upload-loading-overlay">
          <NSpin size="large">
            <template #description>
              <span class="upload-loading-text">正在上传{{ uploadIcon.title.replace('上传', '') }}...</span>
            </template>
          </NSpin>
        </div>

        <div class="upload-icon-wrapper">
          <div class="upload-icon-bg">
            <NIcon size="22" class="upload-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                v-html="uploadIcon.svg"
              />
            </NIcon>
          </div>
        </div>

        <div class="upload-content">
          <h4 class="upload-title">{{ uploadIcon.title }}</h4>
          <p class="upload-description">{{ hintText }}</p>
          <div class="upload-formats">{{ uploadIcon.formats }}</div>
        </div>

        <div class="upload-action">
          <NButton type="primary" size="small" ghost :disabled="isUploading || uploading">
            <template #icon>
              <NIcon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17,8 12,3 7,8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </NIcon>
            </template>
            选择文件
          </NButton>
        </div>
      </div>
    </NUpload>

    <!-- 文件预览按钮 -->
<!--    <div-->
<!--      v-if="fileList && getFileCount() > 0"-->
<!--      class="file-preview-trigger"-->
<!--      @click="$emit('showPreview')"-->
<!--    >-->
<!--      <div class="preview-trigger-content">-->
<!--        <NIcon size="16" class="preview-trigger-icon">-->
<!--          <svg-->
<!--            xmlns="http://www.w3.org/2000/svg"-->
<!--            viewBox="0 0 24 24"-->
<!--            fill="none"-->
<!--            stroke="currentColor"-->
<!--            stroke-width="2"-->
<!--            stroke-linecap="round"-->
<!--            stroke-linejoin="round"-->
<!--          >-->
<!--            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>-->
<!--            <circle cx="12" cy="12" r="3"></circle>-->
<!--          </svg>-->
<!--        </NIcon>-->
<!--        <span class="preview-trigger-text">查看已上传的 {{ getFileCount() }} 个文件</span>-->
<!--        <NIcon size="14" class="preview-trigger-arrow">-->
<!--          <svg-->
<!--            xmlns="http://www.w3.org/2000/svg"-->
<!--            viewBox="0 0 24 24"-->
<!--            fill="none"-->
<!--            stroke="currentColor"-->
<!--            stroke-width="2"-->
<!--            stroke-linecap="round"-->
<!--            stroke-linejoin="round"-->
<!--          >-->
<!--            <polyline points="9,18 15,12 9,6"></polyline>-->
<!--          </svg>-->
<!--        </NIcon>-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<style scoped lang="scss">
/* 增强上传容器样式 */
.enhanced-upload-container {
  position: relative;
  width: 100%;
}

.enhanced-upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 24px 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 120px;
  position: relative;
  overflow: hidden;
}

.enhanced-upload-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 150, 255, 0.02) 0%, rgba(64, 150, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-upload-trigger:hover {
  border-color: #4096ff;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 150, 255, 0.15);
}

.enhanced-upload-trigger:hover::before {
  opacity: 1;
}

.enhanced-upload-trigger.video-upload:hover {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
  box-shadow: 0 8px 25px rgba(82, 196, 26, 0.15);
}

.enhanced-upload-trigger.audio-upload:hover {
  border-color: #fa8c16;
  background: linear-gradient(135deg, #fff7e6 0%, #fef5e7 100%);
  box-shadow: 0 8px 25px rgba(250, 140, 22, 0.15);
}

.enhanced-upload-trigger.clone-voice-upload:hover {
  border-color: #722ed1;
  background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
  box-shadow: 0 8px 25px rgba(114, 46, 209, 0.15);
}

.upload-icon-wrapper {
  margin-bottom: 12px;
}

.upload-icon-bg {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4096ff 0%, #1677ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.3);
  transition: all 0.3s ease;
}

.video-upload .upload-icon-bg {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.audio-upload .upload-icon-bg {
  background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
  box-shadow: 0 4px 12px rgba(250, 140, 22, 0.3);
}

.clone-voice-upload .upload-icon-bg {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
  box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
}

.enhanced-upload-trigger:hover .upload-icon-bg {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(64, 150, 255, 0.4);
}

.video-upload:hover .upload-icon-bg {
  box-shadow: 0 6px 20px rgba(82, 196, 26, 0.4);
}

.audio-upload:hover .upload-icon-bg {
  box-shadow: 0 6px 20px rgba(250, 140, 22, 0.4);
}

.clone-voice-upload:hover .upload-icon-bg {
  box-shadow: 0 6px 20px rgba(114, 46, 209, 0.4);
}

.upload-icon {
  color: white;
}

.upload-content {
  text-align: center;
  margin-bottom: 10px;
}

.upload-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.upload-description {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0 0 6px 0;
  line-height: 1.4;
}

.upload-formats {
  font-size: 12px;
  color: #bfbfbf;
  margin: 0;
}

.upload-action {
  margin-top: 4px;
}

/* 上传状态遮罩样式 */
.upload-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 12px;
}

.upload-loading-text {
  color: #4096ff;
  font-weight: 500;
  margin-top: 8px;
}

.enhanced-upload-trigger.uploading {
  pointer-events: none;
  opacity: 0.8;
}

.enhanced-upload-trigger.uploading .upload-icon-bg {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 150, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(64, 150, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 150, 255, 0.3);
  }
}

/* 文件预览按钮样式 */
.file-preview-trigger {
  margin-top: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-preview-trigger:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #4096ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.15);
}

.preview-trigger-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-trigger-icon {
  color: #4096ff;
}

.preview-trigger-text {
  flex: 1;
  font-size: 14px;
  color: #475569;
  font-weight: 500;
}

.preview-trigger-arrow {
  color: #94a3b8;
  transition: transform 0.3s ease;
}

.file-preview-trigger:hover .preview-trigger-arrow {
  transform: translateX(2px);
}

/* NUpload 组件样式覆盖 */
:deep(.n-upload) {
  width: 100%;
}

:deep(.n-upload-trigger) {
  width: 100% !important;
  height: auto !important;
  min-height: 100px;
  border: 2px dashed #e2e8f0 !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
  transition: all 0.3s ease !important;
}

:deep(.n-upload-trigger:hover) {
  border-color: #667eea !important;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15) !important;
}
</style>
