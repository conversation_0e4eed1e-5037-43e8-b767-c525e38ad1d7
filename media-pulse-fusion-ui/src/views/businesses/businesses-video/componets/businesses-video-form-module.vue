<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { fetchGetBusinessesVideoList, fetchUpdateBusinessesVideo } from '@/service/api/businesses/businesses-video';
import BusinessesVideoBindStoreModal from './businesses-video-bind-store-modal.vue';

defineOptions({
  name: 'BusinessesVideoFormModule'
});

// 视频列表相关状态
const videoList = ref<Api.Businesses.BusinessesVideo[]>([]);
const loading = ref(false);
const editingVideo = ref<Api.Businesses.BusinessesVideo | null>(null);
const showEditModal = ref(false);

// 编辑表单数据
const editForm = ref({
  id: null as number | null,
  title: '',
  description: '',
  videoUrl: '',
  category: '',
  status: ''
});

// 绑定相关状态
const showBindModal = ref(false);
const currentBindVideo = ref<Api.Businesses.BusinessesVideo | null>(null);

// 获取视频列表
async function fetchVideoList() {
  try {
    loading.value = true;
    const response = await fetchGetBusinessesVideoList();
    // 根据实际返回的数据结构调整：response.data 包含真正的数据
    if (response.data && response.data.code === 200) {
      videoList.value = response.data.rows || [];
    } else {
      window.$message?.error(response.data?.msg || '获取视频列表失败');
    }
  } catch (error) {
    window.$message?.error('获取视频列表失败');
  } finally {
    loading.value = false;
  }
}

// 打开编辑弹窗
function openEditModal(video?: Api.Businesses.BusinessesVideo) {
  if (video) {
    editingVideo.value = video;
    editForm.value = {
      id: video.id,
      title: video.title || '',
      description: video.description || '',
      videoUrl: video.videoUrl || '',
      category: video.category || '',
      status: video.status || ''
    };
  } else {
    editingVideo.value = null;
    editForm.value = {
      id: null,
      title: '',
      description: '',
      videoUrl: '',
      category: '',
      status: ''
    };
  }
  showEditModal.value = true;
}

// 保存视频信息
async function saveVideo() {
  try {
    if (editingVideo.value) {
      // 更新
      await fetchUpdateBusinessesVideo(editForm.value);
      window.$message?.success('更新成功');
    } else {
      // 新增
      await fetchCreateBusinessesVideo(editForm.value);
      window.$message?.success('创建成功');
    }
    showEditModal.value = false;
    await fetchVideoList();
  } catch (error) {
    window.$message?.error('保存失败');
  }
}

// 删除视频
async function deleteVideo(id: number) {
  try {
    await fetchBatchDeleteBusinessesVideo([id]);
    window.$message?.success('删除成功');
    await fetchVideoList();
  } catch (error) {
    window.$message?.error('删除失败');
  }
}

// 打开绑定弹窗
function openBindModal(video: Api.Businesses.BusinessesVideo) {
  currentBindVideo.value = video;
  showBindModal.value = true;
}

// 处理绑定成功
function handleBindSuccess() {
  showBindModal.value = false;
  currentBindVideo.value = null;
  window.$message?.success('绑定成功');
}

// 组件挂载时获取视频列表
onMounted(() => {
  fetchVideoList();
});
</script>

<template>
  <div class="video-list-container">
    <!-- 视频列表头部 -->
    <div class="video-list-header">
      <h3 class="video-list-title">
        <NIcon size="18" class="title-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="23 7 16 12 23 17 23 7"></polygon>
            <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
          </svg>
        </NIcon>
        视频列表
      </h3>
      <NButton type="primary" size="small" @click="openEditModal()">
        <template #icon>
          <NIcon>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </NIcon>
        </template>
        新增视频
      </NButton>
    </div>

    <!-- 视频列表内容 -->
    <NSpin :show="loading">
      <div v-if="videoList.length === 0" class="empty-state">
        <div class="empty-icon">
          <NIcon size="48" color="#d9d9d9">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="23 7 16 12 23 17 23 7"></polygon>
              <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
            </svg>
          </NIcon>
        </div>
        <p class="empty-text">暂无视频数据</p>
        <p class="empty-hint">点击上方"新增视频"按钮添加视频</p>
      </div>

      <div v-else class="video-grid">
        <div v-for="video in videoList" :key="video.id" class="video-card">
          <!-- 视频播放区域 -->
          <div class="video-player-container">
            <video
              :src="video.videoUrl"
              class="video-player"
              controls
              preload="metadata"
              :poster="video.thumbnailUrl || ''"
            >
              您的浏览器不支持视频播放
            </video>
          </div>

          <!-- 视频信息 -->
          <div class="video-card-content">
            <h4 class="video-title">{{ video.title || '未命名视频' }}</h4>
            <p class="video-description">{{ video.description || '暂无描述' }}</p>
            <div class="video-meta">
              <span v-if="video.category" class="video-category">{{ video.category }}</span>
<!--              <span v-if="video.status" class="video-status" :class="`status-${video.status}`">{{ video.status }}</span>-->
            </div>
            <!-- 操作按钮区域 -->
            <div class="video-actions">
              <NButton
                class="action-button edit-button"
                size="small"
                ghost
                @click="openEditModal(video)"
              >
                <template #icon>
                  <NIcon size="14">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                  </NIcon>
                </template>
                编辑
              </NButton>
              <NButton
                class="action-button bind-button"
                size="small"
                ghost
                type="warning"
                @click="openBindModal(video)"
              >
                <template #icon>
                  <NIcon size="14">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                      <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                    </svg>
                  </NIcon>
                </template>
                绑定
              </NButton>
              <NPopconfirm @positive-click="deleteVideo(video.id)">
                <template #trigger>
                  <NButton
                    class="action-button delete-button"
                    size="small"
                    ghost
                  >
                    <template #icon>
                      <NIcon size="14">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                      </NIcon>
                    </template>
                    删除
                  </NButton>
                </template>
                确定删除这个视频吗？
              </NPopconfirm>
            </div>
          </div>
        </div>
      </div>
    </NSpin>

    <!-- 编辑弹窗 -->
    <NModal v-model:show="showEditModal" preset="dialog" :title="editingVideo ? '编辑视频' : '新增视频'">
      <NForm :model="editForm" label-placement="left" label-width="80px">
        <NFormItem label="标题" path="title">
          <NInput v-model:value="editForm.title" placeholder="请输入视频标题" />
        </NFormItem>
        <NFormItem label="描述" path="description">
          <NInput
            v-model:value="editForm.description"
            type="textarea"
            placeholder="请输入视频描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="视频URL" path="videoUrl">
          <NInput v-model:value="editForm.videoUrl" placeholder="请输入视频URL" />
        </NFormItem>
        <NFormItem label="分类" path="category">
          <NInput v-model:value="editForm.category" placeholder="请输入视频分类" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSelect
            v-model:value="editForm.status"
            placeholder="请选择状态"
            :options="[
              { label: '草稿', value: 'draft' },
              { label: '发布', value: 'published' },
              { label: '下架', value: 'archived' }
            ]"
          />
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="showEditModal = false">取消</NButton>
          <NButton type="primary" @click="saveVideo">保存</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 绑定视频到店铺弹窗 -->
    <BusinessesVideoBindStoreModal
      v-model:visible="showBindModal"
      :video="currentBindVideo"
      @success="handleBindSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.video-list-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  height: 100vh;
  overflow-y: auto;
}

.video-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.video-list-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.title-icon {
  color: #4096ff;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #8c8c8c;
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  color: #bfbfbf;
  margin: 0;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.video-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: #fff;
  min-height: 300px;
}

.video-card:hover {
  border-color: #4096ff;
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.15);
  transform: translateY(-2px);
}

/* 视频播放器容器 */
.video-player-container {
  position: relative;
  width: 100%;
  height: 250px;
  background: #000;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
}

/* 底部操作按钮样式 */
.video-actions {
  display: flex;
  gap: 0;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.action-button {
  flex: 1;
  background: transparent !important;
  border: 1px solid #d9d9d9;
  border-radius: 0;
}

.action-button:first-child {
  border-radius: 4px 0 0 4px;
  border-right: none;
}

.action-button:last-child {
  border-radius: 0 4px 4px 0;
}

.edit-button {
  color: #1890ff !important;
  border-color: #1890ff !important;
}

.edit-button:hover {
  background: rgba(24, 144, 255, 0.1) !important;
  border-color: #40a9ff !important;
}

.delete-button {
  color: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

.delete-button:hover {
  background: rgba(255, 77, 79, 0.1) !important;
  border-color: #ff7875 !important;
}

.video-card-content {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-description {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.video-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.video-category {
  padding: 2px 8px;
  background: #f0f7ff;
  color: #4096ff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.video-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-draft {
  background: #fff7e6;
  color: #fa8c16;
}

.status-published {
  background: #f6ffed;
  color: #52c41a;
}

.status-archived {
  background: #f5f5f5;
  color: #8c8c8c;
}

/* 响应式设计 - 每行4个卡片 */
@media (max-width: 1400px) {
  .video-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .video-player-container {
    height: 200px;
  }
}

@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .video-list-container {
    padding: 16px;
  }

  .video-list-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .video-player-container {
    height: 180px;
  }

  .video-card {
    min-height: 320px;
  }
}
</style>

