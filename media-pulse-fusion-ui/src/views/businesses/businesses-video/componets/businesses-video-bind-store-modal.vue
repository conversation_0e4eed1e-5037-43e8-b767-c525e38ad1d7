<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { fetchBindVideosToStore } from '@/service/api/businesses/businesses-video';
import { fetchGetStoreList } from '@/service/api/businesses/store';

defineOptions({
  name: 'BusinessesVideoBindStoreModal'
});

interface Props {
  visible: boolean;
  video: Api.Businesses.BusinessesVideo | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 弹窗显示状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 店铺列表相关
const storeList = ref<Api.Businesses.Store[]>([]);
const storeLoading = ref(false);
const selectedStoreIds = ref<number[]>([]);

// 绑定状态
const bindLoading = ref(false);

// 获取店铺列表
async function fetchStores() {
  try {
    storeLoading.value = true;
    const response = await fetchGetStoreList();

    if (response && response.data && response.data.code === 200) {
      storeList.value = response.data.rows || [];
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error);
  } finally {
    storeLoading.value = false;
  }
}

// 处理店铺选择
function handleStoreSelect(storeId: number, checked: boolean) {
  if (checked) {
    if (!selectedStoreIds.value.includes(storeId)) {
      selectedStoreIds.value.push(storeId);
    }
  } else {
    const index = selectedStoreIds.value.indexOf(storeId);
    if (index > -1) {
      selectedStoreIds.value.splice(index, 1);
    }
  }
}

// 处理店铺卡片点击
function handleStoreCardClick(storeId: number) {
  const isSelected = selectedStoreIds.value.includes(storeId);
  handleStoreSelect(storeId, !isSelected);
}

// 全选/取消全选
function handleSelectAll(checked: boolean) {
  if (checked) {
    selectedStoreIds.value = storeList.value.map(store => store.id);
  } else {
    selectedStoreIds.value = [];
  }
}

// 执行绑定
async function handleBind() {
  if (!props.video) {
    return;
  }

  if (selectedStoreIds.value.length === 0) {
    return;
  }

  try {
    bindLoading.value = true;

    // 为每个选中的店铺执行绑定
    const bindPromises = selectedStoreIds.value.map(storeId => {
      const bindData = {
        storeId,
        selfVideoIds: [props.video!.id],
        otherVideoIds: []
      };
      return fetchBindVideosToStore(bindData);
    });
    emit('success');
  } catch (error) {
    console.error('绑定失败:', error);
  } finally {
    bindLoading.value = false;
  }
}

// 重置状态
function resetState() {
  selectedStoreIds.value = [];
}

// 监听弹窗打开
function handleModalOpen() {
  resetState();
  fetchStores();
}

// 组件挂载时获取店铺列表
onMounted(() => {
  if (props.visible) {
    handleModalOpen();
  }
});
</script>

<template>
  <NModal
    v-model:show="modalVisible"
    preset="dialog"
    title="绑定视频到店铺"
    style="width: 600px;"
    @after-enter="handleModalOpen"
  >
    <div class="bind-modal-content">
      <!-- 视频信息 -->
      <div v-if="video" class="video-info">
        <h4>选中视频：{{ video.title || '未命名视频' }}</h4>
        <p class="video-desc">{{ video.description || '暂无描述' }}</p>
      </div>

      <!-- 店铺选择区域 -->
      <div class="store-selection">
        <div class="selection-header">
          <h4>选择店铺</h4>
          <div class="selection-actions">
            <NCheckbox
              :checked="selectedStoreIds.length === storeList.length && storeList.length > 0"
              :indeterminate="selectedStoreIds.length > 0 && selectedStoreIds.length < storeList.length"
              @update:checked="handleSelectAll"
            >
              全选 ({{ selectedStoreIds.length }}/{{ storeList.length }})
            </NCheckbox>
          </div>
        </div>

        <!-- 店铺列表 -->
        <NSpin :show="storeLoading">
          <div v-if="storeList.length === 0" class="empty-state">
            <p>暂无店铺数据</p>
          </div>
          <div v-else class="store-list">
            <div
              v-for="store in storeList"
              :key="store.id"
              class="store-item"
              :class="{ 'selected': selectedStoreIds.includes(store.id) }"
              @click="handleStoreCardClick(store.id)"
            >
              <NCheckbox
                :checked="selectedStoreIds.includes(store.id)"
                @click.stop
                @update:checked="(checked) => handleStoreSelect(store.id, checked)"
              />
              <div class="store-info">
                <div class="store-name">{{ store.storeName || '未命名店铺' }}</div>
                <div class="store-desc">{{ store.storeAddress || '暂无地址' }}</div>
              </div>
            </div>
          </div>
        </NSpin>
      </div>
    </div>

    <template #action>
      <NSpace>
        <NButton @click="modalVisible = false">取消</NButton>
        <NButton
          type="primary"
          :loading="bindLoading"
          :disabled="selectedStoreIds.length === 0"
          @click="handleBind"
        >
          绑定 ({{ selectedStoreIds.length }})
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped lang="scss">
.bind-modal-content {
  max-height: 500px;
  overflow-y: auto;
}

.video-info {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;

  h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
  }

  .video-desc {
    margin: 0;
    color: #666;
    font-size: 12px;
  }
}

.store-selection {
  .selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    h4 {
      margin: 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
  }

  .store-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .store-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
      background-color: #f6ffed;
    }

    &.selected {
      border-color: #1890ff;
      background-color: #e6f7ff;
    }

    .store-info {
      margin-left: 12px;
      flex: 1;

      .store-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .store-desc {
        font-size: 12px;
        color: #666;
      }
    }
  }
}
</style>
