<script setup lang="ts">
// 修改导入语句
import { computed, reactive, watch } from 'vue';
import { NModal, NSpace } from 'naive-ui'; // 添加这些导入
import { fetchCreateStore, fetchUpdateStore } from '@/service/api/businesses/store';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'StoreOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Businesses.Store | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增店铺',
    edit: '编辑店铺'
  };
  return titles[props.operateType];
});

type Model = Api.Businesses.StoreOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: undefined,
    merchantId: undefined,
    storeName: '',
    storePhone: '',
    storeAddress: '',
    title: '',
    headLine: ''
  };
}

type RuleKey = Extract<keyof Model, 'storePhone' | 'storeAddress' | 'tenantId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  storePhone: createRequiredRule('店铺电话不能为空'),
  storeAddress: createRequiredRule('店铺地址不能为空'),
  tenantId: createRequiredRule('租户ID，标记数据所属租户，可通过配置关闭不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, merchantId, storeName, storePhone, storeAddress } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateStore({ id, merchantId, storeName, storePhone, storeAddress });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateStore({ id, merchantId, storeName, storePhone, storeAddress });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" :style="{ width: '800px' }" class="max-w-90%">
    <NForm ref="formRef" :model="model" :rules="rules">
<!--      <NFormItem label="主键ID" path="id">-->
<!--        <NInput v-model:value="model.id" placeholder="请输入主键ID" />-->
<!--      </NFormItem>-->
<!--      <NFormItem label="所属商户ID（无外键约束）" path="merchantId">-->
<!--        <NInput v-model:value="model.merchantId" placeholder="请输入所属商户ID（无外键约束）" />-->
<!--      </NFormItem>-->
      <NFormItem label="店铺名称" path="storeName">
        <NInput v-model:value="model.storeName" placeholder="请输入店铺名称" />
      </NFormItem>
      <NFormItem label="店铺电话" path="storePhone">
        <NInput v-model:value="model.storePhone" placeholder="请输入店铺电话" />
      </NFormItem>
      <NFormItem label="店铺地址" path="storeAddress">
        <NInput v-model:value="model.storeAddress" placeholder="请输入店铺地址" />
      </NFormItem>
      <NFormItem label="店铺标题" path="title">
        <NInput v-model:value="model.title" placeholder="请输入店铺标题" />
      </NFormItem>
      <NFormItem label="店铺文案" path="headLine">
        <NInput v-model:value="model.headLine" placeholder="请输入店铺文案" />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace :size="16">
        <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
        <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>


<style scoped></style>
