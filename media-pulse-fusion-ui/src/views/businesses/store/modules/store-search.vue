<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'StoreSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Businesses.StoreSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
<!--            <NFormItemGi span="24 s:12 m:6" label="所属商户ID（无外键约束）" path="merchantId" class="pr-24px">-->
<!--              <NInput v-model:value="model.merchantId" placeholder="请输入所属商户ID（无外键约束）" />-->
<!--            </NFormItemGi>-->
            <NFormItemGi span="24 s:12 m:6" label="店铺名称" path="storeName" class="pr-24px">
              <NInput v-model:value="model.storeName" placeholder="请输入店铺名称" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="店铺电话" path="storePhone" class="pr-24px">
              <NInput v-model:value="model.storePhone" placeholder="请输入店铺电话" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="店铺地址" path="storeAddress" class="pr-24px">
              <NInput v-model:value="model.storeAddress" placeholder="请输入店铺地址" />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
