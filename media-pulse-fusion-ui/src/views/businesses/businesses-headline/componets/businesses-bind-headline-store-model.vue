<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { fetchBindBusinessesHeadline } from '@/service/api/businesses/businesses-headline';
import { fetchGetStoreList } from '@/service/api/businesses/store';
import { $t } from '@/locales';

defineOptions({
  name: 'BusinessesBindHeadlineStoreModule'
});

interface Props {
  /** 弹窗显示状态 */
  visible?: boolean;
  /** 文案ID */
  headlineId?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  headlineId: null
});

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

// 弹窗显示状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 店铺列表数据
const storeList = ref<Api.Businesses.Store[]>([]);
const loading = ref(false);
const submitting = ref(false);

// 选中的店铺
const selectedStoreIds = ref<number[]>([]);
const selectAll = ref(false);

// 获取店铺列表
async function getStoreList() {
  loading.value = true;
  try {
    const { data, error } = await fetchGetStoreList({
      pageNum: 1,
      pageSize: 1000 // 获取所有店铺
    });
    if (!error && data) {
      // API返回的数据结构是 {code, msg, rows, total}，我们需要使用 rows
      storeList.value = data.rows || [];
    }
  } catch (err) {
    console.error('获取店铺列表失败:', err);
    window.$message?.error('获取店铺列表失败');
  } finally {
    loading.value = false;
  }
}

// 全选/取消全选
function handleSelectAll(checked: boolean) {
  selectAll.value = checked;
  if (checked) {
    selectedStoreIds.value = storeList.value.map(store => store.id as number);
  } else {
    selectedStoreIds.value = [];
  }
}

// 单个选择
function handleStoreSelect(storeId: number, checked: boolean) {
  if (checked) {
    if (!selectedStoreIds.value.includes(storeId)) {
      selectedStoreIds.value.push(storeId);
    }
  } else {
    const index = selectedStoreIds.value.indexOf(storeId);
    if (index > -1) {
      selectedStoreIds.value.splice(index, 1);
    }
  }

  // 更新全选状态
  selectAll.value = selectedStoreIds.value.length === storeList.value.length;
}

// 处理店铺卡片点击（点击卡片任何地方都能选择）
function handleStoreCardClick(storeId: number) {
  const isSelected = selectedStoreIds.value.includes(storeId);
  handleStoreSelect(storeId, !isSelected);
}

// 提交绑定
async function handleSubmit() {
  if (!props.headlineId) {
    window.$message?.error('文案ID不能为空');
    return;
  }

  if (selectedStoreIds.value.length === 0) {
    window.$message?.error('请至少选择一个店铺');
    return;
  }

  submitting.value = true;
  try {
    // 批量绑定
    const promises = selectedStoreIds.value.map(storeId =>
      fetchBindBusinessesHeadline({
        storeId,
        headLineId: props.headlineId!
      })
    );

    const results = await Promise.all(promises);
    const hasError = results.some(result => result.error);

    if (!hasError) {
      window.$message?.success(`成功绑定到 ${selectedStoreIds.value.length} 个店铺`);
      emit('success');
    }
  } catch (err) {
    console.error('绑定失败:', err);
    window.$message?.error('绑定失败，请重试');
  } finally {
    submitting.value = false;
  }
}

// 关闭弹窗
function handleClose() {
  modalVisible.value = false;
  // 重置状态
  selectedStoreIds.value = [];
  selectAll.value = false;
}

// 监听弹窗显示状态
watch(modalVisible, (visible) => {
  if (visible) {
    getStoreList();
  }
});
</script>

<template>
  <NModal
    v-model:show="modalVisible"
    preset="card"
    title="绑定到店铺"
    class="w-800px max-w-90%"
    :mask-closable="false"
    @close="handleClose"
  >
    <div class="bind-store-content">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <NCheckbox
          v-model:checked="selectAll"
          @update:checked="handleSelectAll"
        >
          全选 ({{ selectedStoreIds.length }}/{{ storeList.length }})
        </NCheckbox>
        <div class="selected-info">
          已选择 {{ selectedStoreIds.length }} 个店铺
        </div>
      </div>

      <!-- 店铺列表 -->
      <div class="store-list" :class="{ 'loading': loading }">
        <div
          v-for="store in storeList"
          :key="store.id"
          class="store-item"
          :class="{ 'selected': selectedStoreIds.includes(store.id) }"
          @click="handleStoreCardClick(store.id)"
        >
          <NCheckbox
            :checked="selectedStoreIds.includes(store.id)"
            @click.stop
            @update:checked="(checked) => handleStoreSelect(store.id, checked)"
          />
          <div class="store-info">
            <div class="store-name">{{ store.storeName }}</div>
            <div class="store-details">
              <span class="store-phone">{{ store.storePhone }}</span>
              <span class="store-address">{{ store.storeAddress }}</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && storeList.length === 0" class="empty-state">
          <NEmpty description="暂无店铺数据" />
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="footer-actions">
        <NButton @click="handleClose">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton
          type="primary"
          :loading="submitting"
          :disabled="selectedStoreIds.length === 0"
          @click="handleSubmit"
        >
          确认绑定 ({{ selectedStoreIds.length }})
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
.bind-store-content {
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.selected-info {
  font-size: 14px;
  color: #666;
}

.store-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.store-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.store-item:hover {
  border-color: #1890ff;
  background-color: #f6f8ff;
}

.store-item.selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.store-info {
  flex: 1;
}

.store-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.store-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.store-phone,
.store-address {
  font-size: 14px;
  color: #8c8c8c;
}

.store-phone::before {
  content: "📞 ";
}

.store-address::before {
  content: "📍 ";
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
