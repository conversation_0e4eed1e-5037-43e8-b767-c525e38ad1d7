<script setup lang="ts">
import { ref, reactive } from 'vue';
import { NCard, NForm, NFormItem, NFormItemGi, NInput, NInputNumber, NButton, NSpace, NList, NListItem, NTag, NIcon, NSpin, useMessage, NSelect, NCheckbox, NGrid, NDivider, NEmpty, NTooltip } from 'naive-ui';
import {
  fetchRequestBusinessesHeadline,
} from '@/service/api/businesses/businesses-headline';

defineOptions({
  name: 'BusinessesCreateHeadlineModule'
});

// 定义emit事件
const emit = defineEmits<{
  switchToList: []
}>();

const message = useMessage();

// 表单数据
const formData = reactive({
  title: '',
  count: 3
});

// 生成的文案列表
const generatedHeadlines = ref<string[]>([]);
const loading = ref(false);

// 选中的文案
const selectedHeadlines = ref<Set<number>>(new Set());

// 表单验证规则
const rules = {
  title: {
    required: true,
    message: '请输入文案标题',
    trigger: 'blur'
  },
  count: {
    required: true,
    type: 'number',
    min: 1,
    max: 100,
    message: '生成数量必须在1-100之间',
    trigger: 'blur'
  }
};

const formRef = ref();

// 生成文案（同时自动保存到数据库）
async function generateHeadlines() {
  try {
    await formRef.value?.validate();

    loading.value = true;
    const response = await fetchRequestBusinessesHeadline({
      title: formData.title,
      count: formData.count
    });

    if (response.data) {
      generatedHeadlines.value = response.data.comments || [];
      selectedHeadlines.value.clear();
      message.success(`成功生成 ${generatedHeadlines.value.length} 条文案，每条都已保存为独立记录`);

      // 清空表单，准备下次生成
      setTimeout(() => {
        formData.title = '';
        formData.count = 3;
      }, 2000); // 2秒后清空，让用户有时间看到结果
    }
  } catch (error) {
    console.error('生成文案失败:', error);
    message.error('生成文案失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 切换选中状态
function toggleSelection(index: number) {
  if (selectedHeadlines.value.has(index)) {
    selectedHeadlines.value.delete(index);
  } else {
    selectedHeadlines.value.add(index);
  }
}

// 全选/取消全选
function toggleSelectAll() {
  if (selectedHeadlines.value.size === generatedHeadlines.value.length) {
    selectedHeadlines.value.clear();
  } else {
    selectedHeadlines.value.clear();
    generatedHeadlines.value.forEach((_, index) => {
      selectedHeadlines.value.add(index);
    });
  }
}

// 清空当前结果，准备重新生成
function clearResults() {
  generatedHeadlines.value = [];
  selectedHeadlines.value.clear();
}

// 重新生成
function regenerate() {
  clearResults();
}

// 复制到剪贴板
async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text);
    message.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败');
  }
}

// 跳转到文案列表
function goToHeadlineList() {
  emit('switchToList');
}
</script>

<template>
  <div class="create-module">
    <!-- 生成表单 -->
    <NCard :bordered="false" class="form-card">
      <template #header>
        <div class="card-header">
          <div class="header-content">
            <NIcon size="28" color="#1890ff" class="header-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
            </NIcon>
            <div class="header-text">
              <h2 class="title">AI文案生成</h2>
              <p class="subtitle">智能生成高质量营销文案，提升内容创作效率</p>
            </div>
          </div>
          <div class="header-actions">
            <NButton
              type="default"
              size="large"
              @click="goToHeadlineList"
              class="list-btn"
            >
              <template #icon>
                <NIcon>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="8" y1="6" x2="21" y2="6"></line>
                    <line x1="8" y1="12" x2="21" y2="12"></line>
                    <line x1="8" y1="18" x2="21" y2="18"></line>
                    <line x1="3" y1="6" x2="3.01" y2="6"></line>
                    <line x1="3" y1="12" x2="3.01" y2="12"></line>
                    <line x1="3" y1="18" x2="3.01" y2="18"></line>
                  </svg>
                </NIcon>
              </template>
              查看文案列表
            </NButton>
          </div>
        </div>
      </template>

      <div class="form-container">
        <NForm ref="formRef" :model="formData" :rules="rules" label-placement="top" :label-width="80">
          <NGrid :cols="24" :x-gap="24">
            <NFormItemGi :span="18" label="文案主题" path="title">
              <NInput
                v-model:value="formData.title"
                placeholder="请输入文案主题，例如：新品上市、促销活动、品牌宣传等"
                :disabled="loading"
                size="large"
                clearable
              >
                <template #prefix>
                  <NIcon color="#999">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"></path>
                      <polygon points="18,2 22,6 12,16 8,16 8,12 18,2"></polygon>
                    </svg>
                  </NIcon>
                </template>
              </NInput>
            </NFormItemGi>

            <NFormItemGi :span="6" label="生成数量" path="count">
              <NInputNumber
                v-model:value="formData.count"
                :min="1"
                :max="10"
                placeholder="数量"
                :disabled="loading"
                size="large"
                style="width: 100%"
              >
                <template #prefix>
                  <NIcon color="#999">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <line x1="8" y1="6" x2="21" y2="6"></line>
                      <line x1="8" y1="12" x2="21" y2="12"></line>
                      <line x1="8" y1="18" x2="21" y2="18"></line>
                      <line x1="3" y1="6" x2="3.01" y2="6"></line>
                      <line x1="3" y1="12" x2="3.01" y2="12"></line>
                      <line x1="3" y1="18" x2="3.01" y2="18"></line>
                    </svg>
                  </NIcon>
                </template>
              </NInputNumber>
            </NFormItemGi>
          </NGrid>

          <NFormItem class="action-form-item">
            <NSpace size="large">
              <NButton
                type="primary"
                :loading="loading"
                @click="generateHeadlines"
                size="large"
                class="generate-btn"
              >
                <template #icon>
                  <NIcon>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                  </NIcon>
                </template>
                {{ loading ? '生成中...' : '生成文案' }}
              </NButton>

              <NButton
                v-if="generatedHeadlines.length > 0"
                @click="regenerate"
                :disabled="loading"
                size="large"
                secondary
              >
                <template #icon>
                  <NIcon>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="23 4 23 10 17 10"></polyline>
                      <polyline points="1 20 1 14 7 14"></polyline>
                      <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                    </svg>
                  </NIcon>
                </template>
                重新生成
              </NButton>
            </NSpace>
          </NFormItem>
        </NForm>
      </div>
    </NCard>

    <!-- 生成结果 -->
    <NCard
      :bordered="false"
      class="result-card"
    >
      <template #header>
        <div class="result-header">
          <div class="result-title">
            <NIcon size="24" :color="generatedHeadlines.length > 0 ? '#52c41a' : '#d9d9d9'" class="result-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </NIcon>
            <h3>生成结果</h3>
            <NTag
              :type="generatedHeadlines.length > 0 ? 'success' : 'default'"
              size="medium"
              round
            >
              {{ generatedHeadlines.length > 0 ? `共 ${generatedHeadlines.length} 条文案` : '暂无数据' }}
            </NTag>
          </div>
          <div class="result-actions" v-if="generatedHeadlines.length > 0">
            <NCheckbox
              :checked="selectedHeadlines.size === generatedHeadlines.length && generatedHeadlines.length > 0"
              :indeterminate="selectedHeadlines.size > 0 && selectedHeadlines.size < generatedHeadlines.length"
              @update:checked="toggleSelectAll"
              size="large"
            >
              全选 ({{ selectedHeadlines.size }}/{{ generatedHeadlines.length }})
            </NCheckbox>
          </div>
        </div>
        <NDivider style="margin: 16px 0 0 0" />
        <div class="status-info" v-if="generatedHeadlines.length > 0">
          <NTag type="success" size="small" round>
            <template #icon>
              <NIcon>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
              </NIcon>
            </template>
            每条文案已保存为独立记录
          </NTag>
        </div>
      </template>

      <!-- 有数据时显示文案列表 -->
      <div v-if="generatedHeadlines.length > 0" class="headlines-container">
        <div
          v-for="(headline, index) in generatedHeadlines"
          :key="index"
          class="headline-card"
          :class="{ 'selected': selectedHeadlines.has(index) }"
          @click="toggleSelection(index)"
        >
          <div class="headline-header">
            <div class="headline-checkbox">
              <NCheckbox
                :checked="selectedHeadlines.has(index)"
                @click.stop
                @update:checked="() => toggleSelection(index)"
                size="large"
              />
            </div>
            <div class="headline-number">
              <NTag size="small" type="primary" round>{{ index + 1 }}</NTag>
            </div>
            <div class="headline-actions">
              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton
                    size="small"
                    quaternary
                    circle
                    @click.stop="copyToClipboard(headline)"
                    class="copy-btn"
                  >
                    <template #icon>
                      <NIcon>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                        </svg>
                      </NIcon>
                    </template>
                  </NButton>
                </template>
                复制文案
              </NTooltip>
            </div>
          </div>

          <div class="headline-content">
            <p class="headline-text">{{ headline }}</p>
            <div class="headline-meta">
              <NTag size="small" type="info" round>{{ headline.length }} 字</NTag>
              <span class="word-count-label">字数统计</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 无数据时显示空状态 -->
      <div v-else class="empty-container">
        <NEmpty
          description="暂无生成结果"
          size="large"
          class="custom-empty"
        >
          <template #icon>
            <NIcon size="64" color="#d9d9d9">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
            </NIcon>
          </template>
          <template #extra>
            <div class="empty-tips">
              <p class="tip-text">请在上方输入文案主题，点击"生成文案"开始创作</p>
              <div class="tip-features">
                <div class="feature-item">
                  <NIcon color="#1890ff">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                  </NIcon>
                  <span>AI智能生成</span>
                </div>
                <div class="feature-item">
                  <NIcon color="#52c41a">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                      <polyline points="17 21 17 13 7 13 7 21"></polyline>
                      <polyline points="7 3 7 8 15 8"></polyline>
                    </svg>
                  </NIcon>
                  <span>自动保存</span>
                </div>
                <div class="feature-item">
                  <NIcon color="#fa8c16">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </NIcon>
                  <span>一键复制</span>
                </div>
              </div>
            </div>
          </template>
        </NEmpty>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.create-module {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.form-card {
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  overflow: hidden;
}

.card-header {
  padding: 24px 24px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.list-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.list-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-icon {
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.title {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.form-container {
  padding: 0 24px 24px;
}

.action-form-item {
  margin-top: 24px;
}

.generate-btn {
  min-width: 140px;
  height: 44px;
  font-weight: 500;
  border-radius: 8px;
}

.result-card {
  animation: fadeInUp 0.4s ease-out;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  overflow: hidden;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-header {
  padding: 24px 24px 16px;
}

.result-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.result-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.result-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-info {
  padding: 12px 0 0;
}

.headlines-container {
  padding: 0 24px 24px;
  display: grid;
  gap: 16px;
}

.headline-card {
  background: #fff;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.headline-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.headline-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.headline-card:hover::before {
  transform: scaleX(1);
}

.headline-card.selected {
  border-color: #1890ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
}

.headline-card.selected::before {
  transform: scaleX(1);
}

.headline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.headline-checkbox {
  display: flex;
  align-items: center;
}

.headline-number {
  flex: 1;
  display: flex;
  justify-content: center;
}

.headline-actions {
  display: flex;
  align-items: center;
}

.copy-btn {
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background-color: #1890ff;
  color: white;
}

.headline-content {
  text-align: left;
}

.headline-text {
  font-size: 16px;
  line-height: 1.6;
  color: #262626;
  margin: 0 0 12px 0;
  word-break: break-word;
  font-weight: 500;
}

.headline-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.word-count-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 空状态样式 */
.empty-container {
  padding: 60px 24px;
  text-align: center;
}

.custom-empty {
  padding: 40px 0;
}

.empty-tips {
  margin-top: 24px;
}

.tip-text {
  font-size: 16px;
  color: #666;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.tip-features {
  display: flex;
  justify-content: center;
  gap: 32px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 20px;
  transition: all 0.2s ease;
}

.feature-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .create-module {
    padding: 16px;
  }

  .form-container {
    padding: 0 16px 16px;
  }

  .headlines-container {
    padding: 0 16px 16px;
  }

  .empty-container {
    padding: 40px 16px;
  }

  .headline-card {
    padding: 16px;
  }

  .title {
    font-size: 20px;
  }

  .headline-text {
    font-size: 15px;
  }

  .tip-features {
    gap: 16px;
  }

  .feature-item {
    font-size: 13px;
    padding: 6px 12px;
  }
}
</style>
