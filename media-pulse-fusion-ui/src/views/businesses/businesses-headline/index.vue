<!-- 文件路径: src/views/businesses/businesses-headline/index.vue -->
<script setup lang="tsx">
import { ref, h } from 'vue';
import { NDivider, NTag, NButton, NTooltip, NBadge, NDropdown, NSelect } from 'naive-ui';
import {
  fetchBatchDeleteBusinessesHeadline,
  fetchGetBusinessesHeadlineList,
  fetchUpdateBusinessesHeadline
} from '@/service/api/businesses/businesses-headline';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import StatusSwitch from '@/components/custom/status-switch.vue';
import BusinessesHeadlineOperateDrawer from './modules/businesses-headline-operate-drawer.vue';
import BusinessesHeadlineSearch from './modules/businesses-headline-search.vue';
import BusinessesCreateHeadlineModule from './componets/businesses-create-headline-module.vue';
import BusinessesBindHeadlineStoreModel from './componets/businesses-bind-headline-store-model.vue';

defineOptions({
  name: 'BusinessesHeadlineIndex'
});

// 当前选择的模式：'selection' | 'create' | 'list'
const currentMode = ref<'selection' | 'create' | 'list'>('selection');

// 绑定弹窗状态
const bindModalVisible = ref(false);
const currentBindHeadlineId = ref<number | null>(null);

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

// 打开绑定弹窗
function handleOpenBindModal(headlineId: number) {
  currentBindHeadlineId.value = headlineId;
  bindModalVisible.value = true;
}

// 绑定成功回调
function handleBindSuccess() {
  bindModalVisible.value = false;
  currentBindHeadlineId.value = null;
  getData(); // 刷新数据
}

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetBusinessesHeadlineList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    title: null,
    content: null,
    status: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键ID',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'title',
      title: '文案标题',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'content',
      title: '文案内容',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      minWidth: 120,
      render: row => {
        const getStatusConfig = (status: number) => {
          switch (status) {
            case 0:
              return { text: '草稿', type: 'default', color: '#909399' };
            case 1:
              return { text: '已发布', type: 'success', color: '#67C23A' };
            case 2:
              return { text: '已下架', type: 'warning', color: '#E6A23C' };
            default:
              return { text: '未知', type: 'default', color: '#909399' };
          }
        };

        const config = getStatusConfig(row.status);
        const hasEditAuth = hasAuth('businesses:businessesHeadline:edit');

        // 状态配置
        const statusConfigs = {
          0: { text: '草稿', color: '#909399', bgColor: '#f4f4f5' },
          1: { text: '已发布', color: '#67c23a', bgColor: '#f0f9ff' },
          2: { text: '已下架', color: '#e6a23c', bgColor: '#fdf6ec' }
        };

        const currentConfig = statusConfigs[row.status] || statusConfigs[0];

        if (!hasEditAuth) {
          return (
            <span
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '4px 12px',
                borderRadius: '16px',
                fontSize: '12px',
                fontWeight: '500',
                color: currentConfig.color,
                backgroundColor: currentConfig.bgColor,
                border: `1px solid ${currentConfig.color}20`
              }}
            >
              <span
                style={{
                  width: '6px',
                  height: '6px',
                  borderRadius: '50%',
                  backgroundColor: currentConfig.color,
                  marginRight: '6px'
                }}
              />
              {currentConfig.text}
            </span>
          );
        }

        return (
          <NDropdown
            trigger="click"
            options={[
              { label: '草稿', key: 0, disabled: row.status === 0 },
              { label: '发布', key: 1, disabled: row.status === 1 },
              { label: '下架', key: 2, disabled: row.status === 2 }
            ]}
            onSelect={(key: number) => handleStatusChange(row.id!, key, () => {})}
          >
            <span
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                padding: '4px 12px',
                borderRadius: '16px',
                fontSize: '12px',
                fontWeight: '500',
                color: currentConfig.color,
                backgroundColor: currentConfig.bgColor,
                border: `1px solid ${currentConfig.color}20`,
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              class="status-selector"
            >
              <span
                style={{
                  width: '6px',
                  height: '6px',
                  borderRadius: '50%',
                  backgroundColor: currentConfig.color,
                  marginRight: '6px'
                }}
              />
              {currentConfig.text}
              <span style={{ marginLeft: '4px', fontSize: '10px' }}>▼</span>
            </span>
          </NDropdown>
        );
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 160, // 增加宽度以容纳额外按钮
      render: row => {
        const divider = () => {
          if (!hasAuth('businesses:businessesHeadline:edit') || !hasAuth('businesses:businessesHeadline:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        // 添加绑定按钮
        const bindBtn = () => {
          return (
            <ButtonIcon
              text
              type="warning"
              icon="mdi:link-variant"
              tooltipContent="绑定到店铺"
              onClick={() => handleOpenBindModal(row.id!)}
            />
          );
        };

        const editBtn = () => {
          if (!hasAuth('businesses:businessesHeadline:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('businesses:businessesHeadline:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {bindBtn()}
            {divider()}
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeleteBusinessesHeadline(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeleteBusinessesHeadline([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

// 处理状态点击
function handleStatusClick(id: number, currentStatus: number) {
  const getNextStatus = (status: number) => {
    // 状态循环：草稿(0) -> 发布(1) -> 下架(2) -> 草稿(0)
    switch (status) {
      case 0: return 1; // 草稿 -> 发布
      case 1: return 2; // 发布 -> 下架
      case 2: return 0; // 下架 -> 草稿
      default: return 1;
    }
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case 0: return '草稿';
      case 1: return '发布';
      case 2: return '下架';
      default: return '未知';
    }
  };

  const nextStatus = getNextStatus(currentStatus);
  const currentText = getStatusText(currentStatus);
  const nextText = getStatusText(nextStatus);

  window.$dialog?.warning({
    title: '修改状态',
    content: `确定要将状态从"${currentText}"修改为"${nextText}"吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      handleStatusChange(id, nextStatus, () => {});
    }
  });
}

// 处理状态变更
async function handleStatusChange(id: number, status: number, callback: (flag: boolean) => void) {
  try {
    const { error } = await fetchUpdateBusinessesHeadline({
      id,
      status
    });

    if (error) {
      window.$message?.error('状态更新失败');
      callback(false);
      return;
    }

    const statusText = status === 0 ? '草稿' : status === 1 ? '发布' : '下架';
    window.$message?.success(`状态已更新为：${statusText}`);
    callback(true);
    // 刷新数据
    getDataByPage();
  } catch (err) {
    console.error('状态更新失败:', err);
    window.$message?.error('状态更新失败');
    callback(false);
  }
}

function handleExport() {
  download('/businesses/businessesHeadline/export', searchParams, `商家文案_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="flex-col-stretch gap-16px">

    <!-- 选择界面 -->
    <div v-if="currentMode === 'selection'" class="selection-container">
      <!-- 页面标题和介绍 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <NIcon size="32" color="#1890ff" class="title-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
            </NIcon>
            智能文案管理中心
          </h1>
          <p class="page-subtitle">AI驱动的文案创作与管理平台，助力您的内容营销</p>
        </div>
        <div class="header-decoration">
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
      </div>

      <!-- 功能选择卡片 -->
      <div class="features-section">
        <h2 class="section-title">选择功能</h2>
        <div class="selection-options">
          <div class="selection-card create-card" @click="currentMode = 'create'">
            <div class="card-header">
              <div class="selection-icon">
                <NIcon size="48" color="#1890ff">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                  </svg>
                </NIcon>
              </div>
              <NBadge value="AI" type="info" class="feature-badge">
                <h3 class="selection-title">文案生成</h3>
              </NBadge>
            </div>
            <p class="selection-desc">使用AI智能生成高质量文案内容</p>
            <div class="feature-list">
              <div class="feature-item">
                <NIcon size="16" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </NIcon>
                <span>智能文案生成</span>
              </div>
              <div class="feature-item">
                <NIcon size="16" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </NIcon>
                <span>多种风格选择</span>
              </div>
              <div class="feature-item">
                <NIcon size="16" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </NIcon>
                <span>一键保存发布</span>
              </div>
            </div>
            <div class="card-action">
              <span class="action-text">立即开始创作 →</span>
            </div>
          </div>

          <div class="selection-card list-card" @click="currentMode = 'list'">
            <div class="card-header">
              <div class="selection-icon">
                <NIcon size="48" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="8" y1="6" x2="21" y2="6"></line>
                    <line x1="8" y1="12" x2="21" y2="12"></line>
                    <line x1="8" y1="18" x2="21" y2="18"></line>
                    <line x1="3" y1="6" x2="3.01" y2="6"></line>
                    <line x1="3" y1="12" x2="3.01" y2="12"></line>
                    <line x1="3" y1="18" x2="3.01" y2="18"></line>
                  </svg>
                </NIcon>
              </div>
              <h3 class="selection-title">文案列表</h3>
            </div>
            <p class="selection-desc">查看和管理已有的文案内容</p>
            <div class="feature-list">
              <div class="feature-item">
                <NIcon size="16" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </NIcon>
                <span>文案编辑管理</span>
              </div>
              <div class="feature-item">
                <NIcon size="16" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </NIcon>
                <span>状态批量操作</span>
              </div>
              <div class="feature-item">
                <NIcon size="16" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </NIcon>
                <span>数据导出功能</span>
              </div>
            </div>
            <div class="card-action">
              <span class="action-text">查看文案列表 →</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-section">
        <NCard :bordered="false" class="stats-card">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon">
                <NIcon size="24" color="#1890ff">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                  </svg>
                </NIcon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ data.length || 0 }}</div>
                <div class="stat-label">总文案数</div>
              </div>
            </div>

            <div class="stat-item">
              <div class="stat-icon">
                <NIcon size="24" color="#52c41a">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                  </svg>
                </NIcon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ data.filter(item => item.status === 1).length || 0 }}</div>
                <div class="stat-label">已发布</div>
              </div>
            </div>

            <div class="stat-item">
              <div class="stat-icon">
                <NIcon size="24" color="#faad14">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                </NIcon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ data.filter(item => item.status === 0).length || 0 }}</div>
                <div class="stat-label">草稿</div>
              </div>
            </div>

            <div class="stat-item">
              <div class="stat-icon">
                <NIcon size="24" color="#f5222d">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </NIcon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ data.filter(item => item.status === 2).length || 0 }}</div>
                <div class="stat-label">已下架</div>
              </div>
            </div>
          </div>
        </NCard>
      </div>
    </div>

    <!-- 文案生成模块 -->
    <div v-else-if="currentMode === 'create'" class="create-container">
      <div class="module-header">
        <NButton @click="currentMode = 'selection'" size="small">
          <template #icon>
            <NIcon>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
              </svg>
            </NIcon>
          </template>
          返回
        </NButton>
      </div>
      <BusinessesCreateHeadlineModule @switch-to-list="currentMode = 'list'" />
    </div>

    <!-- 文案列表模块 -->
    <div v-else-if="currentMode === 'list'" class="list-container">
      <div class="module-header">
        <NButton @click="currentMode = 'selection'" size="small">
          <template #icon>
            <NIcon>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
              </svg>
            </NIcon>
          </template>
          返回
        </NButton>
      </div>

      <BusinessesHeadlineSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
      <NCard :bordered="false" size="small" class="card-wrapper">
        <template #header-extra>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            :show-add="hasAuth('businesses:businessesHeadline:add')"
            :show-delete="hasAuth('businesses:businessesHeadline:remove')"
            :show-export="hasAuth('businesses:businessesHeadline:export')"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @export="handleExport"
            @refresh="getData"
          />
        </template>
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
        />
        <BusinessesHeadlineOperateDrawer
          v-model:visible="drawerVisible"
          :operate-type="operateType"
          :row-data="editingData"
          @submitted="getDataByPage"
        />

        <!-- 绑定店铺弹窗 -->
        <BusinessesBindHeadlineStoreModel
          v-model:visible="bindModalVisible"
          :headline-id="currentBindHeadlineId"
          @success="handleBindSuccess"
        />
      </NCard>
    </div>
  </div>
</template>

<style scoped>
.selection-container {
  padding: 24px;
  width: 100%;
  margin: 0;
  min-height: calc(100vh - 200px);
}

/* 页面头部样式 */
.page-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 48px 32px;
  margin-bottom: 32px;
  color: white;
  overflow: hidden;
  height: 180px;
}

.header-content {
  position: relative;
  z-index: 2;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: white;
}

.title-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -60px;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 20px;
  right: 100px;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: -30px;
  right: 20px;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 32px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  padding: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #fafafa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

/* 功能区域样式 */
.features-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 24px 0;
  text-align: center;
}

.selection-options {
  display: flex;
  gap: 32px;
  margin-top: 24px;
  justify-content: center;
  flex-wrap: wrap;
}


.selection-card {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 16px;
  padding: 32px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex: 1;
  min-width: 400px;
}

.selection-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.selection-card:hover {
  border-color: #1890ff;
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(24, 144, 255, 0.15);
}

.selection-card:hover::before {
  transform: scaleX(1);
}

.create-card:hover {
  border-color: #1890ff;
  box-shadow: 0 12px 32px rgba(24, 144, 255, 0.15);
}

.list-card:hover {
  border-color: #52c41a;
  box-shadow: 0 12px 32px rgba(82, 196, 26, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.selection-icon {
  margin-bottom: 0;
}

.feature-badge {
  margin-left: 12px;
}

.selection-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #262626;
}

.selection-desc {
  color: #8c8c8c;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.feature-list {
  margin-bottom: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #595959;
}

.card-action {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-text {
  color: #1890ff;
  font-weight: 600;
  font-size: 14px;
}

/* 列表和创建页面样式 */
.list-header, .create-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.list-header h2, .create-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.3s ease;
  padding: 8px 16px;
  border-radius: 8px;
  background: #f0f8ff;
}

.back-button:hover {
  color: #40a9ff;
  background: #e6f7ff;
}

.create-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
}

.form-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.generate-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.result-section {
  margin-top: 32px;
}

.result-content {
  background: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selection-container {
    padding: 16px;
  }

  .page-header {
    padding: 32px 24px;
  }

  .page-title {
    font-size: 24px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .selection-options {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .selection-card {
    padding: 24px 20px;
  }
}

/* 状态标签悬停效果 */
:deep(.status-tag-clickable) {
  transition: all 0.2s ease;
  position: relative;
}

:deep(.status-tag-clickable:hover) {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.status-tag-clickable:active) {
  transform: scale(0.98);
}

/* 状态选择器悬停效果 */
:deep(.status-selector:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
