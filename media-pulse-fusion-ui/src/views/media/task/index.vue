<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NIcon, NButton, NTooltip, NSpin, NStatistic, NProgress } from 'naive-ui';
import { useMessage } from 'naive-ui';
import TaskListView from './components/task-list-view.vue';
import TaskCreateView from './components/task-create-view.vue';
import { fetchGetTaskList } from '@/service/api/media/task';

defineOptions({
  name: 'TaskIndex'
});

const message = useMessage();

// 当前视图状态：'home' | 'list' | 'create'
const currentView = ref<'home' | 'list' | 'create'>('home');

// 加载状态
const loading = ref(false);

// 任务统计数据
const taskStats = ref({
  total: 0,
  pending: 0,
  processing: 0,
  completed: 0,
  failed: 0
});

// 最近任务列表
const recentTasks = ref<any[]>([]);

// 计算统计数据
const statsData = computed(() => [
  {
    title: '总任务数',
    value: taskStats.value.total,
    color: '#18a058',
    icon: 'material-symbols:task-alt'
  },
  {
    title: '待处理',
    value: taskStats.value.pending,
    color: '#f0a020',
    icon: 'material-symbols:pending-actions'
  },
  {
    title: '处理中',
    value: taskStats.value.processing,
    color: '#2080f0',
    icon: 'material-symbols:autorenew'
  },
  {
    title: '已完成',
    value: taskStats.value.completed,
    color: '#18a058',
    icon: 'material-symbols:check-circle'
  }
]);

// 计算完成率
const completionRate = computed(() => {
  if (taskStats.value.total === 0) return 0;
  return Math.round((taskStats.value.completed / taskStats.value.total) * 100);
});

// 获取任务统计数据
async function fetchTaskStats() {
  try {
    loading.value = true;
    const { data } = await fetchGetTaskList({ pageNum: 1, pageSize: 10 });

    if (data?.rows) {
      const tasks = data.rows;
      taskStats.value = {
        total: data.total || 0,
        pending: tasks.filter((task: any) => task.status === '0').length,
        processing: tasks.filter((task: any) => task.status === '1').length,
        completed: tasks.filter((task: any) => task.status === '2').length,
        failed: tasks.filter((task: any) => task.status === '3').length
      };

      // 获取最近的5个任务
      recentTasks.value = tasks.slice(0, 5);
    }
  } catch (error) {
    console.error('获取任务统计失败:', error);
    message.error('获取任务统计失败');
  } finally {
    loading.value = false;
  }
}

// 切换到任务列表视图
function goToTaskList() {
  currentView.value = 'list';
}

// 切换到创建任务视图
function goToCreateTask() {
  currentView.value = 'create';
}

// 返回主页
function goToHome() {
  currentView.value = 'home';
  // 返回主页时刷新统计数据
  fetchTaskStats();
}

// 格式化任务状态
function getTaskStatusText(status: string | number | null | undefined) {
  // 将值转换为字符串进行比较
  const statusValue = String(status || '');

  const statusMap: Record<string, string> = {
    '0': '待处理',
    '1': '成功',
    '2': '处理中',
    '3': '失败'
  };
  return statusMap[statusValue] || '未知';
}

// 获取任务状态颜色
function getTaskStatusColor(status: string | number | null | undefined) {
  // 将值转换为字符串进行比较
  const statusValue = String(status || '');

  const colorMap: Record<string, string> = {
    '0': '#f0a020',
    '1': '#2080f0',
    '2': '#18a058',
    '3': '#d03050'
  };
  return colorMap[statusValue] || '#909399';
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTaskStats();
});
</script>

<template>
  <div class="task-index-container">
    <!-- 主页视图 -->
    <div v-if="currentView === 'home'" class="home-view">
      <div class="page-header">
        <div class="page-title">
          一键成片
          <NTooltip trigger="hover" placement="top">
            <template #trigger>
              <NIcon size="18" class="ml-1 text-gray-400 cursor-help">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
              </NIcon>
            </template>
            <div class="tooltip-content">
              <h3 class="tooltip-title">一键成片功能说明</h3>
              <div class="tooltip-section">
                <p class="tooltip-subtitle">功能概述：</p>
                <p>一键成片是智能视频生成工具，支持自动剪辑、配音、字幕生成等功能，快速将素材转化为完整视频。</p>
              </div>
              <div class="tooltip-section">
                <p class="tooltip-subtitle">主要功能：</p>
                <ul class="tooltip-list">
                  <li>支持固定镜头和指定镜头两种模式</li>
                  <li>支持AI辅助生成视频标题</li>
                  <li>支持普通配音和克隆音色两种配音方式</li>
                  <li>支持固定字幕和关联字幕两种字幕类型</li>
                  <li>支持批量生成不同任务</li>
                </ul>
              </div>
              <div class="tooltip-section">
                <p class="tooltip-subtitle">使用流程：</p>
                <ol class="tooltip-list">
                  <li>填写基本信息（视频标题等）</li>
                  <li>选择镜头类型并上传素材</li>
                  <li>设置字幕和配音参数</li>
                  <li>提交任务等待处理</li>
                </ol>
              </div>
            </div>
          </NTooltip>
        </div>
      </div>

      <!-- 任务统计卡片 -->
      <NSpin :show="loading">
        <NGrid :cols="4" :x-gap="16" :y-gap="16" class="stats-grid">
          <NGridItem v-for="stat in statsData" :key="stat.title">
            <NCard class="stat-card" hoverable>
              <div class="stat-content">
                <div class="stat-icon" :style="{ color: stat.color }">
                  <NIcon size="32" :name="stat.icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </NIcon>
                </div>
                <div class="stat-info">
                  <div class="stat-title">{{ stat.title }}</div>
                  <div class="stat-value" :style="{ color: stat.color }">{{ stat.value }}</div>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </NSpin>

      <!-- 完成率进度条 -->
      <NCard class="progress-card" v-if="taskStats.total > 0">
        <div class="progress-header">
          <h3>任务完成率</h3>
          <span class="progress-text">{{ completionRate }}%</span>
        </div>
        <NProgress
          type="line"
          :percentage="completionRate"
          :color="completionRate >= 80 ? '#18a058' : completionRate >= 50 ? '#f0a020' : '#d03050'"
          :height="8"
          border-radius="4px"
        />
      </NCard>

      <!-- 功能卡片网格 -->
      <NGrid :cols="2" :x-gap="24" :y-gap="24" class="feature-grid">
        <!-- 创建任务卡片 -->
        <NGridItem>
          <NCard
            class="feature-card create-card"
            hoverable
            @click="goToCreateTask"
          >
            <div class="card-content">
              <div class="card-icon create-icon">
                <NIcon size="48">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="16"></line>
                    <line x1="8" y1="12" x2="16" y2="12"></line>
                  </svg>
                </NIcon>
              </div>
              <div class="card-title">创建新任务</div>
              <div class="card-description">
                快速创建一键成片任务，支持视频剪辑、配音、字幕等功能
              </div>
              <div class="card-features">
                <div class="feature-tag">智能剪辑</div>
                <div class="feature-tag">AI配音</div>
                <div class="feature-tag">自动字幕</div>
              </div>
            </div>
          </NCard>
        </NGridItem>

        <!-- 任务列表卡片 -->
        <NGridItem>
          <NCard
            class="feature-card list-card"
            hoverable
            @click="goToTaskList"
          >
            <div class="card-content">
              <div class="card-icon list-icon">
                <NIcon size="48">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M8 6h13"></path>
                    <path d="M8 12h13"></path>
                    <path d="M8 18h13"></path>
                    <path d="M3 6h.01"></path>
                    <path d="M3 12h.01"></path>
                    <path d="M3 18h.01"></path>
                  </svg>
                </NIcon>
              </div>
              <div class="card-title">任务管理</div>
              <div class="card-description">
                查看和管理所有一键成片任务，支持批量操作和状态监控
              </div>
              <div class="card-features">
                <div class="feature-tag">任务监控</div>
                <div class="feature-tag">批量操作</div>
                <div class="feature-tag">状态跟踪</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>

      <!-- 最近任务列表 -->
      <NCard class="recent-tasks-card" v-if="recentTasks.length > 0">
        <template #header>
          <div class="recent-tasks-header">
            <h3>最近任务</h3>
            <NButton text type="primary" @click="goToTaskList">
              查看全部
              <template #icon>
                <NIcon>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14"></path>
                    <path d="M12 5l7 7-7 7"></path>
                  </svg>
                </NIcon>
              </template>
            </NButton>
          </div>
        </template>
        <div class="recent-tasks-list">
          <div
            v-for="task in recentTasks"
            :key="task.taskId"
            class="recent-task-item"
          >
            <div class="task-info">
              <div class="task-name">{{ task.taskName || '未命名任务' }}</div>
              <div class="task-time">{{ task.createTime }}</div>
            </div>
            <div class="task-status">
              <span
                class="status-badge"
                :style="{ backgroundColor: getTaskStatusColor(task.status) }"
              >
                {{ getTaskStatusText(task.status) }}
              </span>
            </div>
          </div>
        </div>
      </NCard>
    </div>

    <!-- 任务列表视图 -->
    <div v-else-if="currentView === 'list'" class="list-view">
      <div class="view-header">
        <NButton
          type="tertiary"
          @click="goToHome"
          class="back-button"
        >
          <template #icon>
            <NIcon>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 12H5"></path>
                <path d="M12 19l-7-7 7-7"></path>
              </svg>
            </NIcon>
          </template>
          返回主页
        </NButton>
        <h2 class="view-title">任务管理</h2>
      </div>
      <TaskListView />
    </div>

    <!-- 创建任务视图 -->
    <div v-else-if="currentView === 'create'" class="create-view">
      <div class="view-header">
        <NButton
          type="tertiary"
          @click="goToHome"
          class="back-button"
        >
          <template #icon>
            <NIcon>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 12H5"></path>
                <path d="M12 19l-7-7 7-7"></path>
              </svg>
            </NIcon>
          </template>
          返回主页
        </NButton>
        <h2 class="view-title">创建新任务</h2>
      </div>
      <TaskCreateView @task-created="goToTaskList" />
    </div>
  </div>
</template>

<style scoped>
.task-index-container {
  min-height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.home-view {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  display: flex;
  align-items: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ml-1 {
  margin-left: 8px;
}

.text-gray-400 {
  color: #9ca3af;
}

.cursor-help {
  cursor: help;
}

.tooltip-content {
  max-width: 400px;
  padding: 8px 0;
}

.tooltip-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: #1890ff;
}

.tooltip-section {
  margin-bottom: 12px;
}

.tooltip-subtitle {
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.tooltip-list {
  margin: 0;
  padding-left: 16px;
}

.tooltip-list li {
  margin-bottom: 4px;
}

/* 统计卡片样式 */
.stats-grid {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
}

/* 进度卡片样式 */
.progress-card {
  margin-bottom: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.progress-text {
  font-size: 18px;
  font-weight: 700;
  color: #18a058;
}

/* 功能卡片样式 */
.feature-grid {
  max-width: 800px;
  margin: 0 auto 24px auto;
}

.feature-card {
  height: 300px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.feature-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: all 0.6s ease;
  transform: scale(0);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-card:hover::after {
  opacity: 1;
  transform: scale(1);
}

.feature-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.2),
    0 12px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.create-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.create-card::before {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
}

.create-card:hover {
  box-shadow:
    0 25px 50px rgba(102, 126, 234, 0.3),
    0 12px 24px rgba(118, 75, 162, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.list-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  position: relative;
}

.list-card::before {
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.2) 0%, rgba(245, 87, 108, 0.2) 100%);
}

.list-card:hover {
  box-shadow:
    0 25px 50px rgba(240, 147, 251, 0.3),
    0 12px 24px rgba(245, 87, 108, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 添加微妙的动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

.feature-card:hover {
  animation: float 3s ease-in-out infinite;
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px 24px;
  position: relative;
  z-index: 2;
}

.card-icon {
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.feature-card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
  background: rgba(255, 255, 255, 0.35);
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.create-icon {
  color: #fff;
  font-size: 24px;
}

.list-icon {
  color: #fff;
  font-size: 24px;
}

.card-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
  transition: all 0.3s ease;
}

.feature-card:hover .card-title {
  transform: translateY(-2px);
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.card-description {
  font-size: 15px;
  line-height: 1.7;
  margin-bottom: 24px;
  opacity: 0.95;
  max-width: 280px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.feature-card:hover .card-description {
  opacity: 1;
  transform: translateY(-1px);
}

.card-features {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
  transition: all 0.3s ease;
}

.feature-card:hover .card-features {
  transform: translateY(-2px);
}

.feature-tag {
  padding: 6px 14px;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.feature-tag:hover {
  background: rgba(255, 255, 255, 0.35);
  transform: translateY(-1px) scale(1.05);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 最近任务样式 */
.recent-tasks-card {
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.recent-tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recent-tasks-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.recent-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recent-task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.recent-task-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(4px);
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.task-time {
  font-size: 12px;
  color: #666;
}

.task-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  text-align: center;
  min-width: 60px;
}

/* 视图头部样式 */
.view-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.back-button {
  border-radius: 8px;
}

.view-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.list-view,
.create-view {
  max-width: 1400px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stats-grid :deep(.n-grid) {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .task-index-container {
    padding: 16px;
  }

  .page-header {
    margin-bottom: 24px;
  }

  .page-title {
    font-size: 24px;
  }

  .stats-grid :deep(.n-grid) {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 12px !important;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-content {
    gap: 8px;
    padding: 4px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-value {
    font-size: 20px;
  }

  .feature-grid {
    max-width: 100%;
  }

  .feature-card {
    height: 260px;
  }

  .card-content {
    padding: 24px 18px;
  }

  .card-title {
    font-size: 20px;
  }

  .card-description {
    font-size: 14px;
  }

  .card-icon {
    margin-bottom: 18px;
    padding: 12px;
  }

  .feature-tag {
    padding: 5px 12px;
    font-size: 11px;
  }

  .recent-task-item {
    padding: 12px;
  }

  .task-name {
    font-size: 13px;
  }

  .task-time {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .stats-grid :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }

  .feature-grid :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }

  .page-title {
    font-size: 20px;
  }

  .feature-card {
    height: 240px;
  }

  .card-content {
    padding: 20px 16px;
  }

  .card-title {
    font-size: 18px;
  }

  .card-description {
    font-size: 13px;
    max-width: 220px;
  }

  .card-icon {
    padding: 10px;
    margin-bottom: 16px;
  }

  .feature-tag {
    padding: 4px 10px;
    font-size: 10px;
  }

  .recent-tasks-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .recent-task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .task-status {
    align-self: flex-end;
  }
}
</style>
