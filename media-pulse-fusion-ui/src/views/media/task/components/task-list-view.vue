<script setup lang="tsx">
import { h } from 'vue';
import { NButton, NCard, NDataTable, NDivider, NSpace, NTag, NTooltip } from 'naive-ui';
import { fetchBatchDeleteTask, fetchGetTaskList } from '@/service/api/media/task';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import TaskOperateDrawer from '../modules/task-operate-drawer.vue';
import TaskSearch from '../modules/task-search.vue';
import FilePreviewButton from '../modules/file-preview-button.vue';

defineOptions({
  name: 'TaskListView'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

// 格式化配音类型
function formatVoiceType(value: string): any {
  if (value === '1') {
    return h(NTag, { type: 'success', size: 'small' }, { default: () => '普通配音' });
  } else if (value === '2') {
    return h(NTag, { type: 'info', size: 'small' }, { default: () => '克隆音色' });
  }
  return value || '-';
}

// 格式化镜头类型
function formatShotType(value: string): any {
  if (value === '1') {
    return h(NTag, { type: 'warning', size: 'small' }, { default: () => '固定镜头' });
  } else if (value === '2') {
    return h(NTag, { type: 'error', size: 'small' }, { default: () => '指定镜头' });
  }
  return value || '-';
}

// 格式化任务状态
function formatTaskStatus(value: string | number | null | undefined): any {
  // 将值转换为字符串进行比较
  const statusValue = String(value || '');

  if (statusValue === '0') {
    return h(NTag, { type: 'default', size: 'small' }, { default: () => '待处理' });
  } else if (statusValue === '1') {
    return h(NTag, { type: 'info', size: 'small' }, { default: () => '处理中' });
  } else if (statusValue === '2') {
    return h(NTag, { type: 'success', size: 'small' }, { default: () => '已完成' });
  } else if (statusValue === '3') {
    return h(NTag, { type: 'error', size: 'small' }, { default: () => '失败' });
  }

  // 如果状态值为空或未知，显示未知状态
  if (!value && value !== 0) {
    return h(NTag, { type: 'warning', size: 'small' }, { default: () => '未知' });
  }

  return statusValue || '-';
}

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetTaskList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: null,
    videoTitle: null,
    fixedSubtitle: null,
    relatedSubtitle: null,
    voiceType: null,
    voicePreset: null,
    cloneVoiceFile: null,
    cloneVoiceReferenceText: null,
    cloneVoiceTargetText: null,
    videoMaterials: null,
    clipCount: null,
    clipDuration: null,
    backgroundMusic: null,
    shotType: null,
    status: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'taskName',
      title: '任务名称',
      align: 'center',
      minWidth: 120,
      render: row => {
        return h(
          NTooltip,
          { trigger: 'hover' },
          {
            trigger: () => h('span', { style: 'cursor: default;' }, row.taskName || '-'),
            default: () => row.taskName || '-'
          }
        );
      }
    },
    {
      key: 'videoTitle',
      title: '视频标题',
      align: 'center',
      minWidth: 120,
      render: row => {
        return h(
          NTooltip,
          { trigger: 'hover' },
          {
            trigger: () => h('span', { style: 'cursor: default;' }, row.videoTitle || '-'),
            default: () => row.videoTitle || '-'
          }
        );
      }
    },
    {
      key: 'shotType',
      title: '镜头类型',
      align: 'center',
      minWidth: 100,
      render: row => formatShotType(row.shotType)
    },
    {
      key: 'subtitleInfo',
      title: '字幕信息',
      align: 'center',
      minWidth: 150,
      render: row => {
        if (row.fixedSubtitle) {
          return h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () => h(NTag, { type: 'warning', size: 'small' }, { default: () => '固定字幕' }),
              default: () => row.fixedSubtitle
            }
          );
        } else if (row.relatedSubtitle) {
          return h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () => h(NTag, { type: 'success', size: 'small' }, { default: () => '关联字幕' }),
              default: () => row.relatedSubtitle
            }
          );
        }
        return '-';
      }
    },
    {
      key: 'voiceType',
      title: '配音类型',
      align: 'center',
      minWidth: 100,
      render: row => formatVoiceType(row.voiceType)
    },
    {
      key: 'voiceInfo',
      title: '音色信息',
      align: 'center',
      minWidth: 150,
      render: row => {
        if (row.voiceType === '1' && row.voicePreset) {
          return h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () => h('span', { style: 'cursor: default;' }, row.voicePreset || '-'),
              default: () => `预设音色: ${row.voicePreset}`
            }
          );
        } else if (row.voiceType === '2') {
          return h(
            NSpace,
            { size: 'small', vertical: true },
            {
              default: () =>
                [
                  h(FilePreviewButton, {
                    files: row.cloneVoiceFile,
                    type: 'audio'
                  }),
                  row.cloneVoiceReferenceText
                    ? h(
                        NTooltip,
                        { trigger: 'hover' },
                        {
                          trigger: () => h(NTag, { type: 'default', size: 'small' }, { default: () => '参考文本' }),
                          default: () => row.cloneVoiceReferenceText
                        }
                      )
                    : null,
                  row.cloneVoiceTargetText
                    ? h(
                        NTooltip,
                        { trigger: 'hover' },
                        {
                          trigger: () => h(NTag, { type: 'default', size: 'small' }, { default: () => '目标文本' }),
                          default: () => row.cloneVoiceTargetText
                        }
                      )
                    : null
                ].filter(Boolean)
            }
          );
        }
        return '-';
      }
    },
    {
      key: 'videoMaterials',
      title: '视频素材',
      align: 'center',
      minWidth: 120,
      render: row => {
        return h(FilePreviewButton, {
          files: row.videoMaterials,
          type: 'video'
        });
      }
    },
    {
      key: 'clipInfo',
      title: '截取信息',
      align: 'center',
      minWidth: 120,
      render: row => {
        return h(
          NSpace,
          { size: 'small', vertical: true },
          {
            default: () => [
              h(NTag, { type: 'default', size: 'small' }, { default: () => `数量: ${row.clipCount || '-'}` }),
              h(NTag, { type: 'default', size: 'small' }, { default: () => `时长: ${row.clipDuration || '-'}` })
            ]
          }
        );
      }
    },
    {
      key: 'backgroundMusic',
      title: '背景音乐',
      align: 'center',
      minWidth: 120,
      render: row => {
        return h(FilePreviewButton, {
          files: row.backgroundMusic,
          type: 'audio'
        });
      }
    },
    {
      key: 'status',
      title: '任务状态',
      align: 'center',
      minWidth: 100,
      render: row => formatTaskStatus(row.status)
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('media:task:edit') || !hasAuth('media:task:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('media:task:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('media:task:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await fetchBatchDeleteTask(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  const { error } = await fetchBatchDeleteTask([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/media/task/export', searchParams, `media_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="task-list-view">
    <NCard title="媒体任务列表" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <div class="header-operations">
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            :show-add="hasAuth('media:task:add')"
            :show-delete="hasAuth('media:task:remove')"
            :show-export="hasAuth('media:task:export')"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @export="handleExport"
            @refresh="getData"
          />
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-section">
        <TaskSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
        />
      </div>

      <TaskOperateDrawer
        :visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @update:visible="drawerVisible = $event"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped>
.task-list-view {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 120px);
  padding: 0;
}

.card-wrapper {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.header-operations {
  transform: scale(0.95);
  transform-origin: right center;
}

.search-section {
  padding: 0;
  margin-bottom: 16px;
}

.table-section {
  width: 100%;
}

.table-section {
  flex: 1;
  overflow: hidden;
  min-height: 400px;
}

:deep(.n-card) {
  border-radius: 16px;
  background: transparent;
  border: none;
  box-shadow: none;
}

:deep(.n-card__content) {
  padding: 20px 24px;
}

:deep(.n-card-header) {
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

:deep(.n-card-header .n-card-header__main) {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

:deep(.n-card__content) {
  padding: 20px 24px;
}

:deep(.n-data-table) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.n-data-table-thead) {
  background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
}

:deep(.n-data-table-th) {
  background: transparent;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 2px solid #e2e8f0;
  padding: 12px 16px !important;
  font-size: 14px;
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
  padding: 10px 16px !important;
  font-size: 14px;
}

:deep(.n-data-table-tr:hover .n-data-table-td) {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

:deep(.n-data-table-tr--checked .n-data-table-td) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.n-button--primary-type) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

:deep(.n-button--primary-type:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* 分页器样式优化 */
:deep(.n-pagination) {
  margin-top: 16px;
  justify-content: center;
}

:deep(.n-pagination .n-pagination-item) {
  min-width: 32px;
  height: 32px;
  font-size: 14px;
}

:deep(.n-pagination .n-pagination-quick-jumper) {
  font-size: 14px;
}

:deep(.n-pagination .n-pagination-size-picker) {
  font-size: 14px;
}

:deep(.n-pagination) {
  justify-content: center;
  margin-top: 16px;
}

:deep(.n-pagination-item) {
  border-radius: 8px;
  transition: all 0.2s ease;
}

:deep(.n-pagination-item--active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
}

@media (max-width: 768px) {
  .task-list-view {
    gap: 16px;
    padding: 0 16px;
  }

  :deep(.n-card-header) {
    padding: 20px 16px 0 16px;
  }

  :deep(.n-card__content) {
    padding: 16px;
  }

  :deep(.n-card-header .n-card-header__main) {
    font-size: 18px;
  }
}
</style>
