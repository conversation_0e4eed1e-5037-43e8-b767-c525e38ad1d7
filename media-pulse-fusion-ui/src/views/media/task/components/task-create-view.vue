<script setup lang="ts">
import { ref } from 'vue';
import { NCard, NButton, NIcon, NGrid, NGridItem } from 'naive-ui';
import TaskOperateDrawer from '../modules/task-operate-drawer.vue';

defineOptions({
  name: 'TaskCreateView'
});

interface Emits {
  (e: 'task-created'): void;
}

const emit = defineEmits<Emits>();

// 抽屉状态
const drawerVisible = ref(false);
const operateType = ref<'add' | 'edit'>('add');

// 功能特性列表
const features = [
  { name: '智能剪辑', description: '自动识别精彩片段，智能剪辑视频' },
  { name: 'AI配音', description: '多种音色选择，支持克隆音色技术' },
  { name: '字幕生成', description: '自动语音识别，精准字幕同步' },
  { name: '背景音乐', description: '智能匹配背景音乐，提升视频质量' },
  { name: '镜头切换', description: '支持固定镜头和指定镜头模式' },
  { name: '批量处理', description: '支持批量创建任务，提高效率' }
];

// 打开创建抽屉
function openCreateDrawer() {
  operateType.value = 'add';
  drawerVisible.value = true;
}

// 任务创建成功回调
function handleTaskSubmitted() {
  drawerVisible.value = false;
  emit('task-created');
}
</script>

<template>
  <div class="task-create-view">
    <!-- 创建任务主卡片 -->
    <NCard class="create-main-card">
      <div class="create-content">
        <div class="create-header">
          <div class="create-icon">
            <NIcon size="64">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M15 4V2a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v2"></path>
                <path d="M15 18v2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-2"></path>
                <path d="M8 9h8"></path>
                <path d="M8 15h8"></path>
                <path d="M12 4v16"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </NIcon>
          </div>
          <h2 class="create-title">创建一键成片任务</h2>
          <p class="create-description">
            智能视频制作平台，支持自动剪辑、AI配音、字幕生成等功能，让视频创作变得简单高效
          </p>
          <NButton
            type="primary"
            size="large"
            @click="openCreateDrawer"
            class="create-button"
          >
            <template #icon>
              <NIcon>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
              </NIcon>
            </template>
            开始创建任务
          </NButton>
        </div>

        <!-- 功能特性展示 -->
        <div class="features-section">
          <h3 class="features-title">平台功能特性</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16" class="features-grid">
            <NGridItem v-for="feature in features" :key="feature.name">
              <div class="feature-item">
                <div class="feature-name">{{ feature.name }}</div>
                <div class="feature-desc">{{ feature.description }}</div>
              </div>
            </NGridItem>
          </NGrid>
        </div>
      </div>
    </NCard>

    <!-- 使用指南 -->
    <NCard title="使用指南" class="guide-card">
      <div class="guide-content">
        <div class="guide-step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>选择创建方式</h4>
            <p>选择快速创建模板或自定义创建任务</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>配置任务参数</h4>
            <p>设置视频标题、素材、配音、字幕等参数</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>提交任务</h4>
            <p>确认配置后提交任务，系统将自动处理</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>查看结果</h4>
            <p>在任务列表中查看处理进度和结果</p>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 任务创建抽屉 -->
    <TaskOperateDrawer
      :visible="drawerVisible"
      :operate-type="operateType"
      @update:visible="drawerVisible = $event"
      @submitted="handleTaskSubmitted"
    />
  </div>
</template>

<style scoped>
.task-create-view {
  padding: 0;
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.create-main-card {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.create-content {
  text-align: center;
  padding: 48px 32px;
}

.create-header {
  margin-bottom: 48px;
}

.create-icon {
  color: #667eea;
  margin-bottom: 24px;
  filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.2));
}

.create-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 20px 0;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.create-description {
  font-size: 18px;
  color: #4a5568;
  margin: 0 0 40px 0;
  line-height: 1.7;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
}

.create-button {
  font-size: 18px;
  font-weight: 600;
  padding: 16px 48px;
  height: auto;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.create-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.create-button:hover::before {
  left: 100%;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

.features-section {
  text-align: left;
  margin-top: 24px;
}

.features-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 32px 0;
  color: #2d3748;
  text-align: center;
  position: relative;
}

.features-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

.features-grid {
  margin-top: 24px;
}

.feature-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.feature-item:hover::before {
  transform: scaleY(1);
}

.feature-item:hover {
  background: linear-gradient(135deg, #ffffff 0%, #edf2f7 100%);
  border-color: #cbd5e0;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.feature-name {
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 18px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.feature-desc {
  color: #4a5568;
  font-size: 15px;
  line-height: 1.6;
  font-weight: 400;
}

.guide-card {
  border-radius: 16px;
  margin-top: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.guide-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
  margin-top: 24px;
  padding: 32px;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.guide-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.guide-step:hover::before {
  transform: scaleY(1);
}

.guide-step:hover {
  background: linear-gradient(135deg, #ffffff 0%, #edf2f7 100%);
  border-color: #cbd5e0;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.step-content h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.step-content p {
  margin: 0;
  font-size: 15px;
  color: #4a5568;
  line-height: 1.6;
  font-weight: 400;
}

@media (max-width: 768px) {
  .task-create-view {
    padding: 16px;
    gap: 24px;
  }

  .create-content {
    padding: 32px 24px;
  }

  .create-title {
    font-size: 28px;
  }

  .create-description {
    font-size: 16px;
  }

  .create-button {
    font-size: 16px;
    padding: 14px 36px;
  }

  .features-title,
  .guide-title {
    font-size: 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .feature-item {
    padding: 20px;
  }

  .feature-name {
    font-size: 16px;
  }

  .feature-desc {
    font-size: 14px;
  }

  .guide-content {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 24px;
  }

  .guide-step {
    padding: 20px;
    gap: 16px;
  }

  .step-number {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .step-content h4 {
    font-size: 16px;
  }

  .step-content p {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .create-content {
    padding: 24px 16px;
  }

  .create-title {
    font-size: 24px;
  }

  .create-description {
    font-size: 15px;
  }

  .create-button {
    font-size: 15px;
    padding: 12px 28px;
  }

  .feature-item,
  .guide-step {
    padding: 16px;
  }

  .guide-content {
    padding: 20px 16px;
  }
}
</style>
