<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { NButton, NColorPicker, NInput, NInputNumber, NPopover, NSelect, NSlider, NSpin, useMessage } from 'naive-ui';
// @ts-ignore - This module exists but TypeScript can't find its type declaration
import { fetchSubtitlePreview, fetchVideoInfo } from '@/service/api/media/intercept';
import {
  COMMON_FONTS,
  FONT_SIZE_PRESETS,
  type VideoInfo,
  calculateRecommendedPreviewSize,
  createSubtitleConverter,
  createSubtitleConverterWithVideoInfo
} from '@/utils/subtitle-coordinate-converter';

defineOptions({
  name: 'SubtitlePositionSelectorVisual'
});

interface Props {
  /** 视频URL */
  videoUrl?: string;
  /** 字幕位置 */
  modelValue?: string;
  /** 字幕文本 */
  subtitleText?: string;
  /** 任务ID */
  taskId?: string | number;
  /** 任务名称 */
  taskName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  videoUrl: '',
  modelValue: '',
  subtitleText: '预览字幕',
  taskId: undefined,
  taskName: ''
});

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

const emit = defineEmits<Emits>();

const message = useMessage();

// 默认字体选项 - 包含系统字体和自定义字体
const defaultFontOptions = [
  // 自定义字体
  { label: '素材集市康康体', value: 'SuCaiJiShiKangKangTi' },
  { label: '平方赖江湖琅琊体', value: 'PingFangLaiJiangHuLangTi' },
  { label: '平方赖江湖飞扬体', value: 'PingFangLaiJiangHuFeiYangTi' },
  { label: '荆南麦圆体', value: 'JingNanMaiYuanTi' },
  { label: '阿里妈妈方圆体', value: 'AlimamaFangYuanTi' }
];

// 预览相关状态
const previewContainer = ref<HTMLElement>();
const previewImage = ref<string>('');
const previewLoading = ref(false);

// 视频信息相关状态
const videoInfo = ref<VideoInfo | null>(null);
const videoInfoLoading = ref(false);

// 拖拽相关状态
const isDragging = ref(false);
const subtitlePosition = ref({ x: 50, y: 80 }); // 百分比位置，默认中下位置

// 颜色选择器相关状态
const showColorPicker = ref(false);
const colorPickerRef = ref(null);

// 预设颜色
const presetColors = [
  '#FFFFFF', // 白色
  '#000000', // 黑色
  '#FF0000', // 红色
  '#00FF00', // 绿色
  '#0000FF', // 蓝色
  '#FFFF00', // 黄色
  '#FF00FF', // 紫色
  '#00FFFF', // 青色
  '#FFA500', // 橙色
  '#800080', // 紫色
  '#008000', // 深绿
  '#800000', // 深红
];

// 字幕样式
const subtitleStyle = ref({
  fontName: '素材集市康康体', // 默认使用第一个字体
  fontSize: 18, // 改为更小的默认字体大小
  fontStyle: 'normal', // 字体样式：normal, bold, italic
  fontWeight: 'normal', // 字体粗细
  fontColor: '#FFFFFF', // 字体颜色（十六进制）
  r: 255,
  g: 255,
  b: 255,
  text: props.subtitleText || '预览字幕',
  lines: (props.subtitleText || '预览字幕').split('\n') // 添加 lines 字段
  // 可能需要的其他字段
  // alignment: 'center', // 对齐方式
  // backgroundColor: 'transparent', // 背景颜色
  // borderColor: '#000000', // 边框颜色
  // borderWidth: 0 // 边框宽度
});

// 计算字幕颜色
const subtitleColor = computed(() => {
  const { r, g, b } = subtitleStyle.value;
  return `rgb(${r}, ${g}, ${b})`;
});

// 颜色选择器方法
const updateColor = (color: string) => {
  const rgb = color.match(/\d+/g);
  if (rgb) {
    subtitleStyle.value.r = parseInt(rgb[0]);
    subtitleStyle.value.g = parseInt(rgb[1]);
    subtitleStyle.value.b = parseInt(rgb[2]);
  }
};

const selectPresetColor = (color: string) => {
  // 将十六进制颜色转换为RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  subtitleStyle.value.r = r;
  subtitleStyle.value.g = g;
  subtitleStyle.value.b = b;

  showColorPicker.value = false;
};

// 计算预览容器样式
const previewContainerStyle = computed(() => {
  if (videoInfo.value) {
    const recommendedSize = calculateRecommendedPreviewSize(videoInfo.value, 640, 480);
    return {
      width: `${recommendedSize.width}px`,
      height: `${recommendedSize.height}px`,
      aspectRatio: videoInfo.value.aspectRatio.toString()
    };
  }
  return {
    width: '640px',
    height: '360px',
    aspectRatio: '16/9'
  };
});

// 获取实际的视频URL（确保是字符串）
const actualVideoUrl = computed(() => {
  if (!props.videoUrl) return '';

  let videoUrl: any = props.videoUrl;

  // 如果是字符串，尝试解析JSON
  if (typeof videoUrl === 'string') {
    try {
      if (videoUrl.startsWith('[')) {
        const parsed = JSON.parse(videoUrl);
        if (Array.isArray(parsed)) {
          // 如果是二维数组，取第一个数组的第一个元素
          if (parsed.length > 0 && Array.isArray(parsed[0])) {
            videoUrl = parsed[0].length > 0 ? parsed[0][0] : '';
          } else {
            // 如果是一维数组，取第一个元素
            videoUrl = parsed.length > 0 ? parsed[0] : '';
          }
        }
      }
      // 如果不是JSON格式，直接使用字符串
    } catch (e) {
      // JSON解析失败，直接使用原字符串
      // console.log('视频URL不是JSON格式，直接使用:', videoUrl);
    }
  } else if (Array.isArray(videoUrl)) {
    // 如果是数组，处理二维数组的情况
    if (videoUrl.length > 0) {
      if (Array.isArray(videoUrl[0])) {
        // 二维数组，取第一个数组的第一个元素
        videoUrl = videoUrl[0].length > 0 ? videoUrl[0][0] : '';
      } else {
        // 一维数组，取第一个元素
        videoUrl = videoUrl[0];
      }
    } else {
      videoUrl = '';
    }
  }

  // console.log('解析后的视频URL:', videoUrl);
  return String(videoUrl || '');
});

// 监听视频URL变化，自动获取视频信息和生成预览
watch(actualVideoUrl, newUrl => {
  if (newUrl) {
    // 先获取视频信息，再生成预览
    getVideoInfoAndPreview();
  }
});

// 监听字幕文本变化
watch(
  () => props.subtitleText,
  newText => {
    if (newText) {
      subtitleStyle.value.text = newText;
      // 处理换行，生成 lines 数组
      subtitleStyle.value.lines = newText.split('\n');
    }
  }
);

// 响应式预览容器尺寸
const previewContainerSize = computed(() => {
  if (!previewContainer.value) {
    // 如果有视频信息，使用推荐尺寸；否则使用默认尺寸
    if (videoInfo.value) {
      return calculateRecommendedPreviewSize(videoInfo.value, 640, 480);
    }
    return { width: 640, height: 360 }; // 默认尺寸
  }
  const rect = previewContainer.value.getBoundingClientRect();
  return {
    width: rect.width || 640,
    height: rect.height || 360
  };
});

// 创建坐标转换器
const subtitleConverter = computed(() => {
  const containerSize = previewContainerSize.value;

  // 如果有视频信息，使用带视频信息的转换器
  if (videoInfo.value) {
    return createSubtitleConverterWithVideoInfo(containerSize.width, containerSize.height, videoInfo.value);
  }

  // 否则使用默认转换器
  return createSubtitleConverter(containerSize.width, containerSize.height);
});

// 计算实时字体大小
const computedFontSize = computed(() => {
  const converter = subtitleConverter.value;
  const frontendSize = converter.getFontSizeForFrontend(subtitleStyle.value.fontSize);
  // console.log(`字体大小转换: ${subtitleStyle.value.fontSize}pt -> ${frontendSize}px (缩放比例: ${JSON.stringify(converter.getScaleRatio())})`);
  return frontendSize;
});

// 监听容器尺寸变化，重新计算缩放比例
watch(
  previewContainerSize,
  () => {
    // console.log('预览容器尺寸变化:', newSize);
    // console.log('转换器调试信息:', subtitleConverter.value.getDebugInfo());
  },
  { deep: true }
);

// 监听字幕位置和样式变化，发送完整信息给父组件
watch(
  [subtitlePosition, subtitleStyle],
  () => {
    // 使用转换器计算像素坐标
    const converter = subtitleConverter.value;
    const pixelCoords = converter.percentToPixel(subtitlePosition.value.x, subtitlePosition.value.y);
    const backendFontSize = converter.getFontSizeForBackend(subtitleStyle.value.fontSize);

    // 构建与后端预期一致的字幕位置信息格式
    const positionInfo = {
      subtitleStyle: {
        fontName: subtitleStyle.value.fontName,
        // fontSize: backendFontSize, // 使用后端字体大小
        fontSize: subtitleStyle.value.fontSize,
        fontStyle: subtitleStyle.value.fontStyle,
        r: subtitleStyle.value.r,
        g: subtitleStyle.value.g,
        b: subtitleStyle.value.b,
        text: subtitleStyle.value.text,
        lines: subtitleStyle.value.lines,
        hexadecimal:
          `#${subtitleStyle.value.r.toString(16).padStart(2, '0')}${subtitleStyle.value.g.toString(16).padStart(2, '0')}${subtitleStyle.value.b.toString(16).padStart(2, '0')}`.toUpperCase()
      },
      x: pixelCoords.x, // 使用像素坐标
      y: pixelCoords.y
    };

    // 发送JSON字符串给父组件
    const jsonString = JSON.stringify(positionInfo);
    console.log('字幕位置信息发送给父组件:', jsonString);
    emit('update:modelValue', jsonString);
  },
  { deep: true }
);

// 获取视频信息
async function getVideoInfoFromUrl() {
  const videoUrl = actualVideoUrl.value;
  if (!videoUrl) {
    return null;
  }

  videoInfoLoading.value = true;
  try {
    const response = await fetchVideoInfo(videoUrl);
    const info = response.data || response;

    videoInfo.value = {
      width: info.width,
      height: info.height,
      aspectRatio: info.aspectRatio,
      orientation: info.orientation
    };

    console.log('获取视频信息成功:', videoInfo.value);
    return videoInfo.value;
  } catch (error) {
    console.error('获取视频信息失败:', error);
    message.warning('获取视频信息失败，将使用默认设置');
    return null;
  } finally {
    videoInfoLoading.value = false;
  }
}

// 获取视频信息并生成预览
async function getVideoInfoAndPreview() {
  await getVideoInfoFromUrl();
  // 延迟一下再生成预览，确保视频信息已经更新
  setTimeout(() => {
    getPreview();
  }, 500);
}

// 获取预览图片
async function getPreview() {
  const videoUrl = actualVideoUrl.value;
  if (!videoUrl) {
    message.warning('请先上传视频');
    return;
  }

  previewLoading.value = true;
  try {
    // 使用转换工具计算坐标和字体大小
    const converter = subtitleConverter.value;
    const pixelCoords = converter.percentToPixel(subtitlePosition.value.x, subtitlePosition.value.y);
    // const backendFontSize = converter.getFontSizeForBackend(subtitleStyle.value.fontSize);
    const backendFontSize = subtitleStyle.value.fontSize;

    // 调试信息
    // console.log('=== 字幕预览调试信息 ===');
    // console.log('前端百分比坐标:', subtitlePosition.value);
    // console.log('转换后像素坐标:', pixelCoords);
    // console.log('前端字体大小:', subtitleStyle.value.fontSize, 'pt');
    // console.log('后端字体大小:', backendFontSize, 'pt');
    // console.log('容器尺寸:', previewContainerSize.value);
    // console.log('转换器调试信息:', converter.getDebugInfo());

    // 准备完整的请求参数（包含数据库插入所需字段）
    const requestData = {
      videoUrl,
      x: pixelCoords.x,
      y: pixelCoords.y,
      subtitleStyle: {
        fontName: subtitleStyle.value.fontName,
        fontSize: backendFontSize, // 使用转换后的字体大小
        fontStyle: subtitleStyle.value.fontStyle,
        fontWeight: subtitleStyle.value.fontWeight,
        fontColor: subtitleStyle.value.fontColor,
        r: subtitleStyle.value.r,
        g: subtitleStyle.value.g,
        b: subtitleStyle.value.b,
        text: subtitleStyle.value.text,
        lines: subtitleStyle.value.lines
        // alignment: subtitleStyle.value.alignment,
        // backgroundColor: subtitleStyle.value.backgroundColor,
        // borderColor: subtitleStyle.value.borderColor,
        // borderWidth: subtitleStyle.value.borderWidth
      },
      // 任务相关字段
      taskId: props.taskId || null,
      taskName: props.taskName || '',
      // 时间字段
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      // 其他可能需要的字段
      userId: null, // 如果需要用户ID，可以从store获取
      status: 'preview', // 标识这是预览请求
      subtitlePosition: `${Math.round(subtitlePosition.value.x)},${Math.round(subtitlePosition.value.y)}` // 位置字符串
    };

    // console.log('正在获取字幕预览，完整请求参数:', requestData);

    const response = await fetchSubtitlePreview(requestData);
    // console.log('获取字幕预览响应:', response);
    // console.log('响应结构详情:', {
    //   'response.data': response.data,
    //   'response.response': response.response,
    //   'response.response.data': response.response?.data,
    //   'response.response.data.data': response.response?.data?.data,
    //   'response.response.data.code': response.response?.data?.code,
    //   'response.response.data.msg': response.response?.data?.msg
    // });

    // 处理响应数据 - 根据后端 R<List<String>> 格式
    let imageData = null;

    // 后端返回格式: R.ok(base64List)
    // 对应的响应结构: { code: 200, data: [...], msg: "success" }
    if (response.response && response.response.data) {
      const responseData = response.response.data;
      // console.log('后端响应数据详情:', responseData);

      // 检查是否是标准的R格式
      if (responseData.code === 200 && responseData.data) {
        imageData = responseData.data;
        // console.log('从 responseData.data 获取图片数据:', imageData);
      } else if (Array.isArray(responseData)) {
        // 直接是数组格式
        imageData = responseData;
        // console.log('直接数组格式图片数据:', imageData);
      } else {
        // console.log('未知的响应格式:', responseData);
      }
    } else if (response.data && Array.isArray(response.data)) {
      // 备用：直接从 response.data 获取
      imageData = response.data;
      // console.log('从 response.data 获取图片数据:', imageData);
    }

    // console.log('最终提取的图片数据:', imageData);

    if (imageData && Array.isArray(imageData) && imageData.length > 0) {
      const base64Image = imageData[0];
      // console.log('获取到的base64图片数据长度:', base64Image.length);
      // console.log('base64图片数据前100个字符:', base64Image.substring(0, 100));

      previewImage.value = base64Image;
      message.success('预览生成成功');
    } else {
      previewImage.value = '';
      // console.log('未获取到图片数据，imageData:', imageData);
      // message.warning('未获取到预览图片，请检查视频文件和字幕设置');
    }
  } catch (error) {
    // console.error('获取字幕预览失败:', error);
    message.error(`获取字幕预览失败: ${error.message || '未知错误'}`);
    previewImage.value = '';
  } finally {
    previewLoading.value = false;
  }
}

// 处理字幕拖拽
function handleMouseDown(event: MouseEvent) {
  if (!previewContainer.value) return;

  isDragging.value = true;

  function handleMouseMove(e: MouseEvent) {
    if (!isDragging.value || !previewContainer.value) return;

    const rect = previewContainer.value.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    // 按照后端逻辑：允许字幕中心点到达边界甚至稍微超出
    // 后端会确保至少50%的文本可见，前端也采用相同策略
    const margin = 2; // 减少边距限制，允许更自由的拖拽
    subtitlePosition.value.x = Math.max(-margin, Math.min(100 + margin, x));
    subtitlePosition.value.y = Math.max(-margin, Math.min(100 + margin, y));
  }

  function handleMouseUp() {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
}

// 组件挂载后，如果有视频URL就自动获取视频信息和生成预览
onMounted(() => {
  if (actualVideoUrl.value) {
    setTimeout(() => {
      getVideoInfoAndPreview();
    }, 500);
  }
});
</script>

<template>
  <div class="subtitle-position-selector">
    <!-- 参数设置区域 -->
    <div class="parameter-section">
      <div class="parameter-layout">
        <!-- 第一行：字幕内容 -->
<!--        <div class="parameter-row">-->
<!--          <div class="parameter-item full-width">-->
<!--            <label>字幕内容:</label>-->
<!--            <NInput v-model:value="subtitleStyle.text" placeholder="输入字幕内容" :maxlength="200" size="large" style="height: 60px"/>-->
<!--          </div>-->
<!--        </div>-->

        <!-- 字体设置行 -->
        <div class="parameter-row main-controls">

          <div class="parameter-item font-selector">
            <label>字体:</label>
            <NSelect
              v-model:value="subtitleStyle.fontName"
              :options="defaultFontOptions"
              size="medium"
              placeholder="选择字体"
              class="font-select"
            />
          </div>

          <div class="parameter-item size-control">
            <label>字体大小 (pt):</label>
            <NInputNumber
              v-model:value="subtitleStyle.fontSize"
              :min="12"
              :max="200"
              :step="1"
              size="medium"
              class="size-input"
            />
          </div>

          <div class="parameter-item color-control">
            <label>字体颜色:</label>
            <NPopover
              v-model:show="showColorPicker"
              trigger="click"
              placement="bottom"
              :show-arrow="false"
            >
              <template #trigger>
                <div class="color-trigger" @click="showColorPicker = !showColorPicker">
                  <div class="color-swatch" :style="{ backgroundColor: subtitleColor }"></div>
                  <span class="color-value">{{ subtitleColor }}</span>
                  <span class="dropdown-icon">▼</span>
                </div>
              </template>
              <div class="color-picker-panel">
                <div class="preset-colors">
                  <div class="color-row">
                    <div
                      v-for="color in presetColors"
                      :key="color"
                      class="preset-color"
                      :class="{ active: subtitleColor === color }"
                      :style="{ backgroundColor: color }"
                      @click="selectPresetColor(color)"
                      :title="color"
                    ></div>
                  </div>
                </div>
                <div class="custom-color-section">
                  <NColorPicker
                    :value="subtitleColor"
                    @update:value="updateColor"
                    :show-alpha="false"
                    size="medium"
                  />
                </div>
              </div>
            </NPopover>
          </div>

          <div class="parameter-item button-control">
            <NButton
              type="primary"
              size="large"
              :loading="previewLoading"
              @click="getPreview"
              class="preview-button"
            >
              保存字幕预览
            </NButton>
          </div>
        </div>
      </div>



    </div>

    <!-- 预览区域 -->
    <div class="preview-section">
      <div
        ref="previewContainer"
        class="preview-container"
        :class="{ dragging: isDragging }"
        :style="previewContainerStyle"
      >
        <NSpin :show="previewLoading">
          <div class="video-preview">
            <img v-if="previewImage" :src="previewImage" alt="字幕预览" class="preview-image" />
            <div v-else class="preview-placeholder">
              <div class="placeholder-icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="48"
                  height="48"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <polygon points="23 7 16 12 23 17 23 7"></polygon>
                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                </svg>
              </div>
              <div class="placeholder-text">点击"生成预览"查看效果</div>
            </div>

            <!-- 可拖拽的字幕位置指示器（没有预览图时显示） -->
            <div
              v-if="!previewImage"
              class="draggable-subtitle-indicator"
              :style="{
                left: subtitlePosition.x + '%',
                top: subtitlePosition.y + '%'
              }"
              @mousedown="handleMouseDown"
            >
              <div class="position-marker">
                <div class="marker-dot"></div>
                <div class="marker-text">字幕位置</div>
                <div
                  class="marker-subtitle"
                  :style="{
                    fontSize: computedFontSize + 'px',
                    fontFamily: subtitleStyle.fontName,
                    fontStyle: subtitleStyle.fontStyle,
                    fontWeight: subtitleStyle.fontWeight,
                    color: `rgb(${subtitleStyle.r}, ${subtitleStyle.g}, ${subtitleStyle.b})`
                  }"
                >
                  {{ subtitleStyle.text || '预览字幕' }}
                </div>
              </div>
            </div>

            <!-- 可拖拽的字幕（有预览图时显示，与后端保持一致） -->
            <div
              v-if="previewImage"
              class="draggable-subtitle"
              :style="{
                left: subtitlePosition.x + '%',
                top: subtitlePosition.y + '%',
                fontSize: computedFontSize + 'px',
                fontFamily: subtitleStyle.fontName,
                fontStyle: subtitleStyle.fontStyle,
                fontWeight: subtitleStyle.fontWeight,
                color: `rgb(${subtitleStyle.r}, ${subtitleStyle.g}, ${subtitleStyle.b})`,
                textAlign: 'center',
                transform: 'translate(-50%, -50%)',
                cursor: 'move',
                userSelect: 'none',
                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                whiteSpace: 'pre',
                lineHeight: '1.2',
                position: 'absolute',
                maxWidth: 'auto',
                zIndex: 10
              }"
              @mousedown="handleMouseDown"
            >
              {{ subtitleStyle.text }}
            </div>
          </div>
        </NSpin>
      </div>
    </div>

    <!-- 提示信息区域 -->
    <div class="tips-section">
      <div class="tips-content">
        <div class="tip-item">
          <span class="tip-icon">💡</span>
          <span class="tip-text">拖拽字幕到合适位置，然后点击"生成预览"查看效果</span>
        </div>
        <div class="tip-item">
          <span class="tip-icon">📍</span>
          <span class="tip-text">
            当前位置: ({{ Math.round(subtitlePosition.x) }}%, {{ Math.round(subtitlePosition.y) }}%)
          </span>
        </div>
        <div v-if="videoInfo" class="tip-item video-info">
          <span class="tip-icon">🎬</span>
          <span class="tip-text">
            视频信息: {{ videoInfo.width }}×{{ videoInfo.height }} ({{
              videoInfo.orientation === 'landscape' ? '横屏' : videoInfo.orientation === 'portrait' ? '竖屏' : '方形'
            }})
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 导入自定义字体 */
@font-face {
  font-family: 'PingFangLaiJiangHuLangTi';
  src: url('@/assets/平方赖江湖琅琊体/PingFangLaiJiangHuLangTi/PingFangLaiJiangHuLangTi-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'PingFangLaiJiangHuFeiYangTi';
  src: url('@/assets/平方赖江湖飞扬体/PingFangLaiJiangHuFeiYangTi/PingFangLaiJiangHuFeiYangTi-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'SuCaiJiShiKangKangTi';
  src: url('@/assets/素材集市康康体/SuCaiJiShiKangKangTi/SuCaiJiShiKangKangTi-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'JingNanMaiYuanTi';
  src: url('@/assets/荆南麦圆体/JingNanMaiYuanTi/Kingnammm-Maiyuan-II-Regular-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'AlimamaFangYuanTi';
  src: url('@/assets/阿里妈妈方圆体/AlimamaFangYuanTiVF-Thin-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
</style>

<style scoped>
.subtitle-position-selector {
  width: 100%;
  background: #f8fafc;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 参数设置区域 */
.parameter-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.parameter-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.parameter-row {
  display: flex;
  gap: 20px;
  align-items: end;
  flex-wrap: wrap;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
}

.parameter-row.main-controls {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #dee2e6;
}

.parameter-row:first-child {
  margin-bottom: 4px;
}

@media (max-width: 768px) {
  .parameter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .parameter-layout {
    gap: 16px;
  }
}

.parameter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
}

.parameter-item.font-selector {
  flex: 1;
  min-width: 180px;
}

.parameter-item.size-control {
  min-width: 140px;
}

.parameter-item.color-control {
  min-width: 220px;
}

.parameter-item.button-control {
  min-width: 120px;
  align-self: flex-end;
}

.parameter-item.full-width {
  flex: 1;
  min-width: 100%;
}

.parameter-item label {
  font-weight: 600;
  color: #495057;
  font-size: 13px;
  margin: 0;
  white-space: nowrap;
  line-height: 1.4;
  letter-spacing: 0.3px;
  margin-bottom: 4px;
}

.parameter-item input,
.parameter-item select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  background-color: white;
  min-height: 38px;
  box-sizing: border-box;
}

.parameter-item input:focus,
.parameter-item select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.parameter-item select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
}

/* 预览区域 */
.preview-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-container {
  position: relative;
  width: auto;
  min-height: 250px;
  max-height: 480px;
  border: 2px solid #e8eaed;
  border-radius: 12px;
  overflow: hidden;
  cursor: crosshair;
  background: #fafbfc;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.preview-container:hover {
  border-color: #4096ff;
  box-shadow: 0 6px 16px rgba(64, 150, 255, 0.12);
}

.preview-container.dragging {
  cursor: grabbing;
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b;
}

.placeholder-icon {
  margin-bottom: 16px;
  color: #94a3b8;
  opacity: 0.8;
}

.placeholder-text {
  font-size: 16px;
  font-weight: 500;
  color: #475569;
}

.draggable-subtitle {
  position: absolute;
  cursor: grab;
  user-select: none;
  z-index: 10;
  pointer-events: auto;
  /* 移除背景和边框，让字幕看起来更自然 */
  background: transparent;
  padding: 0;
  border: none;
  /* 添加选中状态的视觉反馈 */
  transition: all 0.2s ease;
}

.draggable-subtitle:hover {
  /* 悬停时添加轻微的阴影效果 */
  filter: drop-shadow(0 0 4px rgba(64, 150, 255, 0.6));
}

.draggable-subtitle:active {
  cursor: grabbing;
  /* 拖拽时添加更明显的阴影 */
  filter: drop-shadow(0 0 8px rgba(64, 150, 255, 0.8));
}

/* 字幕位置指示器样式 */
.draggable-subtitle-indicator {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: grab;
  z-index: 10;
}

.position-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.marker-dot {
  width: 14px;
  height: 14px;
  background: linear-gradient(135deg, #4096ff 0%, #1677ff 100%);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.4);
  transition: all 0.3s ease;
}

.marker-dot:hover {
  transform: scale(1.2);
  box-shadow: 0 6px 16px rgba(64, 150, 255, 0.6);
}

.marker-text {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.marker-subtitle {
  margin-top: 8px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  font-weight: bold;
  white-space: pre;
  text-align: center;
  /* max-width: 200px; */
  /* word-wrap: break-word; */
}

/* 提示信息区域 */
.tips-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.tips-content {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0f2fe;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.tip-text {
  color: #0369a1;
  font-weight: 500;
}

.video-info .tip-text {
  color: #059669 !important;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .parameter-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .parameter-item {
    gap: 6px;
  }
}

/* 预览按钮特殊样式 */
.preview-button {
  min-width: 120px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  letter-spacing: 0.5px;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.preview-button:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

/* 自定义组件样式 */
.parameter-item :deep(.n-input),
.parameter-item :deep(.n-input-number),
.parameter-item :deep(.n-select),
.parameter-item :deep(.n-color-picker-trigger),
.parameter-item :deep(.n-button) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.parameter-item :deep(.n-input:hover),
.parameter-item :deep(.n-input-number:hover),
.parameter-item :deep(.n-select:hover .n-base-selection),
.parameter-item :deep(.n-color-picker-trigger:hover) {
  border-color: #4096ff;
}

.parameter-item :deep(.n-input:focus-within),
.parameter-item :deep(.n-input-number:focus-within),
.parameter-item :deep(.n-select.n-select--focused .n-base-selection) {
  border-color: #4096ff;
  box-shadow: 0 0 0 3px rgba(64, 150, 255, 0.1);
}

/* 字体选择下拉框特殊样式 */
.parameter-item :deep(.n-select) {
  min-width: 160px;
}

.parameter-item.font-selector :deep(.n-select) {
  min-width: 180px;
}

.parameter-item.size-control :deep(.n-input-number) {
  width: 100px;
}

/* 自定义颜色选择器样式 */
.color-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.color-trigger:hover {
  border-color: #4096ff;
}

.color-swatch {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.color-value {
  font-size: 13px;
  color: #374151;
  flex: 1;
  font-family: monospace;
}

.dropdown-icon {
  font-size: 10px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.color-picker-panel {
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 280px;
}

.preset-colors {
  margin-bottom: 16px;
}

.color-row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
}

.preset-color {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.preset-color:hover {
  transform: scale(1.1);
  border-color: #4096ff;
}

.preset-color.active {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.preset-color.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.custom-color-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.parameter-item.color-control :deep(.n-color-picker-trigger) {
  width: 60px;
  height: 38px;
}

.parameter-item :deep(.n-select .n-base-selection) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.parameter-item :deep(.n-select .n-base-selection-label) {
  font-weight: 500;
  color: #374151;
}
</style>
