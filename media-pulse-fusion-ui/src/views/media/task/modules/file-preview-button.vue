<script setup lang="ts">
import { ref, computed } from 'vue';
import { NIcon, NModal, NCard, NSpace, NButton, NEmpty, NTag } from 'naive-ui';
import MediaPreviewModal from './media-preview-modal.vue';

defineOptions({
  name: 'FilePreviewButton'
});

interface Props {
  files: string | string[] | string[][];
  type: 'video' | 'audio';
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
});

// 文件预览相关状态
const filePreviewVisible = ref(false);
const mediaPreviewVisible = ref(false);
const previewUrl = ref('');

// 解析文件列表
const fileList = computed(() => {
  if (!props.files) return [];

  try {
    let urls: string[] = [];

    if (typeof props.files === 'string') {
      // 处理空字符串或只有空格的字符串
      if (!props.files.trim()) return [];

      if (props.files.startsWith('[[')) {
        // 处理二维数组 [[视频1, 视频2]] 或 [[视频1], [视频2]]
        const parsedArray = JSON.parse(props.files);
        if (Array.isArray(parsedArray)) {
          parsedArray.forEach(subArray => {
            if (Array.isArray(subArray)) {
              subArray.forEach(url => {
                if (url && typeof url === 'string' && url.trim()) {
                  urls.push(url.trim());
                }
              });
            } else if (subArray && typeof subArray === 'string' && subArray.trim()) {
              urls.push(subArray.trim());
            }
          });
        }
      } else if (props.files.startsWith('[')) {
        const parsedArray = JSON.parse(props.files);
        if (Array.isArray(parsedArray)) {
          urls = parsedArray.filter(url => url && typeof url === 'string' && url.trim()).map(url => url.trim());
        }
      } else if (props.files.includes(',')) {
        urls = props.files.split(',').filter(url => url && url.trim()).map(url => url.trim());
      } else {
        // 单个URL
        if (props.files.trim()) {
          urls = [props.files.trim()];
        }
      }
    } else if (Array.isArray(props.files)) {
      if (props.files.length > 0 && Array.isArray(props.files[0])) {
        // 处理二维数组
        props.files.forEach(subArray => {
          if (Array.isArray(subArray)) {
            subArray.forEach(url => {
              if (url && typeof url === 'string' && url.trim()) {
                urls.push(url.trim());
              }
            });
          } else if (subArray && typeof subArray === 'string' && subArray.trim()) {
            urls.push(subArray.trim());
          }
        });
      } else {
        urls = props.files.filter(url => url && typeof url === 'string' && url.trim()).map(url => url.trim());
      }
    }

    return urls.filter(url => url && url.trim() !== '');
  } catch (e) {
    console.error('解析文件列表失败:', e, props.files);
    return [];
  }
});

// 文件数量
const fileCount = computed(() => fileList.value.length);

// 显示文件预览列表
function showFilePreview() {
  if (fileCount.value > 0) {
    filePreviewVisible.value = true;
  }
}

// 预览单个文件
function handlePreviewFile(url: string) {
  previewUrl.value = url;
  mediaPreviewVisible.value = true;
}

// 获取文件名
function getFileName(url: string, index: number): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const fileName = pathname.split('/').pop() || `${props.type === 'video' ? '视频' : '音频'} ${index + 1}`;
    return fileName;
  } catch {
    return `${props.type === 'video' ? '视频' : '音频'} ${index + 1}`;
  }
}
</script>

<template>
  <div>
    <!-- 文件预览按钮 -->
    <div
      v-if="fileCount > 0"
      class="file-preview-trigger"
      :class="{ disabled: disabled }"
      @click="showFilePreview"
    >
      <div class="preview-trigger-content">
        <NIcon size="14" class="preview-trigger-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
               stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
        </NIcon>
        <span class="preview-trigger-text">
          查看已上传的 {{ fileCount }} 个{{ type === 'video' ? '视频' : '音频' }}文件
        </span>
        <NIcon size="14" class="preview-trigger-arrow">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
               stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        </NIcon>
      </div>
    </div>

    <!-- 无文件时显示 -->
    <div v-else class="no-files">
      <span>暂无{{ type === 'video' ? '视频' : '音频' }}文件</span>
    </div>

    <!-- 文件列表预览弹窗 -->
    <NModal v-model:show="filePreviewVisible" style="width: 80%; max-width: 600px;">
      <NCard
        style="width: 100%;"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header>
          <div class="text-center">
            {{ type === 'video' ? '视频' : '音频' }}文件列表 ({{ fileCount }}个)
          </div>
        </template>

        <div class="file-list-container">
          <NEmpty v-if="fileList.length === 0" description="暂无文件" />

          <NSpace v-else vertical size="medium">
            <div
              v-for="(url, index) in fileList"
              :key="index"
              class="file-item"
            >
              <div class="file-info">
                <NTag :type="type === 'video' ? 'success' : 'info'" size="small">
                  {{ type === 'video' ? '视频' : '音频' }} {{ index + 1 }}
                </NTag>
                <span class="file-name">{{ getFileName(url, index) }}</span>
              </div>
              <div class="file-actions">
                <NButton size="small" type="primary" @click="handlePreviewFile(url)">
                  预览
                </NButton>
              </div>
            </div>
          </NSpace>
        </div>

        <template #footer>
          <div style="display: flex; justify-content: center;">
            <NButton @click="filePreviewVisible = false">
              关闭
            </NButton>
          </div>
        </template>
      </NCard>
    </NModal>

    <!-- 媒体预览弹窗 -->
    <MediaPreviewModal
      :visible="mediaPreviewVisible"
      :url="previewUrl"
      :type="type"
      @update:visible="mediaPreviewVisible = $event"
    />
  </div>
</template>

<style scoped>
.file-preview-trigger {
  background: linear-gradient(135deg, rgba(64, 150, 255, 0.05), rgba(64, 150, 255, 0.02));
  border: 1px solid rgba(64, 150, 255, 0.2);
  border-radius: 6px;
  padding: 6px 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  font-size: 12px;
  max-width: 100%;
}

.file-preview-trigger:hover {
  background: linear-gradient(135deg, rgba(64, 150, 255, 0.1), rgba(64, 150, 255, 0.05));
  border-color: rgba(64, 150, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.15);
}

.file-preview-trigger.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.file-preview-trigger.disabled:hover {
  transform: none;
  box-shadow: none;
}

.preview-trigger-content {
  display: flex;
  align-items: center;
  gap: 6px;
  min-height: 20px;
}

.preview-trigger-icon {
  color: rgba(64, 150, 255, 0.8);
  flex-shrink: 0;
}

.preview-trigger-text {
  color: #333;
  font-size: 12px;
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-trigger-arrow {
  color: rgba(64, 150, 255, 0.6);
  flex-shrink: 0;
}

.no-files {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 8px;
}

.file-list-container {
  max-height: 400px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-name {
  color: #333;
  font-size: 14px;
  word-break: break-all;
}

.file-actions {
  flex-shrink: 0;
}
</style>
