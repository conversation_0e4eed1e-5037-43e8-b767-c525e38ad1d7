<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue';
import {
  fetchBatchCreateDigitalLens,
  fetchCreateDigitalLens,
  fetchUpdateDigitalLens,
  fetchUploadDigitalLensAudio,
  fetchUploadDigitalLensMaterial,
  fetchUploadDigitalLensVideo
} from '@/service/api/media/digital-lens';
// @ts-ignore - This module exists but TypeScript can't find its type declaration
import { fetchAIPolish, fetchAITargetText } from '@/service/api/media/ai';
import { getToken } from '@/store/modules/auth/shared';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
// @ts-ignore - This component uses <script setup> without default export
import TitlePolishDrawer from '../../task/modules/title-polish-drawer.vue';
// @ts-ignore - This component uses <script setup> without default export
import MediaPreviewModal from '../../task/modules/media-preview-modal.vue';
import MaterialsManager from './materials-manager.vue';
// @ts-ignore - This component uses <script setup> without default export
import VideoMaterialsManager from './video-materials-manager.vue';
// @ts-ignore - This component uses <script setup> without default export
import AudioMaterialsManager from './audio-materials-manager.vue';
// @ts-ignore - This component uses <script setup> without default export
import SubtitlePositionSelectorVisual from '../../task/modules/subtitle-position-selector-visual.vue';

defineOptions({
  name: 'DigitalLensOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Media.DigitalLens | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

// 视频格式
const videoFormats = '.mp4,.avi,.mov,.wmv,.flv,.mkv,.webm,.m4v,.3gp,.ts,.mts,.m2ts';

// 音频格式
const audioFormats = '.mp3,.wav,.ogg,.aac,.flac,.m4a,.wma,.aiff,.alac';

const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增数字镜像',
    edit: '编辑数字镜像'
  };
  return titles[props.operateType];
});

// 添加AI标题润色相关状态
const titlePolishVisible = ref(false);
const currentTitle = computed(() => model.title || '');

// AI生成目标文本的加载状态
const isGeneratingTargetText = ref(false);

// 上传状态 - 分别为不同类型的上传创建独立状态
const isVideoUploading = ref(false);
const isAudioUploading = ref(false);

// 预览相关状态
const previewVisible = ref(false);
const previewUrl = ref('');
const previewType = ref<'video' | 'audio'>('video');

// 打开预览
function openPreview(url: string, type: 'video' | 'audio' = 'video') {
  previewUrl.value = url;
  previewType.value = type;
  previewVisible.value = true;
}

// 获取第一个视频URL用于字幕位置预览
function getFirstVideoUrl(): string {
  // 优先使用人物视频
  if (model.characterVideo) {
    return model.characterVideo;
  }

  // 其次使用其他视频素材中的第一个
  if (model.otherVideoMaterials) {
    try {
      const videoMaterials = JSON.parse(model.otherVideoMaterials);
      if (Array.isArray(videoMaterials) && videoMaterials.length > 0) {
        return videoMaterials[0];
      }
    } catch (e) {
      // 如果解析失败，尝试直接使用字符串
      if (typeof model.otherVideoMaterials === 'string' && model.otherVideoMaterials.trim()) {
        return model.otherVideoMaterials;
      }
    }
  }

  return '';
}

// 处理预览按钮点击
function handlePreview(urls: string | string[] | null, type: 'video' | 'audio') {
  if (!urls) {
    window.$message?.warning('没有可预览的媒体文件');
    return;
  }

  let url: string;

  // 处理可能的数组或JSON字符串格式
  if (typeof urls === 'string') {
    if (urls.startsWith('[')) {
      try {
        const parsedUrls = JSON.parse(urls);
        if (parsedUrls && parsedUrls.length > 0) {
          url = parsedUrls[0];
        } else {
          window.$message?.warning('没有可预览的媒体文件');
          return;
        }
      } catch (e) {
        // console.error('解析URL失败:', e);
        window.$message?.warning('解析URL失败');
        return;
      }
    } else {
      url = urls;
    }
  } else if (Array.isArray(urls) && urls.length > 0) {
    url = urls[0];
  } else {
    window.$message?.warning('没有可预览的媒体文件');
    return;
  }

  openPreview(url, type);
}

// 处理AI辅助按钮点击
function handleAIPolishClick(type: 'title' | 'targetText' = 'title') {
  if (type === 'title') {
    if (!model.title) {
      window.$message?.warning('请先输入标题');
      return;
    }
    // console.log('点击AI辅助润色按钮，当前标题:', model.title);
    titlePolishVisible.value = true;
  } else {
    // 目标文本生成需要基于标题
    if (!model.title) {
      window.$message?.warning('请先输入标题');
      return;
    }
    // 直接生成目标文本
    generateTargetText();
  }
}

// 处理选择润色后的标题
function handleSelectPolishedTitle(title: string) {
  // console.log('选择润色后的标题:', title);
  model.title = title;
  window.$message?.success('已应用AI润色标题');
}

// AI生成目标文本
async function generateTargetText() {
  if (!model.title) {
    window.$message?.warning('请先输入标题');
    return;
  }

  isGeneratingTargetText.value = true;

  // 显示提示信息
  window.$message?.info('AI文本生成中，请稍等...');

  try {
    const response = await fetchAITargetText(model.title);
    console.log('AI生成返回原始数据:', response);

    // 处理响应数据
    const responseData = response.data;
    let generatedText = '';

    // 情况1: 数据已经是数组
    if (Array.isArray(responseData)) {
      generatedText = responseData.length > 0 ? responseData[0] : '';
    }
    // 情况2: 数据是字符串（可能是JSON字符串）
    else if (typeof responseData === 'string') {
      try {
        // 尝试解析JSON字符串
        const parsedData = JSON.parse(responseData);
        if (Array.isArray(parsedData)) {
          generatedText = parsedData.length > 0 ? parsedData[0] : '';
        } else {
          generatedText = responseData;
        }
      } catch (e) {
        // 如果解析失败，直接使用字符串
        generatedText = responseData;
      }
    }

    if (generatedText) {
      model.targetText = generatedText;
      window.$message?.success('AI目标文本生成成功');
    } else {
      window.$message?.warning('生成的文本为空，请重试');
    }
  } catch (e) {
    console.error('生成目标文本失败:', e);

    // 检查是否为超时错误
    if (e.message && e.message.includes('timeout')) {
      window.$message?.error('AI生成请求超时，请稍后重试');
    } else {
      window.$message?.error('生成目标文本失败，请稍后重试');
    }
  } finally {
    isGeneratingTargetText.value = false;
  }
}

type Model = Api.Media.DigitalLensOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    title: '',
    characterVideo: null,
    clonedVoice: null,
    referenceText: '',
    targetText: '',
    otherMaterials: null,
    otherVideoMaterials: null,
    otherAudioMaterials: null,
    status: 0, // 默认状态为待处理
    taskId: null,
    subtitlePosition: ''
  };
}

// 上传组件的引用
const videoUploadRef = ref();
const audioUploadRef = ref();
// 用于强制重新渲染上传组件的key
const uploadKey = ref(0);

// 完全重置表单数据和状态
function resetFormCompletely() {
  // 重置模型数据
  Object.assign(model, createDefaultModel());

  // 重置验证状态
  restoreValidation();

  // 重置上传状态
  isVideoUploading.value = false;
  isAudioUploading.value = false;

  // 强制重新渲染上传组件以清空内部状态
  uploadKey.value++;

  // 重置预览状态
  previewVisible.value = false;
  previewUrl.value = '';

  // 重置润色状态
  titlePolishVisible.value = false;
  isGeneratingTargetText.value = false;

  console.log('表单已完全重置');
}

// 批量创建相同数字镜像的数量
const batchCount = ref(1);
// 保存多个不同数字镜像的数组
const multiLensList = ref<any[]>([]);

// 验证规则
const rules = computed(() => {
  return {
    title: [
      {
        required: true,
        message: '请输入标题',
        trigger: ['blur', 'input']
      }
    ],
    characterVideo: [
      {
        required: true,
        validator: (rule: any, value: any) => {
          if (!value || (Array.isArray(value) && value.length === 0)) {
            return new Error('请上传人物视频');
          }
          return true;
        },
        trigger: ['blur', 'change']
      }
    ],
    clonedVoice: [
      {
        required: true,
        validator: (rule: any, value: any) => {
          if (!value || (Array.isArray(value) && value.length === 0)) {
            return new Error('请上传克隆音色文件');
          }
          return true;
        },
        trigger: ['blur', 'change']
      }
    ],
    referenceText: [
      {
        required: true,
        message: '请输入音色文件对应的文本内容',
        trigger: ['blur', 'input']
      }
    ],
    targetText: [
      {
        required: true,
        message: '请输入目标文本',
        trigger: ['blur', 'input']
      }
    ]
  };
});

// 修改handleUpdateModelWhenEdit函数，改进处理JSON格式素材的逻辑
function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    // 复制基本属性
    const rowDataCopy = { ...props.rowData };

    // 处理可能是JSON字符串的字段
    try {
      // 处理人物视频
      if (typeof rowDataCopy.characterVideo === 'string') {
        if (rowDataCopy.characterVideo.startsWith('[')) {
          try {
            rowDataCopy.characterVideo = JSON.parse(rowDataCopy.characterVideo);
          } catch (e) {
            // 解析失败，保持原样
            // console.warn('解析人物视频JSON失败:', rowDataCopy.characterVideo);
          }
        } else if (rowDataCopy.characterVideo.includes(',')) {
          // 兼容逗号分隔格式
          rowDataCopy.characterVideo = rowDataCopy.characterVideo.split(',');
        }
      }

      // 处理克隆音色
      if (typeof rowDataCopy.clonedVoice === 'string') {
        if (rowDataCopy.clonedVoice.startsWith('[')) {
          try {
            rowDataCopy.clonedVoice = JSON.parse(rowDataCopy.clonedVoice);
          } catch (e) {
            // console.warn('解析克隆音色JSON失败:', rowDataCopy.clonedVoice);
          }
        } else if (rowDataCopy.clonedVoice.includes(',')) {
          rowDataCopy.clonedVoice = rowDataCopy.clonedVoice.split(',');
        }
      }

      // 处理其他视频素材
      if (typeof rowDataCopy.otherVideoMaterials === 'string') {
        if (rowDataCopy.otherVideoMaterials.startsWith('[')) {
          try {
            rowDataCopy.otherVideoMaterials = JSON.parse(rowDataCopy.otherVideoMaterials);
          } catch (e) {
            // console.warn('解析其他视频素材JSON失败:', rowDataCopy.otherVideoMaterials);
            // 尝试其他方式解析
            if (rowDataCopy.otherVideoMaterials.includes(',')) {
              rowDataCopy.otherVideoMaterials = rowDataCopy.otherVideoMaterials.split(',');
            } else {
              // 单个URL，转为数组
              rowDataCopy.otherVideoMaterials = [rowDataCopy.otherVideoMaterials];
            }
          }
        } else if (rowDataCopy.otherVideoMaterials.includes(',')) {
          // 兼容逗号分隔格式
          rowDataCopy.otherVideoMaterials = rowDataCopy.otherVideoMaterials.split(',');
        } else if (rowDataCopy.otherVideoMaterials) {
          // 单个URL，转为数组
          rowDataCopy.otherVideoMaterials = [rowDataCopy.otherVideoMaterials];
        }
      }

      // 处理其他音频素材
      if (typeof rowDataCopy.otherAudioMaterials === 'string') {
        if (rowDataCopy.otherAudioMaterials.startsWith('[')) {
          try {
            rowDataCopy.otherAudioMaterials = JSON.parse(rowDataCopy.otherAudioMaterials);
          } catch (e) {
            // console.warn('解析其他音频素材JSON失败:', rowDataCopy.otherAudioMaterials);
            // 尝试其他方式解析
            if (rowDataCopy.otherAudioMaterials.includes(',')) {
              rowDataCopy.otherAudioMaterials = rowDataCopy.otherAudioMaterials.split(',');
            } else {
              // 单个URL，转为数组
              rowDataCopy.otherAudioMaterials = [rowDataCopy.otherAudioMaterials];
            }
          }
        } else if (rowDataCopy.otherAudioMaterials.includes(',')) {
          rowDataCopy.otherAudioMaterials = rowDataCopy.otherAudioMaterials.split(',');
        } else if (rowDataCopy.otherAudioMaterials) {
          rowDataCopy.otherAudioMaterials = [rowDataCopy.otherAudioMaterials];
        }
      }

      // 向后兼容：如果有otherMaterials但没有otherVideoMaterials和otherAudioMaterials
      if (
        rowDataCopy.otherMaterials &&
        (!rowDataCopy.otherVideoMaterials || rowDataCopy.otherVideoMaterials.length === 0) &&
        (!rowDataCopy.otherAudioMaterials || rowDataCopy.otherAudioMaterials.length === 0)
      ) {
        // 解析otherMaterials
        let parsedMaterials = [];
        if (typeof rowDataCopy.otherMaterials === 'string') {
          if (rowDataCopy.otherMaterials.startsWith('[')) {
            try {
              parsedMaterials = JSON.parse(rowDataCopy.otherMaterials);
            } catch (e) {
              if (rowDataCopy.otherMaterials.includes(',')) {
                parsedMaterials = rowDataCopy.otherMaterials.split(',');
              } else {
                parsedMaterials = [rowDataCopy.otherMaterials];
              }
            }
          } else if (rowDataCopy.otherMaterials.includes(',')) {
            parsedMaterials = rowDataCopy.otherMaterials.split(',');
          } else {
            parsedMaterials = [rowDataCopy.otherMaterials];
          }
        } else if (Array.isArray(rowDataCopy.otherMaterials)) {
          parsedMaterials = [...rowDataCopy.otherMaterials];
        }

        // 将otherMaterials中的素材分类到视频和音频
        const videoExtensions = props.videoFormats.split(',').map(ext => ext.replace('.', '').toLowerCase());
        const audioExtensions = props.audioFormats.split(',').map(ext => ext.replace('.', '').toLowerCase());

        const videoMaterials = [];
        const audioMaterials = [];

        parsedMaterials.forEach(url => {
          if (typeof url === 'string') {
            const extension = url.split('.').pop()?.toLowerCase() || '';
            if (videoExtensions.includes(extension)) {
              videoMaterials.push(url);
            } else if (audioExtensions.includes(extension)) {
              audioMaterials.push(url);
            } else {
              // 如果无法确定类型，默认添加到视频素材
              videoMaterials.push(url);
            }
          }
        });

        if (videoMaterials.length > 0) {
          rowDataCopy.otherVideoMaterials = videoMaterials;
        }

        if (audioMaterials.length > 0) {
          rowDataCopy.otherAudioMaterials = audioMaterials;
        }
      }
    } catch (e) {
      console.error('处理素材数据时出错:', e);
    }

    // 更新模型
    Object.assign(model, rowDataCopy);

    // 检查并转换素材格式
    if (!Array.isArray(model.otherVideoMaterials) && model.otherVideoMaterials) {
      console.warn('其他视频素材需要转换为数组:', model.otherVideoMaterials);
      model.otherVideoMaterials = [model.otherVideoMaterials];
    }

    if (!Array.isArray(model.otherAudioMaterials) && model.otherAudioMaterials) {
      console.warn('其他音频素材需要转换为数组:', model.otherAudioMaterials);
      model.otherAudioMaterials = [model.otherAudioMaterials];
    }
  }
}

// 解析URL列表
function parseUrls(urls: string | string[]): string[] {
  if (!urls) return [];

  if (typeof urls === 'string') {
    try {
      if (urls.startsWith('[')) {
        // JSON字符串
        return JSON.parse(urls);
      } else if (urls.includes(',')) {
        // 逗号分隔字符串
        return urls.split(',').map(url => url.trim());
      } else if (urls) {
        // 单个URL
        return [urls];
      }
    } catch (e) {
      // console.error('解析URL失败:', e);
    }
  } else if (Array.isArray(urls)) {
    return [...urls];
  }

  return [];
}

function closeDrawer() {
  // 完全重置表单数据和状态
  resetFormCompletely();

  // 清空多数字镜像列表
  multiLensList.value = [];

  visible.value = false;
}

// 准备提交数据 - 将数组转换为JSON字符串以适应后端API
function prepareDigitalLensData(data: any) {
  const submitData = { ...data };

  // 处理人物视频
  if (Array.isArray(submitData.characterVideo)) {
    submitData.characterVideo = JSON.stringify(submitData.characterVideo);
  } else if (typeof submitData.characterVideo === 'string' && !submitData.characterVideo.startsWith('[')) {
    try {
      submitData.characterVideo = JSON.stringify([submitData.characterVideo]);
    } catch (e) {
      // console.error('处理人物视频失败:', e);
    }
  }

  // 处理克隆音色
  if (Array.isArray(submitData.clonedVoice)) {
    submitData.clonedVoice = JSON.stringify(submitData.clonedVoice);
  } else if (typeof submitData.clonedVoice === 'string' && !submitData.clonedVoice.startsWith('[')) {
    try {
      submitData.clonedVoice = JSON.stringify([submitData.clonedVoice]);
    } catch (e) {
      // console.error('处理克隆音色失败:', e);
    }
  }

  // 处理其他视频素材 - 将数组转换为JSON字符串
  if (Array.isArray(submitData.otherVideoMaterials)) {
    submitData.otherVideoMaterials = JSON.stringify(submitData.otherVideoMaterials);
  } else if (typeof submitData.otherVideoMaterials === 'string' && !submitData.otherVideoMaterials.startsWith('[')) {
    try {
      submitData.otherVideoMaterials = JSON.stringify([submitData.otherVideoMaterials]);
    } catch (e) {
      // console.error('处理其他视频素材失败:', e);
    }
  }

  // 处理其他音频素材 - 将数组转换为JSON字符串
  if (Array.isArray(submitData.otherAudioMaterials)) {
    submitData.otherAudioMaterials = JSON.stringify(submitData.otherAudioMaterials);
  } else if (typeof submitData.otherAudioMaterials === 'string' && !submitData.otherAudioMaterials.startsWith('[')) {
    try {
      submitData.otherAudioMaterials = JSON.stringify([submitData.otherAudioMaterials]);
    } catch (e) {
      // console.error('处理其他音频素材失败:', e);
    }
  }

  return submitData;
}

// 修改handleSubmit函数，修复批量创建的API调用问题
async function handleSubmit() {
  try {
    await validate();
  } catch (err) {
    return;
  }

  // 检查是创建单个数字镜像还是批量创建
  if (batchCount.value <= 1) {
    // 单个数字镜像 - 正常提交
    const submitData = prepareDigitalLensData({ ...model });

    // request
    if (props.operateType === 'add') {
      const { error } = await fetchCreateDigitalLens(submitData);
      if (error) return;
    }

    if (props.operateType === 'edit') {
      const { error } = await fetchUpdateDigitalLens(submitData);
      if (error) return;
    }
  } else {
    // 批量创建 - 创建多个相同数字镜像
    const baseDigitalLens = prepareDigitalLensData({ ...model });

    // 创建多个相同数字镜像
    const digitalLenses = [];
    for (let i = 0; i < batchCount.value; i++) {
      // 每次创建新的副本，以确保ID不冲突
      const lensCopy = JSON.parse(JSON.stringify(baseDigitalLens));
      // 修改标题，加上序号
      lensCopy.title = `${lensCopy.title}`;
      digitalLenses.push(lensCopy);
    }

    try {
      // 批量提交
      // console.log('批量提交数据:', digitalLenses);
      const { error } = await fetchBatchCreateDigitalLens(digitalLenses);
      if (error) {
        window.$message?.error('批量提交数字镜像失败');
        return;
      }

      window.$message?.success(`成功批量创建 ${digitalLenses.length} 个相同数字镜像`);
    } catch (error) {
      // console.error('批量创建失败:', error);
      window.$message?.error('批量提交数字镜像失败，请检查控制台错误');
      return;
    }
  }

  window.$message?.success($t('common.updateSuccess'));

  // 完全重置表单数据和状态
  resetFormCompletely();

  closeDrawer();
  emit('submitted');
}

// 处理"下一步"按钮点击 - 保存当前数字镜像并清空表单以添加下一个不同的数字镜像
function handleNextDigitalLens() {
  validate()
    .then(() => {
      // 克隆当前表单数据
      const clonedModel = JSON.parse(JSON.stringify(model));

      // 准备好JSON数据
      const preparedLens = prepareDigitalLensData(clonedModel);

      // 将准备好的数字镜像添加到多数字镜像列表
      multiLensList.value.push(preparedLens);

      // 显示成功消息，指明当前添加了几个数字镜像
      window.$message?.success(`已添加数字镜像 #${multiLensList.value.length}，可继续添加下一个数字镜像`);

      // 完全重置表单以输入下一个数字镜像
      resetFormCompletely();
    })
    .catch(errors => {
      // console.error('表单验证失败:', errors);
    });
}

// 处理"提交所有数字镜像"按钮点击 - 提交多个不同的数字镜像
function handleSubmitAllLenses() {
  validate()
    .then(() => {
      // 如果当前表单有内容，先添加到列表中
      if (model.title || model.characterVideo || model.clonedVoice) {
        // 克隆当前表单数据
        const clonedModel = JSON.parse(JSON.stringify(model));

        // 准备好JSON数据
        const preparedLens = prepareDigitalLensData(clonedModel);

        // 添加到多数字镜像列表
        multiLensList.value.push(preparedLens);
      }

      // 检查是否有数字镜像可提交
      if (multiLensList.value.length === 0) {
        window.$message?.warning('没有数字镜像可提交');
        return;
      }

      // console.log('批量提交不同数字镜像数据:', multiLensList.value);

      // 批量提交所有数字镜像
      fetchBatchCreateDigitalLens(multiLensList.value)
        .then(res => {
          // console.log('批量创建响应:', res);
          const { error } = res || {};

          if (error) {
            window.$message?.error('批量提交数字镜像失败');
            return;
          }

          window.$message?.success(`成功提交 ${multiLensList.value.length} 个不同数字镜像`);
          multiLensList.value = []; // 清空数字镜像列表

          // 完全重置表单数据和状态
          resetFormCompletely();

          closeDrawer();
          emit('submitted');
        })
        .catch(err => {
          // console.error('批量提交数字镜像出错:', err);
          window.$message?.error('批量提交数字镜像失败');
        });
    })
    .catch(errors => {
      // console.error('表单验证失败:', errors);
    });
}

// 处理人物视频上传
function handleVideoUpload({ file, onFinish, onError }: any) {
  // 设置上传状态
  isVideoUploading.value = true;

  // 显示上传中提示
  window.$message?.loading('正在上传视频文件...', { duration: 0 });

  // 获取当前token
  const token = getToken();
  const clientId = import.meta.env.VITE_APP_CLIENT_ID || '';

  // 上传请求
  fetchUploadDigitalLensVideo(file.file, token, clientId)
    .then(res => {
      // 关闭加载提示
      window.$message?.destroyAll();

      if (res.code === 200) {
        // 成功获取到MinIO的URL
        const url = res.data?.url || res.data || res.url;

        if (!url) {
          window.$message?.error('上传成功但未获取到文件URL');
          onError();
          return;
        }

        // 将URL存入模型中
        model.characterVideo = [url];

        window.$message?.success('视频上传成功');
        onFinish();
        // console.log('视频上传成功，MinIO URL:', url);
      } else {
        window.$message?.error(res.msg || '视频上传失败');
        onError();
      }
    })
    .catch(error => {
      // console.error('视频上传失败:', error);
      window.$message?.destroyAll();
      window.$message?.error(`视频上传失败: ${error.message}`);
      onError();
    })
    .finally(() => {
      // 重置上传状态
      isVideoUploading.value = false;
    });
}

// 处理克隆音色上传
function handleAudioUpload({ file, onFinish, onError }: any) {
  // 设置上传状态
  isAudioUploading.value = true;

  // 显示上传中提示
  window.$message?.loading('正在上传音频文件...', { duration: 0 });

  // 获取当前token
  const token = getToken();
  const clientId = import.meta.env.VITE_APP_CLIENT_ID || '';

  // 上传请求
  fetchUploadDigitalLensAudio(file.file, token, clientId)
    .then(res => {
      // 关闭加载提示
      window.$message?.destroyAll();

      if (res.code === 200) {
        // 成功获取到MinIO的URL
        const url = res.data?.url || res.data || res.url;

        if (!url) {
          window.$message?.error('上传成功但未获取到文件URL');
          onError();
          return;
        }

        // 将URL存入模型中
        model.clonedVoice = [url];

        window.$message?.success('音频上传成功');
        onFinish();
        // console.log('音频上传成功，MinIO URL:', url);
      } else {
        window.$message?.error(res.msg || '音频上传失败');
        onError();
      }
    })
    .catch(error => {
      // console.error('音频上传失败:', error);
      window.$message?.destroyAll();
      window.$message?.error(`音频上传失败: ${error.message}`);
      onError();
    })
    .finally(() => {
      // 重置上传状态
      isAudioUploading.value = false;
    });
}

// 处理其他素材上传
// function handleOtherMaterialsUpload({ file, onFinish, onError }: any) {
//   // 显示上传中提示
//   window.$message?.loading('正在上传素材文件...', { duration: 0 });
//
//   // 获取当前token
//   const token = getToken();
//   const clientId = import.meta.env.VITE_APP_CLIENT_ID || 'e5cd7e4891bf95d1d19206ce24a7b32e';
//
//   // 上传请求
//   fetchUploadDigitalLensMaterial(file.file, token, clientId)
//     .then(res => {
//       // 关闭加载提示
//       window.$message?.destroyAll();
//
//       if (res.code === 200) {
//         // 成功获取到MinIO的URL
//         const url = res.data?.url || res.data || res.url;
//
//         if (!url) {
//           window.$message?.error('上传成功但未获取到文件URL');
//           onError();
//           return;
//         }
//
//         // 将URL存入模型中 - 处理JSON数组格式
//         try {
//           let otherMaterials = [];
//           if (model.otherMaterials) {
//             // 尝试解析现有的值，可能是JSON字符串或普通字符串
//             try {
//               if (typeof model.otherMaterials === 'string') {
//                 if (model.otherMaterials.startsWith('[')) {
//                   // 已经是JSON字符串
//                   otherMaterials = JSON.parse(model.otherMaterials);
//                 } else if (model.otherMaterials.includes(',')) {
//                   // 逗号分隔的字符串
//                   otherMaterials = model.otherMaterials.split(',');
//                 } else {
//                   // 单个URL
//                   otherMaterials = [model.otherMaterials];
//                 }
//               } else if (Array.isArray(model.otherMaterials)) {
//                 // 已经是数组
//                 otherMaterials = [...model.otherMaterials];
//               }
//             } catch (e) {
//               // console.error('解析其他素材失败:', e);
//               otherMaterials = [];
//             }
//           }
//
//           // 添加新URL
//           otherMaterials.push(url);
//
//           // 更新模型
//           model.otherMaterials = otherMaterials;
//         } catch (e) {
//           // console.error('处理其他素材失败:', e);
//           // 如果处理失败，直接设置为单个URL
//           model.otherMaterials = [url];
//         }
//
//         window.$message?.success('素材上传成功');
//         onFinish();
//         // console.log('素材上传成功，MinIO URL:', url);
//       } else {
//         window.$message?.error(res.msg || '素材上传失败');
//         onError();
//       }
//     })
//     .catch(error => {
//       // console.error('素材上传失败:', error);
//       window.$message?.destroyAll();
//       window.$message?.error(`素材上传失败: ${error.message}`);
//       onError();
//     });
// }

// 删除视频文件
function handleRemoveVideo(file: any) {
  model.characterVideo = [];
  window.$message?.success('已删除视频文件');
}

// 删除音频文件
function handleRemoveAudio(file: any) {
  model.clonedVoice = [];
  window.$message?.success('已删除音频文件');
}


watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <div>
    <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
      <NDrawerContent :title="title" :native-scrollbar="false" closable>
        <NForm ref="formRef" :model="model" :rules="rules">
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="form-section-title">
              基本信息
              <NTooltip trigger="hover" placement="top">
                <template #trigger>
                  <NIcon size="16" class="ml-1 cursor-help text-gray-400">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                      <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                  </NIcon>
                </template>
                <div class="tooltip-content">
                  <p>填写数字镜像的基本信息，包括标题等。</p>
                  <p>可以使用AI辅助功能自动生成标题。</p>
                </div>
              </NTooltip>
            </div>
            <NFormItem path="title" :show-label="false">
              <div class="title-input-wrapper">
                <div class="title-label-with-button">
                  <span class="title-label">标题</span>
                  <NButton
                    size="small"
                    type="primary"
                    :disabled="!model.title || !model.title.trim()"
                    class="ai-polish-btn"
                    @click="handleAIPolishClick('title')"
                  >
                    <template #icon>
                      <NIcon>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                          <path d="M2 17l10 5 10-5"></path>
                          <path d="M2 12l10 5 10-5"></path>
                        </svg>
                      </NIcon>
                    </template>
                    AI润色
                  </NButton>
                </div>
                <NInput
                  v-model:value="model.title"
                  placeholder="请输入标题，可以根据你输入的标题进行AI辅助，生成一份更有助于您的标题"
                  class="title-input"
                />
              </div>
            </NFormItem>
          </div>

          <div class="form-section">
            <div class="form-section-title">
              素材上传
              <NTooltip trigger="hover" placement="top">
                <template #trigger>
                  <NIcon size="16" class="ml-1 cursor-help text-gray-400">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                      <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                  </NIcon>
                </template>
                <div class="tooltip-content">
                  <p class="tooltip-subtitle">素材说明：</p>
                  <ul class="tooltip-list">
                    <li>
                      <strong>人物视频：</strong>
                      支持mp4、avi等常见视频格式，用于数字人形象
                    </li>
                    <li>
                      <strong>克隆音色配置：</strong>
                      包含音频文件和对应的参考文本，两者需要内容匹配
                    </li>
                  </ul>
                  <p class="tooltip-subtitle">配置要求：</p>
                  <ul class="tooltip-list">
                    <li>人物视频只能上传1个文件</li>
                    <li>克隆音色需要上传音频文件并输入对应的参考文本</li>
                    <li>音频支持mp3、wav、ogg等格式</li>
                    <li>参考文本内容需要与音频内容一致，以获得最佳克隆效果</li>
                  </ul>
                </div>
              </NTooltip>
            </div>

            <!-- 数字人素材配置区域 - 三者一体 -->
            <div class="digital-human-section">
              <!-- 分组标题和说明 -->
              <div class="section-header">
                <div class="section-title-wrapper">
                  <h3 class="section-title">
                    <NIcon size="18" class="section-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </NIcon>
                    数字人形象设置
                  </h3>
                  <NTooltip trigger="hover" placement="top">
                    <template #trigger>
                      <NIcon size="16" class="ml-2 cursor-help text-gray-400">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                          <line x1="12" y1="17" x2="12.01" y2="17"></line>
                        </svg>
                      </NIcon>
                    </template>
                    <div class="tooltip-content">
                      <p class="tooltip-title">数字人素材配置说明</p>
                      <ul class="tooltip-list">
                        <li><strong>人物视频：</strong>数字人的形象来源，支持mp4、avi等格式</li>
                        <li><strong>克隆音色：</strong>用于克隆的音频样本，支持mp3、wav等格式</li>
                        <li><strong>参考文本：</strong>与音频内容对应的文本，用于音色学习</li>
                        <li><strong>配套使用：</strong>三者配合使用，创建完整的数字人形象</li>
                      </ul>
                    </div>
                  </NTooltip>
                </div>
                <div class="section-description">
                  上传数字人形象视频、音频文件并输入对应的参考文本，三者配套使用创建数字人形象
                </div>
              </div>

              <!-- 三个素材的配置区域 -->
              <div class="digital-human-content">
                <!-- 第一行：人物视频 -->
                <div class="material-row">
                  <NFormItem label="数字人形象" path="characterVideo">
                    <div class="enhanced-upload-container">
                      <NUpload
                        :key="`video-upload-${uploadKey}`"
                        ref="videoUploadRef"
                        v-model:value="model.characterVideo"
                        :custom-request="handleVideoUpload"
                        :accept="videoFormats"
                        :max="1"
                        :show-file-list="false"
                        @remove="handleRemoveVideo"
                      >
                        <div class="enhanced-upload-trigger video-upload" :class="{ uploading: isVideoUploading }">
                          <!-- 上传状态遮罩 -->
                          <div v-if="isVideoUploading" class="upload-loading-overlay">
                            <NSpin size="large">
                              <template #description>
                                <span class="upload-loading-text">正在上传视频...</span>
                              </template>
                            </NSpin>
                          </div>

                          <div class="upload-icon-wrapper">
                            <div class="upload-icon-bg">
                              <NIcon size="22" class="upload-icon">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  stroke-width="2"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                >
                                  <polygon points="23 7 16 12 23 17 23 7"></polygon>
                                  <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                                </svg>
                              </NIcon>
                            </div>
                          </div>
                          <div class="upload-content">
                            <h4 class="upload-title">上传视频</h4>
                            <p class="upload-description">支持mp4/avi/mov等格式</p>
                            <div class="upload-formats">支持 MP4、AVI、MOV 等格式</div>
                          </div>
                          <div class="upload-action">
                            <NButton type="primary" size="small" ghost :disabled="isVideoUploading">
                              <template #icon>
                                <NIcon>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                  >
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17,8 12,3 7,8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                  </svg>
                                </NIcon>
                              </template>
                              选择文件
                            </NButton>
                          </div>
                        </div>
                      </NUpload>

                      <!-- 文件预览按钮 -->
                      <div
                        v-if="model.characterVideo && model.characterVideo.length > 0"
                        class="file-preview-trigger"
                        @click="handlePreview(model.characterVideo, 'video')"
                      >
                        <div class="preview-trigger-content">
                          <NIcon size="16" class="preview-trigger-icon">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                              <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                          </NIcon>
                          <span class="preview-trigger-text">查看已上传的视频文件</span>
                          <NIcon size="14" class="preview-trigger-arrow">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                          </NIcon>
                        </div>
                      </div>
                    </div>
                  </NFormItem>
                </div>

                <!-- 第二行：克隆音色和参考文本的配对区域 -->
                <div class="voice-clone-pair">
                  <div class="pair-header">
                    <div class="pair-title">
                      <NIcon size="16" class="pair-icon">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                          <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                          <line x1="12" y1="19" x2="12" y2="23"></line>
                          <line x1="8" y1="23" x2="16" y2="23"></line>
                        </svg>
                      </NIcon>
                      <span>克隆音色配置</span>
                    </div>
                    <div class="pair-description">上传音频文件并输入音色文件对应的文本内容，两者需要内容匹配</div>
                  </div>

                  <NGrid :cols="2" :x-gap="20">
                    <!-- 克隆音色上传 -->
                    <NGi>
                      <NFormItem label="克隆音色" path="clonedVoice">
                        <div class="enhanced-upload-container">
                          <NUpload
                            :key="`audio-upload-${uploadKey}`"
                            ref="audioUploadRef"
                            v-model:value="model.clonedVoice"
                            :custom-request="handleAudioUpload"
                            :accept="audioFormats"
                            :max="1"
                            :show-file-list="false"
                            @remove="handleRemoveAudio"
                          >
                            <div class="enhanced-upload-trigger audio-upload" :class="{ uploading: isAudioUploading }">
                              <!-- 上传状态遮罩 -->
                              <div v-if="isAudioUploading" class="upload-loading-overlay">
                                <NSpin size="large">
                                  <template #description>
                                    <span class="upload-loading-text">正在上传音频...</span>
                                  </template>
                                </NSpin>
                              </div>

                              <div class="upload-icon-wrapper">
                                <div class="upload-icon-bg">
                                  <NIcon size="22" class="upload-icon">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      stroke-width="2"
                                      stroke-linecap="round"
                                      stroke-linejoin="round"
                                    >
                                      <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                      <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                      <line x1="12" y1="19" x2="12" y2="23"></line>
                                      <line x1="8" y1="23" x2="16" y2="23"></line>
                                    </svg>
                                  </NIcon>
                                </div>
                              </div>
                              <div class="upload-content">
                                <h4 class="upload-title">上传音频</h4>
                                <p class="upload-description">支持mp3/wav/ogg等格式</p>
                                <div class="upload-formats">支持 MP3、WAV、OGG 等格式</div>
                              </div>
                              <div class="upload-action">
                                <NButton type="primary" size="small" ghost :disabled="isAudioUploading">
                                  <template #icon>
                                    <NIcon>
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                      >
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17,8 12,3 7,8"></polyline>
                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                      </svg>
                                    </NIcon>
                                  </template>
                                  选择文件
                                </NButton>
                              </div>
                            </div>
                          </NUpload>

                          <!-- 文件预览按钮 -->
                          <div
                            v-if="model.clonedVoice && model.clonedVoice.length > 0"
                            class="file-preview-trigger"
                            @click="handlePreview(model.clonedVoice, 'audio')"
                          >
                            <div class="preview-trigger-content">
                              <NIcon size="16" class="preview-trigger-icon">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  stroke-width="2"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                >
                                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                  <circle cx="12" cy="12" r="3"></circle>
                                </svg>
                              </NIcon>
                              <span class="preview-trigger-text">查看已上传的音频文件</span>
                              <NIcon size="14" class="preview-trigger-arrow">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  stroke-width="2"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                >
                                  <polyline points="9,18 15,12 9,6"></polyline>
                                </svg>
                              </NIcon>
                            </div>
                          </div>
                        </div>
                      </NFormItem>
                    </NGi>

                    <!-- 参考文本输入 -->
                    <NGi>
                      <NFormItem label="音色文件对应的文本内容" path="referenceText">
                        <div class="text-input-wrapper">
                          <div class="reference-text-container">
                            <NInput
                              v-model:value="model.referenceText"
                              type="textarea"
                              :autosize="{ minRows: 6, maxRows: 8 }"
                              placeholder="请输入与音频内容对应的文本内容..."
                              class="text-input reference-text-paired"
                            />
                            <div class="reference-text-hint">
                              <NIcon size="14" class="hint-icon">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  stroke-width="2"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                >
                                  <path d="M9 12l2 2 4-4"></path>
                                  <circle cx="12" cy="12" r="10"></circle>
                                </svg>
                              </NIcon>
                              <span class="hint-text">请确保文本内容与音频内容一致</span>
                            </div>
                          </div>
                        </div>
                      </NFormItem>
                    </NGi>
                  </NGrid>

                  <!-- 配对关系提示 -->
<!--                  <div class="minimal-indicator">-->
<!--                    <div class="minimal-line"></div>-->
<!--                    <div class="minimal-label">-->
<!--                      <span>分镜对应</span>-->
<!--                    </div>-->
<!--                  </div>-->





                </div>

                <!-- 整体配套关系提示 -->
                <div class="digital-human-indicator">
                  <div class="indicator-content">
                    <NIcon size="18" class="indicator-icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M9 12l2 2 4-4"></path>
                        <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c1.66 0 3.22.45 4.56 1.24"></path>
                      </svg>
                    </NIcon>
                    <span class="indicator-text">视频 + 音频 + 文本 = 完整数字人配置</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 文本设置 -->
          <div class="form-section">
            <div class="form-section-title">
              文本设置
              <NTooltip trigger="hover" placement="top">
                <template #trigger>
                  <NIcon size="16" class="ml-1 text-gray-400 cursor-help">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                      <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                  </NIcon>
                </template>
                <div class="tooltip-content">
                  <p class="tooltip-subtitle">文本类型说明：</p>
                  <ul class="tooltip-list">
                    <li><strong>参考文本：</strong>用于语音合成时的参考依据，帮助系统理解上下文</li>
                    <li><strong>字幕内容：</strong>实际要转换为语音的文本内容</li>
                  </ul>
                  <p class="tooltip-subtitle">使用说明：</p>
                  <ul class="tooltip-list">
                    <li>多段文本请用&&分隔</li>
                    <li>可以使用AI生成功能基于标题自动生成文本内容</li>
                  </ul>
                </div>
              </NTooltip>
            </div>

            <!-- 目标文本输入 -->
            <NFormItem path="targetText" :show-label="false">
              <div class="text-input-wrapper">
                <div class="text-label-with-button">
                  <span class="text-label">字幕内容</span>
                  <NButton
                    size="small"
                    type="primary"
                    :disabled="!model.title || !model.title.trim() || isGeneratingTargetText"
                    :loading="isGeneratingTargetText"
                    @click="handleAIPolishClick('targetText')"
                    class="ai-generate-btn"
                  >
                    <template #icon>
                      <NIcon>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                          <path d="M2 17l10 5 10-5"></path>
                          <path d="M2 12l10 5 10-5"></path>
                        </svg>
                      </NIcon>
                    </template>
                    {{ isGeneratingTargetText ? 'AI生成中...' : 'AI生成' }}
                  </NButton>
                </div>
                <NInput
                  v-model:value="model.targetText"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 6 }"
                  placeholder="请输入目标文本，用于语音合成输出。也可以点击'AI生成'基于标题自动生成"
                  class="text-input"
                />
              </div>
            </NFormItem>

            <!-- 字幕位置设置 -->
            <NFormItem label="字幕位置" path="subtitlePosition">
              <SubtitlePositionSelectorVisual
                v-model:modelValue="model.subtitlePosition"
                :video-url="getFirstVideoUrl()"
                :subtitle-text="model.targetText || '预览字幕'"
                :task-id="model.taskId"
                :task-name="model.title"
              />
            </NFormItem>
          </div>

          <!-- 其他视频素材上传 -->
          <NFormItem label="其他视频素材" path="otherVideoMaterials">
            <VideoMaterialsManager v-model:materials="model.otherVideoMaterials" :video-formats="videoFormats" />
            <template #label>
              <div class="flex items-center">
                <span>商品视频素材</span>
                <NTooltip trigger="hover" placement="top">
                  <template #trigger>
                    <NIcon size="16" class="ml-1 cursor-help text-gray-400">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                      </svg>
                    </NIcon>
                  </template>
                  上传与数字镜像相关的其他视频素材，如背景视频、特效视频等
                </NTooltip>
              </div>
            </template>
          </NFormItem>

          <!-- 其他音频素材上传 -->
          <NFormItem label="其他音频素材" path="otherAudioMaterials">
            <AudioMaterialsManager
              v-model:materials="model.otherAudioMaterials"
              :audio-formats="audioFormats"
            />
            <template #label>
              <div class="flex items-center">
                <span>背景音频素材</span>
                <NTooltip trigger="hover" placement="top">
                  <template #trigger>
                    <NIcon size="16" class="ml-1 cursor-help text-gray-400">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                      </svg>
                    </NIcon>
                  </template>
                  上传与数字镜像相关的其他音频素材，如背景音乐、音效等
                </NTooltip>
              </div>
            </template>
          </NFormItem>
        </NForm>
        <template #footer>
          <div class="flex items-center justify-end gap-x-4 px-4 pb-4">
            <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>

            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-500">批量数量:</span>
              <NInputNumber v-model:value="batchCount" :min="1" :max="20" class="w-16" />
            </div>

            <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
            <NButton type="success" @click="handleNextDigitalLens">批量生产不同的</NButton>
            <NButton v-if="multiLensList.length > 0" type="warning" @click="handleSubmitAllLenses">
              提交全部({{ multiLensList.length + (model.title ? 1 : 0) }})
            </NButton>
          </div>
        </template>
      </NDrawerContent>
    </NDrawer>

    <!-- 添加标题润色抽屉 -->
    <TitlePolishDrawer
      v-model:visible="titlePolishVisible"
      :original-title="currentTitle"
      @select="handleSelectPolishedTitle"
    />

    <!-- 预览模态框 -->
    <MediaPreviewModal v-model:visible="previewVisible" :url="previewUrl" :type="previewType" />
  </div>
</template>

<style scoped>
.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px dashed #e5e7eb;
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

/* 标题输入样式 */
.title-input-wrapper {
  width: 100%;
}

.title-label-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.ai-polish-btn {
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}

.title-input {
  width: 100%;
}

/* 文本输入样式 */
.text-input-wrapper {
  width: 100%;
}

.text-label-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.text-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.text-input {
  width: 100%;
}

.text-label-with-tooltip {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.media-upload-container {
  width: 100%;
}

.media-upload-actions {
  margin-bottom: 8px;
}

.media-upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.media-upload-trigger:hover {
  border-color: #1890ff;
}

.media-upload-icon {
  margin-bottom: 8px;
  color: #8c8c8c;
}

.media-upload-text {
  color: #262626;
  font-size: 14px;
  margin-bottom: 4px;
}

.media-upload-hint {
  color: #8c8c8c;
  font-size: 12px;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.flex-1 {
  flex: 1;
}

.cursor-help {
  cursor: help;
}

.text-gray-400 {
  color: #9ca3af;
}

.reference-text-input,
.target-text-input {
  min-height: 120px;
  width: 100%;
}

.w-full {
  width: 100%;
}

.materials-group-wrapper {
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.materials-group-wrapper-outer {
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 增强版上传组件样式 */
:deep(.n-upload) {
  width: 100%;
}

:deep(.n-upload-trigger) {
  width: 100% !important;
  height: auto !important;
  min-height: 100px;
  border: 2px dashed #e2e8f0 !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
  transition: all 0.3s ease !important;
}

:deep(.n-upload-trigger:hover) {
  border-color: #667eea !important;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15) !important;
}

.enhanced-upload-container {
  position: relative;
  width: 100%;
}

.enhanced-upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  transition: all 0.3s ease;
  padding: 24px 16px;
  min-height: 120px;
  position: relative;
  overflow: hidden;
}

.enhanced-upload-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 150, 255, 0.02) 0%, rgba(64, 150, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-upload-trigger:hover {
  border-color: #4096ff;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 150, 255, 0.15);
}

.enhanced-upload-trigger:hover::before {
  opacity: 1;
}

.enhanced-upload-trigger.video-upload:hover {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
  box-shadow: 0 8px 25px rgba(82, 196, 26, 0.15);
}

.enhanced-upload-trigger.audio-upload:hover {
  border-color: #fa8c16;
  background: linear-gradient(135deg, #fff7e6 0%, #fef5e7 100%);
  box-shadow: 0 8px 25px rgba(250, 140, 22, 0.15);
}

.upload-icon-wrapper {
  position: relative;
  z-index: 1;
  margin-bottom: 12px;
}

.upload-icon-bg {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4096ff 0%, #1677ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.3);
}

.video-upload .upload-icon-bg {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.audio-upload .upload-icon-bg {
  background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
  box-shadow: 0 4px 12px rgba(250, 140, 22, 0.3);
}

.enhanced-upload-trigger:hover .upload-icon-bg {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(64, 150, 255, 0.4);
}

.video-upload:hover .upload-icon-bg {
  box-shadow: 0 6px 20px rgba(82, 196, 26, 0.4);
}

.audio-upload:hover .upload-icon-bg {
  box-shadow: 0 6px 20px rgba(250, 140, 22, 0.4);
}

.upload-icon {
  color: white;
}

.upload-content {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 12px;
}

.upload-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 4px 0;
}

.upload-description {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0 0 8px 0;
}

.upload-formats {
  font-size: 12px;
  color: #bfbfbf;
  margin: 0;
}

.upload-action {
  position: relative;
  z-index: 1;
}

.upload-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 12px;
}

.upload-loading-text {
  color: #4096ff;
  font-weight: 500;
  margin-top: 8px;
}

.enhanced-upload-trigger.uploading {
  pointer-events: none;
  opacity: 0.8;
}

.enhanced-upload-trigger.uploading .upload-icon-bg {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 150, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(64, 150, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(64, 150, 255, 0.3);
  }
}

/* 文件预览按钮样式 */
.file-preview-trigger {
  margin-top: 12px;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-preview-trigger:hover {
  border-color: #4096ff;
  background: #f0f7ff;
}

.preview-trigger-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview-trigger-icon {
  color: #4096ff;
  margin-right: 8px;
}

.preview-trigger-text {
  flex: 1;
  font-size: 14px;
  color: #595959;
}

.preview-trigger-arrow {
  color: #bfbfbf;
}

/* 工具提示样式 */
.tooltip-content {
  max-width: 400px;
  padding: 8px 0;
}

.tooltip-subtitle {
  font-weight: 600;
  margin: 8px 0 4px 0;
  color: #333;
}

.tooltip-list {
  margin: 0 0 12px 0;
  padding-left: 16px;
}

.tooltip-list li {
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 1.4;
}

/* 数字人素材配置区域样式 */
.digital-human-section {
  margin-top: 24px;
  padding: 24px;
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  margin-bottom: 24px;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.section-icon {
  margin-right: 8px;
  color: #4096ff;
}

.section-description {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.5;
}

.digital-human-content {
  position: relative;
}

/* 素材行样式 */
.material-row {
  margin-bottom: 24px;
}

/* 音色配对区域样式 */
.voice-clone-pair {
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #ffffff;
  position: relative;
  margin-bottom: 20px;
}

.pair-header {
  margin-bottom: 16px;
}

.pair-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 6px;
}

.pair-icon {
  margin-right: 8px;
  color: #4096ff;
}

.pair-description {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 参考文本配对样式 */
.reference-text-container {
  position: relative;
}

.reference-text-paired {
  width: 100% !important;
}

.reference-text-hint {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 6px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  font-size: 12px;
}

.hint-icon {
  margin-right: 6px;
  color: #52c41a;
}

.hint-text {
  color: #389e0d;
}

/* 配对关系指示器 */
.pairing-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  pointer-events: none;
}

.pairing-line {
  position: absolute;
  top: 50%;
  left: -60px;
  right: -60px;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #4096ff 20%, #4096ff 80%, transparent 100%);
  transform: translateY(-50%);
}

.pairing-badge {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: #4096ff;
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(64, 150, 255, 0.3);
  white-space: nowrap;
}

.pairing-icon {
  margin-right: 4px;
}

.pairing-text {
  font-size: 12px;
}

/* 参考文本横屏占满样式 */
.reference-text-full-width {
  width: 100% !important;
}

.reference-text-full-width .n-input__textarea {
  resize: vertical;
}

/* 数字人整体配套关系提示样式 */
.digital-human-indicator {
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 1px solid #91d5ff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.indicator-content {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.indicator-icon {
  margin-right: 8px;
  color: #52c41a;
}

.indicator-text {
  white-space: nowrap;
}

/* 增强上传容器样式 - 与一键成片保持一致 */
:deep(.enhanced-upload-container .n-upload-file-list) {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

:deep(.enhanced-upload-container .n-upload-file-list::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

:deep(.enhanced-upload-container .n-upload-file) {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 12px;
}

:deep(.enhanced-upload-container .n-upload-file::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: transparent;
  transition: all 0.3s ease;
}

:deep(.enhanced-upload-container .n-upload-file:hover) {
  border-color: #667eea;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
  transform: translateY(-3px);
}

:deep(.enhanced-upload-container .n-upload-file--finished) {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #ffffff 100%);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.2);
}

:deep(.enhanced-upload-container .n-upload-file--finished::before) {
  background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
}

:deep(.enhanced-upload-container .n-upload-file-info) {
  padding: 16px 20px;
}

:deep(.enhanced-upload-container .n-upload-file-info__name) {
  font-weight: 600;
  color: #2d3748;
  font-size: 15px;
  line-height: 1.4;
}

</style>
