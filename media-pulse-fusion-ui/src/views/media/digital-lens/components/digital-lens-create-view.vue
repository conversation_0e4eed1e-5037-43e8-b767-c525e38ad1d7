<script setup lang="ts">
import { ref } from 'vue';
import { NCard, NButton, NIcon, NGrid, NGridItem } from 'naive-ui';
// @ts-ignore - This component uses <script setup> without default export
import DigitalLensOperateDrawer from '../modules/digital-lens-operate-drawer.vue';

defineOptions({
  name: 'DigitalLensCreateView'
});

interface Emits {
  (e: 'lens-created'): void;
}

const emit = defineEmits<Emits>();

// 抽屉状态
const drawerVisible = ref(false);
const operateType = ref<'add' | 'edit'>('add');

// 功能特性列表
const features = [
  { name: '人物视频', description: '上传数字人物视频素材，支持多种格式' },
  { name: '音色克隆', description: '上传音频文件进行音色克隆训练' },
  { name: '文本配置', description: '设置参考文本和目标文本内容' },
  { name: '素材管理', description: '管理视频、音频等多媒体素材' },
  { name: '预览功能', description: '支持在线预览各种媒体文件' },
  { name: '批量操作', description: '支持批量上传和管理素材文件' }
];

// 打开创建抽屉
function openCreateDrawer() {
  operateType.value = 'add';
  drawerVisible.value = true;
}

// 数字镜像创建成功回调
function handleLensSubmitted() {
  drawerVisible.value = false;
  emit('lens-created');
}
</script>

<template>
  <div class="digital-lens-create-view">
    <!-- 创建数字镜像主卡片 -->
    <NCard class="create-main-card">
      <div class="create-content">
        <div class="create-header">
          <div class="create-icon">
            <NIcon size="64">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </NIcon>
          </div>
          <h2 class="create-title">创建数字镜像</h2>
          <p class="create-description">
            数字镜像是AI生成的基础，通过上传人物视频、音色文件和配置文本，创建完整的数字人物模型，为后续的视频生成提供素材支持
          </p>
          <NButton
            type="primary"
            size="large"
            class="create-button"
            @click="openCreateDrawer"
          >
            <template #icon>
              <NIcon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
              </NIcon>
            </template>
            开始创建数字镜像
          </NButton>
        </div>

        <!-- 功能特性展示 -->
        <div class="features-section">
          <h3 class="features-title">平台功能特性</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16" class="features-grid">
            <NGridItem v-for="(feature, index) in features" :key="feature.name">
              <div class="feature-item" :class="`feature-item-${index + 1}`">
                <div class="feature-icon-wrapper">
                  <div class="feature-icon">
                    <NIcon size="24">
                      <svg
                        v-if="index === 0"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M23 7l-7 5 7 5V7z"></path>
                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                      </svg>
                      <svg v-else-if="index === 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                        <line x1="12" y1="19" x2="12" y2="23"></line>
                        <line x1="8" y1="23" x2="16" y2="23"></line>
                      </svg>
                      <svg v-else-if="index === 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10,9 9,9 8,9"></polyline>
                      </svg>
                      <svg v-else-if="index === 3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <rect x="7" y="7" width="3" height="9"></rect>
                        <rect x="14" y="7" width="3" height="5"></rect>
                      </svg>
                      <svg v-else-if="index === 4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                      <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                        <path d="M9 14l2 2 4-4"></path>
                      </svg>
                    </NIcon>
                  </div>
                </div>
                <div class="feature-content">
                  <div class="feature-name">{{ feature.name }}</div>
                  <div class="feature-desc">{{ feature.description }}</div>
                </div>
                <div class="feature-decoration"></div>
              </div>
            </NGridItem>
          </NGrid>
        </div>
      </div>
    </NCard>

    <!-- 使用指南 -->
    <NCard title="使用指南" class="guide-card">
      <div class="guide-content">
        <div class="guide-step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>填写基本信息</h4>
            <p>为数字镜像设置标题和描述信息</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>上传人物视频</h4>
            <p>上传清晰的人物视频文件，建议时长3-10分钟</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>上传音色文件</h4>
            <p>上传音频文件用于音色克隆，建议时长1-5分钟</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>配置文本内容</h4>
            <p>设置参考文本和目标文本，用于语音合成</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">5</div>
          <div class="step-content">
            <h4>上传辅助素材</h4>
            <p>可选择上传其他视频或音频素材作为补充</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="step-number">6</div>
          <div class="step-content">
            <h4>保存并预览</h4>
            <p>保存数字镜像并预览各项素材内容</p>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 数字镜像操作抽屉 -->
    <DigitalLensOperateDrawer
      :visible="drawerVisible"
      :operate-type="operateType"
      @update:visible="drawerVisible = $event"
      @submitted="handleLensSubmitted"
    />
  </div>
</template>

<style scoped>
.digital-lens-create-view {
  padding: 0;
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.create-main-card {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.create-content {
  text-align: center;
  padding: 48px 32px;
}

.create-header {
  margin-bottom: 48px;
}

.create-icon {
  color: #f093fb;
  margin-bottom: 24px;
  filter: drop-shadow(0 4px 8px rgba(240, 147, 251, 0.2));
}

.create-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 20px 0;
  color: #1a202c;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #667eea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.create-description {
  font-size: 18px;
  color: #4a5568;
  margin: 0 0 40px 0;
  line-height: 1.7;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
}

.create-button {
  font-size: 18px;
  font-weight: 600;
  padding: 16px 48px;
  height: auto;
  border-radius: 12px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(240, 147, 251, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.create-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.create-button:hover::before {
  left: 100%;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(240, 147, 251, 0.4);
}

.features-section {
  text-align: left;
  margin-top: 24px;
}

.features-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 32px 0;
  color: #2d3748;
  text-align: center;
  position: relative;
}

.features-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  border-radius: 2px;
}

.features-grid {
  margin-top: 24px;
}

.feature-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 0;
  border: 1px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 160px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.feature-item::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(240, 147, 251, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: all 0.6s ease;
  transform: scale(0);
}

.feature-item:hover::before {
  opacity: 1;
}

.feature-item:hover::after {
  opacity: 1;
  transform: scale(1);
}

.feature-item:hover {
  background: linear-gradient(135deg, #ffffff 0%, #fef7ff 100%);
  border-color: rgba(240, 147, 251, 0.3);
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(240, 147, 251, 0.15),
  0 8px 16px rgba(0, 0, 0, 0.1),
  inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.feature-icon-wrapper {
  padding: 20px 20px 0 20px;
  position: relative;
  z-index: 2;
}

.feature-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
  position: relative;
  overflow: hidden;
}

.feature-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.feature-item:hover .feature-icon::before {
  left: 100%;
}

.feature-item:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 20px rgba(240, 147, 251, 0.4);
}

.feature-content {
  padding: 16px 20px 20px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.feature-name {
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  font-size: 16px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.feature-item:hover .feature-name {
  transform: translateY(-1px);
}

.feature-desc {
  color: #4a5568;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
  flex: 1;
  transition: all 0.3s ease;
}

.feature-item:hover .feature-desc {
  color: #2d3748;
  transform: translateY(-1px);
}

.feature-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  transform: scaleX(0);
  transition: transform 0.4s ease;
  border-radius: 0 0 16px 16px;
}

.feature-item:hover .feature-decoration {
  transform: scaleX(1);
}

/* 为不同的功能特性添加独特的颜色主题 */
.feature-item-1:hover {
  border-color: rgba(102, 126, 234, 0.3);
}

.feature-item-1 .feature-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.feature-item-1:hover .feature-icon {
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.feature-item-1 .feature-decoration {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-item-2:hover {
  border-color: rgba(52, 211, 153, 0.3);
}

.feature-item-2 .feature-icon {
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  box-shadow: 0 4px 12px rgba(52, 211, 153, 0.3);
}

.feature-item-2:hover .feature-icon {
  box-shadow: 0 8px 20px rgba(52, 211, 153, 0.4);
}

.feature-item-2 .feature-decoration {
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
}

.feature-item-3:hover {
  border-color: rgba(251, 146, 60, 0.3);
}

.feature-item-3 .feature-icon {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
  box-shadow: 0 4px 12px rgba(251, 146, 60, 0.3);
}

.feature-item-3:hover .feature-icon {
  box-shadow: 0 8px 20px rgba(251, 146, 60, 0.4);
}

.feature-item-3 .feature-decoration {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
}

.guide-card {
  border-radius: 16px;
  margin-top: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.guide-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
  margin-top: 24px;
  padding: 32px;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.guide-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.guide-step:hover::before {
  transform: scaleY(1);
}

.guide-step:hover {
  background: linear-gradient(135deg, #ffffff 0%, #edf2f7 100%);
  border-color: #cbd5e0;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
}

.step-content h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.step-content p {
  margin: 0;
  font-size: 15px;
  color: #4a5568;
  line-height: 1.6;
  font-weight: 400;
}

@media (max-width: 768px) {
  .digital-lens-create-view {
    padding: 16px;
    gap: 24px;
  }

  .create-content {
    padding: 32px 24px;
  }

  .create-title {
    font-size: 28px;
  }

  .create-description {
    font-size: 16px;
  }

  .create-button {
    font-size: 16px;
    padding: 14px 36px;
  }

  .features-title {
    font-size: 20px;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .feature-item {
    height: 140px;
  }

  .feature-icon-wrapper {
    padding: 16px 16px 0 16px;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
  }

  .feature-content {
    padding: 12px 16px 16px 16px;
  }

  .feature-name {
    font-size: 15px;
    margin-bottom: 6px;
  }

  .feature-desc {
    font-size: 13px;
  }

  .guide-content {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 24px;
  }

  .guide-step {
    padding: 20px;
    gap: 16px;
  }

  .step-number {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .step-content h4 {
    font-size: 16px;
  }

  .step-content p {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .create-content {
    padding: 24px 16px;
  }

  .create-title {
    font-size: 24px;
  }

  .create-description {
    font-size: 15px;
  }

  .create-button {
    font-size: 15px;
    padding: 12px 28px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .feature-item {
    height: 120px;
  }

  .feature-icon-wrapper {
    padding: 12px 12px 0 12px;
  }

  .feature-icon {
    width: 36px;
    height: 36px;
  }

  .feature-content {
    padding: 8px 12px 12px 12px;
  }

  .feature-name {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .feature-desc {
    font-size: 12px;
    line-height: 1.4;
  }

  .guide-step {
    padding: 16px;
  }

  .guide-content {
    padding: 20px 16px;
  }
}
</style>
