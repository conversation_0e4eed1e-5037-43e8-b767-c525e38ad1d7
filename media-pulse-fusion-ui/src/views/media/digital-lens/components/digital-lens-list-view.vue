<script setup lang="tsx">
import { h } from 'vue';
import { NButton, NCard, NDataTable, NDivider, NSpace, NTag, NTooltip } from 'naive-ui';
import { fetchBatchDeleteDigitalLens, fetchGetDigitalLensList } from '@/service/api/media/digital-lens';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import DigitalLensOperateDrawer from '../modules/digital-lens-operate-drawer.vue';
import DigitalLensSearch from '../modules/digital-lens-search.vue';
import FilePreviewButton from '../../task/modules/file-preview-button.vue';

defineOptions({
  name: 'DigitalLensListView'
});

interface Emits {
  (e: 'lens-updated'): void;
}

const emit = defineEmits<Emits>();

const appStore = useAppStore();
const { hasAuth } = useAuth();
const { downloadByUrl } = useDownload();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetDigitalLensList,
  apiParams: {
    pageNum: 1,
    pageSize: 10
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: '序号',
      align: 'center',
      width: 64
    },
    {
      key: 'taskId',
      title: '任务ID',
      align: 'center',
      width: 120,
      ellipsis: {
        tooltip: true
      },
      render: row => {
        if (row.taskId) {
          return row.taskId;
        }
        return '-';
      }
    },
    {
      key: 'title',
      title: '标题',
      align: 'center',
      minWidth: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      key: 'characterVideo',
      title: '人物视频',
      align: 'center',
      width: 150,
      render: row => {
        if (row.characterVideo) {
          return h(FilePreviewButton, {
            files: row.characterVideo,
            type: 'video'
          });
        }
        return '-';
      }
    },
    {
      key: 'clonedVoice',
      title: '克隆音色',
      align: 'center',
      width: 150,
      render: row => {
        if (row.clonedVoice) {
          return h(FilePreviewButton, {
            files: row.clonedVoice,
            type: 'audio'
          });
        }
        return '-';
      }
    },
    {
      key: 'referenceText',
      title: '参考文本',
      align: 'center',
      minWidth: 120,
      ellipsis: {
        tooltip: true
      },
      render: row => {
        return row.referenceText || '-';
      }
    },
    {
      key: 'targetText',
      title: '目标文本',
      align: 'center',
      minWidth: 120,
      ellipsis: {
        tooltip: true
      },
      render: row => {
        return row.targetText || '-';
      }
    },
    {
      key: 'otherVideoMaterials',
      title: '其他视频素材',
      align: 'center',
      width: 150,
      render: row => {
        if (row.otherVideoMaterials) {
          return h(FilePreviewButton, {
            files: row.otherVideoMaterials,
            type: 'video'
          });
        }
        return '-';
      }
    },
    {
      key: 'otherAudioMaterials',
      title: '其他音频素材',
      align: 'center',
      width: 150,
      render: row => {
        if (row.otherAudioMaterials) {
          return h(FilePreviewButton, {
            files: row.otherAudioMaterials,
            type: 'audio'
          });
        }
        return '-';
      }
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const statusMap: Record<number, { text: string; type: any }> = {
          0: { text: '待处理', type: 'warning' },
          1: { text: '成功', type: 'success' },
          2: { text: '处理中', type: 'info' },
          3: { text: '失败', type: 'error' }
        };
        const statusInfo = statusMap[row.status] || { text: '未知', type: 'default' };
        return h(
          NTag,
          { type: statusInfo.type, size: 'small' },
          { default: () => statusInfo.text }
        );
      }
    },

    // {
    //   key: 'createTime',
    //   title: '创建时间',
    //   align: 'center',
    //   width: 180,
    //   render: row => {
    //     return row.createTime || '-';
    //   }
    // },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: row => {
        return (
          <NSpace justify={'center'}>
            <NTooltip>
              {{
                default: () => $t('common.edit'),
                trigger: () => (
                  <NButton
                    quaternary
                    size={'small'}
                    onClick={() => handleEdit(row.id)}
                    disabled={!hasAuth('media:digitalLens:edit')}
                  >
                    <ButtonIcon icon={'material-symbols:edit-outline'} />
                  </NButton>
                )
              }}
            </NTooltip>
            <NTooltip>
              {{
                default: () => $t('common.delete'),
                trigger: () => (
                  <NButton
                    quaternary
                    size={'small'}
                    type={'error'}
                    onClick={() => handleDelete(row.id)}
                    disabled={!hasAuth('media:digitalLens:remove')}
                  >
                    <ButtonIcon icon={'material-symbols:delete-outline'} />
                  </NButton>
                )
              }}
            </NTooltip>
          </NSpace>
        );
      }
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted
} = useTableOperate(data, getData);

// 处理删除
async function handleDelete(id: string) {
  await onBatchDeleted([id], fetchBatchDeleteDigitalLens, '数字镜像');
  emit('lens-updated');
}

// 处理批量删除
async function handleBatchDelete() {
  await onBatchDeleted(checkedRowKeys.value, fetchBatchDeleteDigitalLens, '数字镜像');
  emit('lens-updated');
}

// 处理导出
function handleExport() {
  downloadByUrl('/dev-api/media/digitalLens/export', '数字镜像列表.xlsx');
}

// 处理数字镜像提交成功
function handleLensSubmitted() {
  getData();
  emit('lens-updated');
}
</script>

<template>
  <div class="digital-lens-list-view">
    <NCard title="数字镜像列表" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <div class="header-operations">
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            :show-add="hasAuth('media:digitalLens:add')"
            :show-delete="hasAuth('media:digitalLens:remove')"
            :show-export="hasAuth('media:digitalLens:export')"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @export="handleExport"
            @refresh="getData"
          />
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-section">
        <DigitalLensSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
      </div>

      <NDivider style="margin: 12px 0;" />

      <!-- 数据表格 -->
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="1200"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
      />
    </NCard>

    <!-- 数字镜像操作抽屉 -->
    <DigitalLensOperateDrawer
      :visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @update:visible="drawerVisible = $event"
      @submitted="handleLensSubmitted"
    />
  </div>
</template>

<style scoped>
.digital-lens-list-view {
  padding: 16px;
}

.header-operations {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-section {
  margin-bottom: 16px;
}

.card-wrapper {
  display: flex;
  flex-direction: column;
}

.card-wrapper :deep(.n-card__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
