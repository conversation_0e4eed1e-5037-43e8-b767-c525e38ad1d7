/**
 * namespace Points
 *
 * backend api module: "Points"
 */
namespace Points {
  /** points wallet */
  type PointsWallet = Common.CommonRecord<{
    /** 主键ID */
    id: CommonType.IdType;
    /** 租户编号 */
    tenantId: CommonType.IdType;
    /** 用户ID */
    userId: CommonType.IdType;
    /** 余额 */
    balance: number;
    /** 冻结金额 */
    frozenAmount: number;
    /** 累计充值 */
    totalRecharge: number;
    /** 累计消费 */
    totalConsume: number;
    /** 积分余额 */
    pointsBalance: number;
    /** 累计获得积分 */
    totalPoints: number;
    /** 累计使用积分 */
    usedPoints: number;
    /** 支付密码 */
    payPassword: string;
    /** 是否设置支付密码（0否 1是） */
    isPayPwdSet: string;
    /** 钱包状态（0正常 1冻结 2注销） */
    status: string;
    /** 最后交易时间 */
    lastTradeTime: string;
    /** 版本号（乐观锁） */
    version: number;
    /** 删除标志（0代表存在 1代表删除） */
    delFlag: string;
    /** 备注 */
    remark: string;
  }>;

  /** points wallet search params */
  type PointsWalletSearchParams = CommonType.RecordNullable<
    Pick<
      Api.Points.PointsWallet,
      | 'userId'
      | 'balance'
      | 'frozenAmount'
      | 'totalRecharge'
      | 'totalConsume'
      | 'pointsBalance'
      | 'totalPoints'
      | 'usedPoints'
      | 'payPassword'
      | 'isPayPwdSet'
      | 'status'
      | 'lastTradeTime'
    > &
    Api.Common.CommonSearchParams
  >;

  /** points wallet operate params */
  type PointsWalletOperateParams = CommonType.RecordNullable<
    Pick<
      Api.Points.PointsWallet,
      | 'id'
      | 'userId'
      | 'balance'
      | 'frozenAmount'
      | 'totalRecharge'
      | 'totalConsume'
      | 'pointsBalance'
      | 'totalPoints'
      | 'usedPoints'
      | 'payPassword'
      | 'isPayPwdSet'
      | 'status'
      | 'lastTradeTime'
      | 'remark'
    >
  >;

  /** points wallet list */
  type PointsWalletList = Api.Common.PaginatingQueryRecord<PointsWallet>;
}
