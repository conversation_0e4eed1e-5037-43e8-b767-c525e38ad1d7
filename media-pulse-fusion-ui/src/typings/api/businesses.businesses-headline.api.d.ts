/**
 * namespace Businesses
 *
 * backend api module: "Businesses"
 */
namespace Businesses {
  /** businesses headline */
  type BusinessesHeadline = Common.CommonRecord<{
    /** 主键ID */
    id: CommonType.IdType;
    /** 文案标题 */
    title: string;
    /** 文案内容 */
    content: string;
    /** 状态（0-草稿，1-发布，2-下架） */
    status: number;
    /** 租户ID，标记数据所属租户，可通过配置关闭 */
    tenantId: CommonType.IdType;
    /** 删除标记(0-正常,1-已删除)，逻辑删除字段 */
    delFlag: number;
  }>;

  /** businesses headline search params */
  type BusinessesHeadlineSearchParams = CommonType.RecordNullable<
    Pick<
      Api.Businesses.BusinessesHeadline,
      | 'title'
      | 'content'
      | 'status'
    > &
    Api.Common.CommonSearchParams
  >;

  /** businesses headline operate params */
  type BusinessesHeadlineOperateParams = CommonType.RecordNullable<
    Pick<
      Api.Businesses.BusinessesHeadline,
      | 'id'
      | 'title'
      | 'content'
      | 'status'
    >
  >;

  /** businesses headline list */
  type BusinessesHeadlineList = Api.Common.PaginatingQueryRecord<BusinessesHeadline>;

  // 新增的类型定义
  /** 请求生成文案参数 */
  type HeadLineRequest = {
    /** 标题 */
    title: string;
    /** 生成数量 */
    count: number;
  };

  /** 文案响应结果 */
  type HeadlineResponse = {
    /** 生成的文案列表 */
    comments: string[];
    /** 总数量 */
    totalCount: number;
  };

  /** 绑定文案到店铺参数 */
  type StoreHeadLineBindRequest = {
    /** 店铺ID */
    storeId: number;
    /** 文案ID */
    headLineId: number;
  };
}
