/**
 * namespace Points
 *
 * backend api module: "Points"
 */
namespace Points {
    /** points wallet record */
    type PointsWalletRecord = Common.CommonRecord<{
        /** 主键ID */
        id: CommonType.IdType; 
        /** 租户编号 */
        tenantId: CommonType.IdType; 
        /** 用户ID */
        userId: CommonType.IdType; 
        /** 钱包ID */
        walletId: CommonType.IdType; 
        /** 交易流水号 */
        tradeNo: string; 
        /** 交易类型（1收入 2支出） */
        tradeType: string; 
        /** 交易原因（recharge充值 consume消费 refund退款 transfer转账 withdraw提现） */
        tradeReason: string; 
        /** 交易金额 */
        amount: number; 
        /** 交易前余额 */
        balanceBefore: number; 
        /** 交易后余额 */
        balanceAfter: number; 
        /** 关联ID */
        relatedId: CommonType.IdType; 
        /** 关联类型 */
        relatedType: string; 
        /** 支付方式 */
        paymentMethod: string; 
        /** 支付流水号 */
        paymentNo: string; 
        /** 描述说明 */
        description: string; 
        /** 客户端IP */
        clientIp: string; 
        /** 删除标志（0代表存在 1代表删除） */
        delFlag: string; 
        /** 备注 */
        remark: string; 
    }>;

    /** points wallet record search params */
    type PointsWalletRecordSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Points.PointsWalletRecord,
        | 'userId'
        | 'walletId'
        | 'tradeNo'
        | 'tradeType'
        | 'tradeReason'
        | 'amount'
        | 'balanceBefore'
        | 'balanceAfter'
        | 'relatedId'
        | 'relatedType'
        | 'paymentMethod'
        | 'paymentNo'
        | 'description'
        | 'clientIp'
      > &
        Api.Common.CommonSearchParams
    >;

    /** points wallet record operate params */
    type PointsWalletRecordOperateParams = CommonType.RecordNullable<
      Pick<
        Api.Points.PointsWalletRecord,
        | 'id'
        | 'userId'
        | 'walletId'
        | 'tradeNo'
        | 'tradeType'
        | 'tradeReason'
        | 'amount'
        | 'balanceBefore'
        | 'balanceAfter'
        | 'relatedId'
        | 'relatedType'
        | 'paymentMethod'
        | 'paymentNo'
        | 'description'
        | 'clientIp'
        | 'remark'
      >
    >;

    /** points wallet record list */
    type PointsWalletRecordList = Api.Common.PaginatingQueryRecord<PointsWalletRecord>;
}
