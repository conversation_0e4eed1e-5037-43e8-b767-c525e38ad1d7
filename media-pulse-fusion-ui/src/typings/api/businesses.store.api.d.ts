/**
 * namespace Businesses
 *
 * backend api module: "Businesses"
 */
namespace Businesses {
  /** store */
  type Store = Common.CommonRecord<{
    /** 主键ID */
    id: CommonType.IdType;
    /** 所属商户ID（无外键约束） */
    merchantId: CommonType.IdType;
    /** 店铺名称 */
    storeName: string;
    /** 店铺电话 */
    storePhone: string;
    /** 店铺地址 */
    storeAddress: string;
    /** 租户ID，标记数据所属租户，可通过配置关闭 */
    tenantId: CommonType.IdType;
    /** 删除标记(0-正常,1-已删除)，逻辑删除字段 */
    delFlag: number;
  }>;

  /** store search params */
  type StoreSearchParams = CommonType.RecordNullable<
    Pick<
      Api.Businesses.Store,
      | 'merchantId'
      | 'storeName'
      | 'storePhone'
      | 'storeAddress'
    > &
    Api.Common.CommonSearchParams
  >;

  /** store operate params */
  type StoreOperateParams = CommonType.RecordNullable<
    Pick<
      Api.Businesses.Store,
      | 'id'
      | 'merchantId'
      | 'storeName'
      | 'storePhone'
      | 'storeAddress'
    >
  >;

  /** store list */
  type StoreList = Api.Common.PaginatingQueryRecord<Store>;
}
