/**
 * namespace Points
 *
 * backend api module: "Points"
 */
namespace Points {
  /** points package */
  type PointsPackage = Common.CommonRecord<{
    /** 主键ID */
    id: CommonType.IdType;
    /** 租户编号 */
    tenantId: CommonType.IdType;
    /** 套餐名称 */
    packageName: string;
    /** 套餐描述 */
    packageDesc: string;
    /** 积分数量 */
    pointsAmount: number;
    /** 原价 */
    originalPrice: number;
    /** 售价 */
    salePrice: number;
    /** 折扣率(%) */
    discountRate: number;
    /** 赠送积分 */
    bonusPoints: number;
    /** 套餐类型（1普通套餐 2限时优惠 3新用户专享） */
    packageType: string;
    /** 是否热门（0否 1是） */
    isHot: string;
    /** 显示顺序 */
    sortOrder: number;
    /** 生效时间 */
    startTime: string;
    /** 失效时间 */
    endTime: string;
    /** 状态（0正常 1停用） */
    status: string;
    /** 删除标志（0代表存在 1代表删除） */
    delFlag: string;
    /** 备注 */
    remark: string;
  }>;

  /** points package search params */
  type PointsPackageSearchParams = CommonType.RecordNullable<
    Pick<
      Api.Points.PointsPackage,
      | 'packageName'
      | 'packageDesc'
      | 'pointsAmount'
      | 'originalPrice'
      | 'salePrice'
      | 'discountRate'
      | 'bonusPoints'
      | 'packageType'
      | 'isHot'
      | 'sortOrder'
      | 'startTime'
      | 'endTime'
      | 'status'
    > &
    Api.Common.CommonSearchParams
  >;

  /** points package operate params */
  type PointsPackageOperateParams = CommonType.RecordNullable<
    Pick<
      Api.Points.PointsPackage,
      | 'id'
      | 'packageName'
      | 'packageDesc'
      | 'pointsAmount'
      | 'originalPrice'
      | 'salePrice'
      | 'discountRate'
      | 'bonusPoints'
      | 'packageType'
      | 'isHot'
      | 'sortOrder'
      | 'startTime'
      | 'endTime'
      | 'status'
      | 'remark'
    >
  >;

  /** points package list */
  type PointsPackageList = Api.Common.PaginatingQueryRecord<PointsPackage>;
}
