/**
 * namespace Businesses
 *
 * backend api module: "Businesses"
 */
namespace Businesses {
    /** merchant */
    type Merchant = Common.CommonRecord<{
        /** 主键ID */
        id: CommonType.IdType; 
        /** 商户名称 */
        name: string; 
        /** 联系电话 */
        contactPhone: string; 
        /** 地址 */
        address: string; 
        /** 租户ID，标记数据所属租户，可通过配置关闭 */
        tenantId: CommonType.IdType; 
        /** 删除标记(0-正常,1-已删除)，逻辑删除字段 */
        delFlag: number; 
    }>;

    /** merchant search params */
    type MerchantSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Businesses.Merchant,
        | 'name'
        | 'contactPhone'
        | 'address'
      > &
        Api.Common.CommonSearchParams
    >;

    /** merchant operate params */
    type MerchantOperateParams = CommonType.RecordNullable<
      Pick<
        Api.Businesses.Merchant,
        | 'id'
        | 'name'
        | 'contactPhone'
        | 'address'
      >
    >;

    /** merchant list */
    type MerchantList = Api.Common.PaginatingQueryRecord<Merchant>;
}
