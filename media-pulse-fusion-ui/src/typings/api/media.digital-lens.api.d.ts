/**
 * namespace Media
 *
 * backend api module: "Media"
 */
export namespace Media {
  /** digital lens */
  type DigitalLens = Common.CommonRecord<{
    /** 主键ID */
    id: CommonType.IdType;
    /** 租户ID，标记数据所属租户，可通过配置关闭 */
    tenantId: CommonType.IdType;
    /** 删除标记(0-正常,1-已删除)，逻辑删除字段 */
    delFlag: number;
    /** 乐观锁版本号，用于并发控制 */
    version: number;
    /** 标题 */
    title: string;
    /** 人物视频URL */
    characterVideo: CommonType.IdType;
    /** 克隆音色URL */
    clonedVoice: string;
    /** 参考文本，用于语音合成参考 */
    referenceText: string;
    /** 目标文本，用于语音合成输出 */
    targetText: string;
    /** 其他素材URL */
    otherMaterials: string;
    /** 其他视频素材URL */
    otherVideoMaterials?: string;
    /** 其他音频素材URL */
    otherAudioMaterials?: string;
    /** 状态：0-待处理，1-成功，2-处理中，3-失败 */
    status?: number;
    /** 关联任务UUID */
    taskId?: string;
    /** 字幕位置 */
    subtitlePosition?: string;
  }>;

  /** digital lens search params */
  type DigitalLensSearchParams = CommonType.RecordNullable<
    Pick<
      Api.Media.DigitalLens,
      | 'title'
      | 'characterVideo'
      | 'clonedVoice'
      | 'referenceText'
      | 'targetText'
      | 'otherMaterials'
      | 'otherVideoMaterials'
      | 'otherAudioMaterials'
      | 'status'
      | 'taskId'
      | 'subtitlePosition'
    > &
    Api.Common.CommonSearchParams
  >;

  /** digital lens operate params */
  type DigitalLensOperateParams = CommonType.RecordNullable<
    Pick<
      Api.Media.DigitalLens,
      | 'id'
      | 'title'
      | 'characterVideo'
      | 'clonedVoice'
      | 'referenceText'
      | 'targetText'
      | 'otherMaterials'
      | 'otherVideoMaterials'
      | 'otherAudioMaterials'
      | 'status'
      | 'taskId'
      | 'subtitlePosition'
    >
  >;

  /** digital lens list */
  type DigitalLensList = Api.Common.PaginatingQueryRecord<DigitalLens>;
}
