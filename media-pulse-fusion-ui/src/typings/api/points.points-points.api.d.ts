/**
 * namespace Points
 *
 * backend api module: "Points"
 */
namespace Points {
    /** points points */
    type PointsPoints = Common.CommonRecord<{
        /** 主键ID */
        id: CommonType.IdType; 
        /** 租户编号 */
        tenantId: CommonType.IdType; 
        /** 用户ID */
        userId: CommonType.IdType; 
        /** 变动类型（1收入 2支出） */
        changeType: string; 
        /** 变动原因（purchase购买 consume消费 refund退款 gift赠送 expire过期 admin管理员操作） */
        changeReason: string; 
        /** 积分数量 */
        pointsAmount: number; 
        /** 变动前余额 */
        balanceBefore: number; 
        /** 变动后余额 */
        balanceAfter: number; 
        /** 关联ID（订单ID、消费记录ID等） */
        relatedId: CommonType.IdType; 
        /** 关联类型（order订单 consume消费 gift赠送） */
        relatedType: string; 
        /** 积分过期时间 */
        expireTime: string; 
        /** 描述说明 */
        description: string; 
        /** 删除标志（0代表存在 1代表删除） */
        delFlag: string; 
        /** 备注 */
        remark: string; 
    }>;

    /** points points search params */
    type PointsPointsSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Points.PointsPoints,
        | 'userId'
        | 'changeType'
        | 'changeReason'
        | 'pointsAmount'
        | 'balanceBefore'
        | 'balanceAfter'
        | 'relatedId'
        | 'relatedType'
        | 'expireTime'
        | 'description'
      > &
        Api.Common.CommonSearchParams
    >;

    /** points points operate params */
    type PointsPointsOperateParams = CommonType.RecordNullable<
      Pick<
        Api.Points.PointsPoints,
        | 'id'
        | 'userId'
        | 'changeType'
        | 'changeReason'
        | 'pointsAmount'
        | 'balanceBefore'
        | 'balanceAfter'
        | 'relatedId'
        | 'relatedType'
        | 'expireTime'
        | 'description'
        | 'remark'
      >
    >;

    /** points points list */
    type PointsPointsList = Api.Common.PaginatingQueryRecord<PointsPoints>;
}
