/**
 * namespace Points
 *
 * backend api module: "Points"
 */
namespace Points {
  /** points order */
  type PointsOrder = Common.CommonRecord<{
    /** 主键ID */
    id: CommonType.IdType;
    /** 租户编号 */
    tenantId: CommonType.IdType;
    /** 订单号 */
    orderNo: string;
    /** 用户ID */
    userId: CommonType.IdType;
    /** 套餐ID */
    packageId: CommonType.IdType;
    /** 套餐名称 */
    packageName: string;
    /** 购买积分数量 */
    pointsAmount: number;
    /** 赠送积分数量 */
    bonusPoints: number;
    /** 总积分数量 */
    totalPoints: number;
    /** 原价 */
    originalPrice: number;
    /** 实付金额 */
    payAmount: number;
    /** 优惠金额 */
    discountAmount: number;
    /** 支付方式（alipay支付宝 wechat微信 balance余额） */
    paymentMethod: string;
    /** 支付流水号 */
    paymentNo: string;
    /** 订单状态（0待支付 1已支付 2已取消 3已退款 4支付失败） */
    orderStatus: string;
    /** 支付时间 */
    payTime: string;
    /** 取消时间 */
    cancelTime: string;
    /** 退款时间 */
    refundTime: string;
    /** 订单过期时间 */
    expireTime: string;
    /** 客户端IP */
    clientIp: string;
    /** 用户代理 */
    userAgent: string;
    /** 删除标志（0代表存在 1代表删除） */
    delFlag: string;
    /** 备注 */
    remark: string;
  }>;

  /** points order search params */
  type PointsOrderSearchParams = CommonType.RecordNullable<
    Pick<
      Api.Points.PointsOrder,
      | 'orderNo'
      | 'userId'
      | 'packageId'
      | 'packageName'
      | 'pointsAmount'
      | 'bonusPoints'
      | 'totalPoints'
      | 'originalPrice'
      | 'payAmount'
      | 'discountAmount'
      | 'paymentMethod'
      | 'paymentNo'
      | 'orderStatus'
      | 'payTime'
      | 'cancelTime'
      | 'refundTime'
      | 'expireTime'
      | 'clientIp'
      | 'userAgent'
    > &
    Api.Common.CommonSearchParams
  >;

  /** points order operate params */
  type PointsOrderOperateParams = CommonType.RecordNullable<
    Pick<
      Api.Points.PointsOrder,
      | 'id'
      | 'orderNo'
      | 'userId'
      | 'packageId'
      | 'packageName'
      | 'pointsAmount'
      | 'bonusPoints'
      | 'totalPoints'
      | 'originalPrice'
      | 'payAmount'
      | 'discountAmount'
      | 'paymentMethod'
      | 'paymentNo'
      | 'orderStatus'
      | 'payTime'
      | 'cancelTime'
      | 'refundTime'
      | 'expireTime'
      | 'clientIp'
      | 'userAgent'
      | 'remark'
    >
  >;

  /** points order list */
  type PointsOrderList = Api.Common.PaginatingQueryRecord<PointsOrder>;
}
