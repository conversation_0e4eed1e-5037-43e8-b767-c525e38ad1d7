/**
 * 字幕坐标和字体大小转换工具
 * 用于解决前后端坐标系统和字体大小不匹配的问题
 */

// 标准视频尺寸（后端处理基准）
export const STANDARD_VIDEO_WIDTH = 1920;
export const STANDARD_VIDEO_HEIGHT = 1080;

// 默认预览容器尺寸
export const DEFAULT_PREVIEW_WIDTH = 640;
export const DEFAULT_PREVIEW_HEIGHT = 360;

/**
 * 视频信息接口
 */
export interface VideoInfo {
  /** 视频宽度 */
  width: number;
  /** 视频高度 */
  height: number;
  /** 宽高比 */
  aspectRatio: number;
  /** 视频方向：landscape(横屏), portrait(竖屏), square(方形) */
  orientation: string;
}

/**
 * 坐标转换配置
 */
export interface CoordinateConfig {
  /** 标准视频宽度 */
  standardVideoWidth: number;
  /** 标准视频高度 */
  standardVideoHeight: number;
  /** 预览容器宽度 */
  previewWidth: number;
  /** 预览容器高度 */
  previewHeight: number;
  /** 视频信息（可选） */
  videoInfo?: VideoInfo;
}

/**
 * 字幕样式配置
 */
export interface SubtitleStyleConfig {
  /** 字体名称 */
  fontName: string;
  /** 字体大小（pt） */
  fontSize: number;
  /** 字体样式 */
  fontStyle: string;
  /** 字体粗细 */
  fontWeight: string;
  /** 字体颜色RGB */
  r: number;
  g: number;
  b: number;
  /** 字幕文本 */
  text: string;
}

/**
 * 坐标转换器类
 */
export class SubtitleCoordinateConverter {
  private config: CoordinateConfig;

  constructor(config: CoordinateConfig) {
    this.config = config;
  }

  /**
   * 获取缩放比例
   */
  getScaleRatio() {
    const scaleX = this.config.standardVideoWidth / this.config.previewWidth;
    const scaleY = this.config.standardVideoHeight / this.config.previewHeight;
    return { scaleX, scaleY };
  }

  /**
   * 百分比坐标转换为像素坐标（用于后端）
   * @param percentX 百分比X坐标 (0-100)
   * @param percentY 百分比Y坐标 (0-100)
   * @returns 像素坐标
   */
  percentToPixel(percentX: number, percentY: number) {
    // 如果有视频信息，使用实际视频尺寸；否则使用标准尺寸
    if (this.config.videoInfo) {
      const videoInfo = this.config.videoInfo;
      let baseWidth, baseHeight;

      if (videoInfo.orientation === 'landscape') {
        // 横屏：使用1920x1080基准
        baseWidth = 1920;
        baseHeight = 1080;
      } else if (videoInfo.orientation === 'portrait') {
        // 竖屏：使用1080x1920基准
        baseWidth = 1080;
        baseHeight = 1920;
      } else {
        // 方形：使用1920x1920基准
        baseWidth = 1920;
        baseHeight = 1920;
      }

      return {
        x: Math.round((percentX / 100) * baseWidth),
        y: Math.round((percentY / 100) * baseHeight)
      };
    }

    // 回退到标准尺寸
    return {
      x: Math.round((percentX / 100) * this.config.standardVideoWidth),
      y: Math.round((percentY / 100) * this.config.standardVideoHeight)
    };
  }

  /**
   * 像素坐标转换为百分比坐标（用于前端显示）
   * @param pixelX 像素X坐标
   * @param pixelY 像素Y坐标
   * @returns 百分比坐标
   */
  pixelToPercent(pixelX: number, pixelY: number) {
    // 如果有视频信息，使用对应的基准尺寸
    if (this.config.videoInfo) {
      const videoInfo = this.config.videoInfo;
      let baseWidth, baseHeight;

      if (videoInfo.orientation === 'landscape') {
        baseWidth = 1920;
        baseHeight = 1080;
      } else if (videoInfo.orientation === 'portrait') {
        baseWidth = 1080;
        baseHeight = 1920;
      } else {
        baseWidth = 1920;
        baseHeight = 1920;
      }

      return {
        x: (pixelX / baseWidth) * 100,
        y: (pixelY / baseHeight) * 100
      };
    }

    // 回退到标准尺寸
    return {
      x: (pixelX / this.config.standardVideoWidth) * 100,
      y: (pixelY / this.config.standardVideoHeight) * 100
    };
  }

  /**
   * 前端字体大小转换为后端字体大小
   * @param frontendFontSize 前端字体大小（pt）
   * @returns 后端字体大小（pt）
   */
  getFontSizeForBackend(frontendFontSize: number): number {
    const { scaleX, scaleY } = this.getScaleRatio();
    // 使用较大的缩放比例，确保字体在后端渲染时足够清晰
    const fontSizeRatio = Math.max(scaleX, scaleY);

    // 应用缩放比例，但设置合理的范围
    const scaledSize = frontendFontSize * fontSizeRatio;

    // 限制字体大小范围：最小12pt，最大200pt
    return Math.max(12, Math.min(200, Math.round(scaledSize)));
  }

  /**
   * 后端字体大小转换为前端显示字体大小
   * @param backendFontSize 后端字体大小（pt）
   * @returns 前端显示字体大小（px）
   */
  getFontSizeForFrontend(backendFontSize: number): number {
    // 简化逻辑：前端直接显示用户设置的字体大小
    // pt和px在屏幕显示中基本等价（1pt ≈ 1.33px，但在预览中可以直接使用）
    const frontendSize = backendFontSize;

    // 限制字体大小范围：最小8px，最大100px
    return Math.max(8, Math.min(100, Math.round(frontendSize)));
  }

  /**
   * 更新配置
   * @param newConfig 新的配置
   */
  updateConfig(newConfig: Partial<CoordinateConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): CoordinateConfig {
    return { ...this.config };
  }

  /**
   * 验证坐标是否在有效范围内
   * @param x X坐标（百分比）
   * @param y Y坐标（百分比）
   * @returns 是否有效
   */
  isValidCoordinate(x: number, y: number): boolean {
    return x >= 0 && x <= 100 && y >= 0 && y <= 100;
  }

  /**
   * 限制坐标在有效范围内
   * @param x X坐标（百分比）
   * @param y Y坐标（百分比）
   * @returns 限制后的坐标
   */
  clampCoordinate(x: number, y: number) {
    return {
      x: Math.max(0, Math.min(100, x)),
      y: Math.max(0, Math.min(100, y))
    };
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    const { scaleX, scaleY } = this.getScaleRatio();
    return {
      config: this.config,
      scaleRatio: { scaleX, scaleY },
      aspectRatio: {
        standard: this.config.standardVideoWidth / this.config.standardVideoHeight,
        preview: this.config.previewWidth / this.config.previewHeight
      }
    };
  }
}

/**
 * 创建默认的坐标转换器
 * @param previewWidth 预览容器宽度
 * @param previewHeight 预览容器高度
 * @returns 坐标转换器实例
 */
export function createSubtitleConverter(
  previewWidth: number = DEFAULT_PREVIEW_WIDTH,
  previewHeight: number = DEFAULT_PREVIEW_HEIGHT
): SubtitleCoordinateConverter {
  return new SubtitleCoordinateConverter({
    standardVideoWidth: STANDARD_VIDEO_WIDTH,
    standardVideoHeight: STANDARD_VIDEO_HEIGHT,
    previewWidth,
    previewHeight
  });
}

/**
 * 创建带视频信息的坐标转换器
 * @param previewWidth 预览容器宽度
 * @param previewHeight 预览容器高度
 * @param videoInfo 视频信息
 * @returns 坐标转换器实例
 */
export function createSubtitleConverterWithVideoInfo(
  previewWidth: number,
  previewHeight: number,
  videoInfo: VideoInfo
): SubtitleCoordinateConverter {
  return new SubtitleCoordinateConverter({
    standardVideoWidth: STANDARD_VIDEO_WIDTH,
    standardVideoHeight: STANDARD_VIDEO_HEIGHT,
    previewWidth,
    previewHeight,
    videoInfo
  });
}

/**
 * 根据视频信息计算推荐的预览容器尺寸
 * @param videoInfo 视频信息
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @returns 推荐的预览容器尺寸
 */
export function calculateRecommendedPreviewSize(
  videoInfo: VideoInfo,
  maxWidth: number = 640,
  maxHeight: number = 480
) {
  const aspectRatio = videoInfo.aspectRatio;

  let width, height;

  if (aspectRatio > 1) {
    // 横屏视频
    width = Math.min(maxWidth, maxHeight * aspectRatio);
    height = width / aspectRatio;
  } else {
    // 竖屏或方形视频
    height = Math.min(maxHeight, maxWidth / aspectRatio);
    width = height * aspectRatio;
  }

  return {
    width: Math.round(width),
    height: Math.round(height)
  };
}

/**
 * 字体大小预设
 */
export const FONT_SIZE_PRESETS = {
  small: 14,
  medium: 18,
  large: 24,
  xlarge: 32,
  xxlarge: 48
} as const;

/**
 * 常用字体列表
 */
export const COMMON_FONTS = [
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Courier New',
  'Verdana',
  'Georgia',
  'Comic Sans MS',
  'Impact',
  'Trebuchet MS',
  'Arial Black'
] as const;

/**
 * 字体样式选项
 */
export const FONT_STYLE_OPTIONS = [
  { label: '正常', value: 'normal' },
  { label: '粗体', value: 'bold' },
  { label: '斜体', value: 'italic' }
] as const;
