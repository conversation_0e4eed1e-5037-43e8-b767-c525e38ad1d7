First of all, thank you for your contribution! 😄

Please send pull request to dev branch. Pull request will be merged after one of collaborators approve. Please makes sure that these form are filled before submitting your pull request, thank you!

[[中文版模板 / Chinese template](./pr_cn.md)]

### This is a ...

- [ ] New feature
- [ ] Bug fix
- [ ] Site / document update
- [ ] Component style update
- [ ] TypeScript definition update
- [ ] Refactoring
- [ ] Code style optimization
- [ ] Branch merge
- [ ] Other (about what?)

### What's the background?

> 1. Describe the source of requirement.
> 2. Resolve what problem.
> 3. Related issue link.

### API Realization (Optional if not new feature)

> 1. Basic thought of solution and other optional proposal.
> 2. List final API realization and usage sample.
> 3. GIF or snapshot should be provided if includes UI/interactive modification.

### What's the effect? (Optional if not new feature)

> 1. Does this PR affect user? Which part will be affected?
> 2. What will say in changelog?
> 3. Does this PR contains potential break change or other risk?

### Changelog description (Optional if not new feature)

> 1. English description
> 2. Chinese description (optional)

### Self Check before Merge

- [ ] Doc is updated/provided or not needed
- [ ] Demo is updated/provided or not needed
- [ ] TypeScript definition is updated/provided or not needed
- [ ] Changelog is provided or not needed

### Additional Plan? (Optional if not new feature)

> If this PR related with other PR or following info. You can type here.
