mvn clean package -D maven.test.skip=true -P prod


docker-compose up -d mysql nginx-web redis minio

docker-compose up -d ruoyi-monitor-admin ruoyi-snailjob-server ruoyi-server1 ruoyi-server2


cd media-pulse-fusion-ui/packages/tinymce
pnpm --filter @sa/tinymce run build

cd media-pulse-fusion-ui

pnpm run build:prod
或
vite build --mode prod


先进入 media-pulse-fusion-ui/src/service/api/media 目录下
更改 task.ts，digital-lens.ts
上传服务器将dev-api改成prod-api
